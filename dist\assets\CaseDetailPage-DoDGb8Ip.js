import{a as E,g as r,S as I,r as d,c as f,b as V,y as P,o as F,e as L,w as z,f as G,h as s,i as o,j as n,t as a,s as O,F as u,k as v,l as w,n as Q,m as l,p as W,_ as $}from"./index-Dz-_Smvi.js";import{u as H,a as J,A as K}from"./AppLayout-DAG3tKFc.js";import{B as U}from"./Breadcrumb-DX9d_gU2.js";/* empty css                                                                            */import"./contact-sLg3zrn9.js";const X={class:"container"},Y={class:"hero-layout"},Z={class:"case-info"},ss={class:"case-meta"},ts={class:"case-industry"},es={class:"case-badges"},is={key:0,class:"badge badge--new"},as={key:1,class:"badge badge--featured"},ls={key:2,class:"badge badge--award"},os={class:"case-title"},cs={class:"case-description"},ns={class:"project-info"},rs={class:"info-item"},ds={class:"info-value"},us={class:"info-item"},vs={class:"info-value"},_s={class:"info-item"},ms={class:"info-value"},gs={key:0,class:"case-technologies"},hs={class:"tech-tags"},fs={class:"case-visual"},ps={class:"case-image"},bs=["src","alt"],ys={class:"container"},ks={class:"metrics-grid"},ws={class:"metric-icon"},Cs={class:"metric-content"},Ss={class:"metric-value"},Rs={class:"metric-label"},xs={class:"container"},Ms={class:"details-layout"},Ns={class:"details-main"},Ts={class:"detail-section"},js={class:"section-content"},qs={class:"detail-section"},Bs={class:"section-content"},As={key:0,class:"project-results"},Ds={class:"results-list"},Es={key:0,class:"detail-section"},Is={class:"testimonial-card"},Vs={class:"testimonial-content"},Ps={class:"testimonial-quote"},Fs={class:"testimonial-author"},Ls={class:"author-info"},zs={class:"author-name"},Gs={class:"author-position"},Os={class:"testimonial-rating"},Qs={class:"details-sidebar"},Ws={class:"sidebar-card"},$s={class:"related-cases"},Hs=["href"],Js={class:"case-thumb"},Ks=["src","alt"],Us={class:"case-info"},Xs={class:"case-name"},Ys={class:"case-client"},Zs=E({__name:"CaseDetailPage",setup(st){r.registerPlugin(I);const C=V(),{navigateToContact:p}=H(),m=d(),g=d(),h=d(),b=d([]);f(()=>C.params.id);const e=d({id:"1",title:"智能手机包装设计革新",description:"为知名手机品牌设计的全新包装系列，融合科技感与环保理念，提升了品牌形象和用户体验。通过创新的设计理念和先进的制作工艺，我们成功地为客户打造了一套完整的包装解决方案。",client:"科技公司A",industry:"电子科技",date:"2024-01-15",image:"/images/case1.svg",featured:!0,isNew:!0,results:["包装成本降低30%","品牌认知度提升50%","客户满意度达到95%","环保评分提升40%","生产效率提升25%","市场反馈积极"],metrics:[{label:"成本节省",value:"30%"},{label:"满意度",value:"95%"},{label:"环保提升",value:"40%"},{label:"效率提升",value:"25%"}],technologies:["环保材料","智能包装","防伪技术","可回收设计"],testimonial:{content:"新的包装设计不仅降低了成本，还大大提升了我们的品牌形象。客户反馈非常积极，销量也有了显著提升。",author:"张总",position:"CEO"}}),S=f(()=>"客户面临的主要挑战包括：现有包装成本过高、环保性能不足、品牌识别度较低、用户体验有待改善。同时，还需要在保证产品保护性能的前提下，实现包装的轻量化和可持续发展目标。"),R=f(()=>"我们采用了创新的设计理念和先进的材料技术，通过深入的市场调研和用户分析，设计出了既美观又实用的包装方案。使用环保材料，优化结构设计，融入智能元素，最终实现了成本降低、环保提升、品牌价值增强的多重目标。"),_=P([]),x=d([{id:"2",title:"有机食品包装安全升级",client:"绿色食品公司",image:"/images/case2.svg"},{id:"3",title:"奢侈品包装定制项目",client:"奢侈品牌C",image:"/images/case3.svg"}]),M=c=>({成本节省:"icon-cost",满意度:"icon-satisfaction",环保提升:"icon-eco",效率提升:"icon-efficiency"})[c]||"icon-metric",N=c=>c.replace(/\d+/,""),T=(c,t)=>{c&&(b.value[t]=c)},y=()=>{p()},j=()=>{p()},q=()=>{console.log("Image load error")},B=()=>{e.value?.metrics&&e.value.metrics.forEach((c,t)=>{const i=parseInt(c.value.replace(/\D/g,""));r.to(_,{[t]:i,duration:2,ease:"power2.out",scrollTrigger:{trigger:g.value,start:"top 80%",once:!0}})})},A=async()=>{await Q(),r.from(m.value?.querySelector(".case-info")?.children||[],{y:50,opacity:0,duration:.8,stagger:.2,ease:"power2.out"}),r.from(m.value?.querySelector(".case-visual"),{x:50,opacity:0,duration:1,ease:"power2.out",delay:.3}),r.from(b.value,{y:60,opacity:0,duration:.8,stagger:.2,ease:"power2.out",scrollTrigger:{trigger:g.value,start:"top 80%",once:!0}}),r.from(h.value?.querySelector(".details-main")?.children||[],{y:40,opacity:0,duration:.8,stagger:.1,ease:"power2.out",scrollTrigger:{trigger:h.value,start:"top 80%",once:!0}}),B()};return F(()=>{e.value?.metrics&&_.splice(0,_.length,...new Array(e.value.metrics.length).fill(0)),A()}),(c,t)=>(l(),L(K,null,{default:z(()=>[G(U),s("section",{class:"case-detail-hero",ref_key:"heroRef",ref:m},[s("div",X,[s("div",Y,[s("div",Z,[s("div",ss,[s("span",ts,a(e.value?.industry),1),s("div",es,[e.value?.isNew?(l(),o("span",is,"最新")):n("",!0),e.value?.featured?(l(),o("span",as,"精选")):n("",!0),e.value?.award?(l(),o("span",ls,"获奖")):n("",!0)])]),s("h1",os,a(e.value?.title),1),s("p",cs,a(e.value?.description),1),s("div",ns,[s("div",rs,[t[0]||(t[0]=s("div",{class:"info-label"},"客户",-1)),s("div",ds,a(e.value?.client),1)]),s("div",us,[t[1]||(t[1]=s("div",{class:"info-label"},"行业",-1)),s("div",vs,a(e.value?.industry),1)]),s("div",_s,[t[2]||(t[2]=s("div",{class:"info-label"},"完成时间",-1)),s("div",ms,a(O(J)(e.value?.date)),1)])]),e.value?.technologies?(l(),o("div",gs,[t[3]||(t[3]=s("h3",{class:"tech-title"},"使用技术",-1)),s("div",hs,[(l(!0),o(u,null,v(e.value.technologies,i=>(l(),o("span",{key:i,class:"tech-tag"},a(i),1))),128))])])):n("",!0),s("div",{class:"case-actions"},[s("button",{class:"btn btn--secondary btn--lg",onClick:y},t[4]||(t[4]=[w(" 咨询类似项目 ",-1),s("i",{class:"icon-phone"},null,-1)])),s("button",{class:"btn btn--primary btn--lg",onClick:j},t[5]||(t[5]=[w(" 获取报价 ",-1),s("i",{class:"icon-quote"},null,-1)]))])]),s("div",fs,[s("div",ps,[s("img",{src:e.value?.image||"/images/case-default.svg",alt:e.value?.title,onError:q},null,40,bs)])])])])],512),e.value?.metrics?(l(),o("section",{key:0,class:"case-metrics",ref_key:"metricsRef",ref:g},[s("div",ys,[t[6]||(t[6]=s("div",{class:"section-header"},[s("h2",{class:"section-title"},"项目成果"),s("p",{class:"section-subtitle"},"数据说话，成果显著")],-1)),s("div",ks,[(l(!0),o(u,null,v(e.value.metrics,(i,k)=>(l(),o("div",{key:i.label,class:"metric-card",ref_for:!0,ref:D=>T(D,k)},[s("div",ws,[s("i",{class:W(M(i.label))},null,2)]),s("div",Cs,[s("div",Ss,a(_[k]||0)+a(N(i.value)),1),s("div",Rs,a(i.label),1)])]))),128))])])],512)):n("",!0),s("section",{class:"case-details",ref_key:"detailsRef",ref:h},[s("div",xs,[s("div",Ms,[s("div",Ns,[s("div",Ts,[t[7]||(t[7]=s("h2",{class:"section-title"},"项目挑战",-1)),s("div",js,[s("p",null,a(S.value),1)])]),s("div",qs,[t[10]||(t[10]=s("h2",{class:"section-title"},"解决方案",-1)),s("div",Bs,[s("p",null,a(R.value),1),e.value?.results?(l(),o("div",As,[t[9]||(t[9]=s("h3",{class:"results-title"},"具体成果",-1)),s("ul",Ds,[(l(!0),o(u,null,v(e.value.results,i=>(l(),o("li",{key:i,class:"result-item"},[t[8]||(t[8]=s("i",{class:"icon-check"},null,-1)),s("span",null,a(i),1)]))),128))])])):n("",!0)])]),e.value?.testimonial?(l(),o("div",Es,[t[11]||(t[11]=s("h2",{class:"section-title"},"客户评价",-1)),s("div",Is,[s("div",Vs,[s("blockquote",Ps,' "'+a(e.value.testimonial.content)+'" ',1),s("div",Fs,[s("div",Ls,[s("div",zs,a(e.value.testimonial.author),1),s("div",Gs,a(e.value.testimonial.position),1)]),s("div",Os,[(l(),o(u,null,v(5,i=>s("i",{key:i,class:"star filled"})),64))])])])])])):n("",!0)]),s("div",Qs,[t[16]||(t[16]=s("div",{class:"sidebar-card"},[s("h3",{class:"card-title"},"项目信息"),s("div",{class:"project-details"},[s("div",{class:"detail-item"},[s("div",{class:"detail-label"},"项目类型"),s("div",{class:"detail-value"},"包装设计")]),s("div",{class:"detail-item"},[s("div",{class:"detail-label"},"服务范围"),s("div",{class:"detail-value"},"设计 + 生产")]),s("div",{class:"detail-item"},[s("div",{class:"detail-label"},"团队规模"),s("div",{class:"detail-value"},"8人")]),s("div",{class:"detail-item"},[s("div",{class:"detail-label"},"项目状态"),s("div",{class:"detail-value status-completed"},"已完成")])])],-1)),s("div",Ws,[t[13]||(t[13]=s("h3",{class:"card-title"},"相关案例",-1)),s("div",$s,[(l(!0),o(u,null,v(x.value,i=>(l(),o("a",{key:i.id,href:`/cases/${i.id}`,class:"related-case-item"},[s("div",Js,[s("img",{src:i.image,alt:i.title},null,8,Ks)]),s("div",Us,[s("div",Xs,a(i.title),1),s("div",Ys,a(i.client),1)]),t[12]||(t[12]=s("i",{class:"icon-arrow-right"},null,-1))],8,Hs))),128))])]),s("div",{class:"sidebar-card"},[t[15]||(t[15]=s("h3",{class:"card-title"},"项目咨询",-1)),s("div",{class:"contact-info"},[t[14]||(t[14]=s("p",{class:"contact-text"}," 对这个项目感兴趣？我们可以为您提供类似的解决方案。 ",-1)),s("button",{class:"btn btn--primary btn--block",onClick:y}," 立即咨询 ")])])])])])],512)]),_:1}))}}),ot=$(Zs,[["__scopeId","data-v-a6bc153d"]]);export{ot as default};
