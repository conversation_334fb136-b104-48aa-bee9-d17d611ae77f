import{a as n,i as s,h as o,f as a,w as r,l,z as d,m as _,_ as i}from"./index-Dz-_Smvi.js";const p={class:"not-found-page"},u={class:"container"},m={class:"content"},c=n({__name:"NotFoundPage",setup(f){return(v,t)=>{const e=d("router-link");return _(),s("div",p,[o("div",u,[o("div",m,[t[1]||(t[1]=o("h1",null,"404",-1)),t[2]||(t[2]=o("h2",null,"页面未找到",-1)),t[3]||(t[3]=o("p",null,"抱歉，您访问的页面不存在或已被移除。",-1)),a(e,{to:"/",class:"btn btn--primary"},{default:r(()=>t[0]||(t[0]=[l(" 返回首页 ",-1)])),_:1,__:[0]})])])])}}}),N=i(c,[["__scopeId","data-v-16173a38"]]);export{N as default};
