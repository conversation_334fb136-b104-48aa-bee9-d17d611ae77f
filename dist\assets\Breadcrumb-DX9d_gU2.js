import{u as _}from"./AppLayout-DAG3tKFc.js";import{a as d,i as t,j as n,s,h as c,F as m,k as u,z as p,m as e,p as b,e as h,w as k,l as f,t as l,_ as g}from"./index-Dz-_Smvi.js";const B={key:0,class:"breadcrumb","aria-label":"面包屑导航"},v={class:"container"},y={class:"breadcrumb-list"},C={key:1,class:"breadcrumb-current"},N={key:2,class:"breadcrumb-separator","aria-hidden":"true"},x=d({__name:"Breadcrumb",setup(V){const{breadcrumbs:a}=_();return(w,z)=>{const i=p("router-link");return s(a).length>1?(e(),t("nav",B,[c("div",v,[c("ol",y,[(e(!0),t(m,null,u(s(a),(r,o)=>(e(),t("li",{key:r.path,class:b(["breadcrumb-item",{active:o===s(a).length-1}])},[o<s(a).length-1?(e(),h(i,{key:0,to:r.path,class:"breadcrumb-link"},{default:k(()=>[f(l(r.label),1)]),_:2},1032,["to"])):(e(),t("span",C,l(r.label),1)),o<s(a).length-1?(e(),t("i",N)):n("",!0)],2))),128))])])])):n("",!0)}}}),D=g(x,[["__scopeId","data-v-e796bfc5"]]);export{D as B};
