<template>
  <div class="micro-interactions">
    <!-- 磁性按钮效果 -->
    <div
      v-if="type === 'magnetic'"
      ref="magneticRef"
      class="magnetic-element"
      @mousemove="handleMagneticMove"
      @mouseleave="handleMagneticLeave"
    >
      <slot />
    </div>

    <!-- 粒子效果按钮 -->
    <div
      v-else-if="type === 'particles'"
      ref="particlesRef"
      class="particles-element"
      @click="createParticles"
    >
      <slot />
      <canvas
        ref="particlesCanvas"
        class="particles-canvas"
        :width="canvasSize.width"
        :height="canvasSize.height"
      ></canvas>
    </div>

    <!-- 波纹效果 -->
    <div
      v-else-if="type === 'ripple'"
      ref="rippleRef"
      class="ripple-element"
      @click="createRipple"
    >
      <slot />
    </div>

    <!-- 悬停发光效果 -->
    <div
      v-else-if="type === 'glow'"
      ref="glowRef"
      class="glow-element"
      @mousemove="handleGlowMove"
      @mouseleave="handleGlowLeave"
    >
      <slot />
      <div class="glow-overlay" ref="glowOverlay"></div>
    </div>

    <!-- 3D 倾斜效果 -->
    <div
      v-else-if="type === 'tilt'"
      ref="tiltRef"
      class="tilt-element"
      @mousemove="handleTiltMove"
      @mouseleave="handleTiltLeave"
    >
      <slot />
    </div>

    <!-- 默认包装器 -->
    <div v-else>
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { gsap } from 'gsap'

interface Props {
  type: 'magnetic' | 'particles' | 'ripple' | 'glow' | 'tilt'
  intensity?: number
  color?: string
  duration?: number
}

const props = withDefaults(defineProps<Props>(), {
  intensity: 1,
  color: '#ff6b35',
  duration: 0.3
})

// 模板引用
const magneticRef = ref<HTMLElement>()
const particlesRef = ref<HTMLElement>()
const particlesCanvas = ref<HTMLCanvasElement>()
const rippleRef = ref<HTMLElement>()
const glowRef = ref<HTMLElement>()
const glowOverlay = ref<HTMLElement>()
const tiltRef = ref<HTMLElement>()

// 状态
const canvasSize = reactive({ width: 200, height: 200 })
const particles = ref<any[]>([])
const animationFrame = ref<number>()

// 磁性效果
const handleMagneticMove = (event: MouseEvent) => {
  if (!magneticRef.value) return

  const rect = magneticRef.value.getBoundingClientRect()
  const centerX = rect.left + rect.width / 2
  const centerY = rect.top + rect.height / 2
  
  const deltaX = (event.clientX - centerX) * props.intensity * 0.3
  const deltaY = (event.clientY - centerY) * props.intensity * 0.3

  gsap.to(magneticRef.value, {
    x: deltaX,
    y: deltaY,
    duration: props.duration,
    ease: 'power2.out'
  })
}

const handleMagneticLeave = () => {
  if (!magneticRef.value) return

  gsap.to(magneticRef.value, {
    x: 0,
    y: 0,
    duration: props.duration * 1.5,
    ease: 'elastic.out(1, 0.3)'
  })
}

// 粒子效果
const createParticles = (event: MouseEvent) => {
  if (!particlesCanvas.value || !particlesRef.value) return

  const rect = particlesRef.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  const particleCount = 15 * props.intensity
  const newParticles = []

  for (let i = 0; i < particleCount; i++) {
    newParticles.push({
      x,
      y,
      vx: (Math.random() - 0.5) * 10 * props.intensity,
      vy: (Math.random() - 0.5) * 10 * props.intensity,
      life: 1,
      decay: 0.02,
      size: Math.random() * 4 + 2
    })
  }

  particles.value.push(...newParticles)
  animateParticles()
}

const animateParticles = () => {
  if (!particlesCanvas.value) return

  const ctx = particlesCanvas.value.getContext('2d')
  if (!ctx) return

  ctx.clearRect(0, 0, canvasSize.width, canvasSize.height)

  particles.value = particles.value.filter(particle => {
    particle.x += particle.vx
    particle.y += particle.vy
    particle.life -= particle.decay
    particle.vx *= 0.98
    particle.vy *= 0.98

    if (particle.life > 0) {
      ctx.globalAlpha = particle.life
      ctx.fillStyle = props.color
      ctx.beginPath()
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
      ctx.fill()
      return true
    }
    return false
  })

  if (particles.value.length > 0) {
    animationFrame.value = requestAnimationFrame(animateParticles)
  }
}

// 波纹效果
const createRipple = (event: MouseEvent) => {
  if (!rippleRef.value) return

  const rect = rippleRef.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  const ripple = document.createElement('div')
  ripple.className = 'ripple'
  ripple.style.left = `${x}px`
  ripple.style.top = `${y}px`
  ripple.style.backgroundColor = props.color

  rippleRef.value.appendChild(ripple)

  gsap.fromTo(ripple, 
    {
      scale: 0,
      opacity: 0.6
    },
    {
      scale: 4 * props.intensity,
      opacity: 0,
      duration: 0.6,
      ease: 'power2.out',
      onComplete: () => {
        ripple.remove()
      }
    }
  )
}

// 发光效果
const handleGlowMove = (event: MouseEvent) => {
  if (!glowRef.value || !glowOverlay.value) return

  const rect = glowRef.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  gsap.to(glowOverlay.value, {
    '--glow-x': `${x}px`,
    '--glow-y': `${y}px`,
    opacity: 0.3 * props.intensity,
    duration: 0.1,
    ease: 'none'
  })
}

const handleGlowLeave = () => {
  if (!glowOverlay.value) return

  gsap.to(glowOverlay.value, {
    opacity: 0,
    duration: props.duration,
    ease: 'power2.out'
  })
}

// 3D 倾斜效果
const handleTiltMove = (event: MouseEvent) => {
  if (!tiltRef.value) return

  const rect = tiltRef.value.getBoundingClientRect()
  const centerX = rect.left + rect.width / 2
  const centerY = rect.top + rect.height / 2
  
  const rotateX = (event.clientY - centerY) / rect.height * -20 * props.intensity
  const rotateY = (event.clientX - centerX) / rect.width * 20 * props.intensity

  gsap.to(tiltRef.value, {
    rotationX: rotateX,
    rotationY: rotateY,
    duration: props.duration,
    ease: 'power2.out',
    transformPerspective: 1000
  })
}

const handleTiltLeave = () => {
  if (!tiltRef.value) return

  gsap.to(tiltRef.value, {
    rotationX: 0,
    rotationY: 0,
    duration: props.duration * 1.5,
    ease: 'elastic.out(1, 0.3)'
  })
}

// 初始化画布尺寸
const initCanvas = () => {
  if (!particlesRef.value || !particlesCanvas.value) return

  const rect = particlesRef.value.getBoundingClientRect()
  canvasSize.width = rect.width
  canvasSize.height = rect.height
}

// 生命周期
onMounted(async () => {
  await nextTick()
  
  if (props.type === 'particles') {
    initCanvas()
    window.addEventListener('resize', initCanvas)
  }
})

onUnmounted(() => {
  if (animationFrame.value) {
    cancelAnimationFrame(animationFrame.value)
  }
  
  if (props.type === 'particles') {
    window.removeEventListener('resize', initCanvas)
  }
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.micro-interactions {
  display: inline-block;
  position: relative;
}

.magnetic-element,
.particles-element,
.ripple-element,
.glow-element,
.tilt-element {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

// 粒子效果样式
.particles-element {
  .particles-canvas {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 10;
  }
}

// 波纹效果样式
.ripple-element {
  overflow: hidden;
  
  :deep(.ripple) {
    position: absolute;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 1;
  }
}

// 发光效果样式
.glow-element {
  .glow-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    opacity: 0;
    background: radial-gradient(
      circle 100px at var(--glow-x, 50%) var(--glow-y, 50%),
      v-bind(color),
      transparent 70%
    );
    pointer-events: none;
    z-index: 1;
  }
}

// 3D 倾斜效果样式
.tilt-element {
  transform-style: preserve-3d;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%,
      rgba(0, 0, 0, 0.1) 100%
    );
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 1;
  }
  
  &:hover::before {
    opacity: 1;
  }
}

// 响应式调整
@media (max-width: 768px) {
  .magnetic-element,
  .tilt-element {
    // 在移动设备上禁用某些效果以提高性能
    pointer-events: auto;
    
    &:hover {
      transform: none !important;
    }
  }
}

// 减少动画偏好
@media (prefers-reduced-motion: reduce) {
  .magnetic-element,
  .particles-element,
  .ripple-element,
  .glow-element,
  .tilt-element {
    * {
      animation: none !important;
      transition: none !important;
    }
  }
}
</style>
