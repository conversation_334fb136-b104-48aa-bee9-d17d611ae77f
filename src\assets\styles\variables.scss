// 色彩系统
$primary-color: #1a365d;        // 深蓝色 - 主色调
$secondary-color: #ff6b35;      // 橙色 - 辅助色
$accent-color: #4299e1;         // 亮蓝色 - 强调色

// 中性色
$white: #ffffff;
$gray-50: #f7fafc;
$gray-100: #edf2f7;
$gray-200: #e2e8f0;
$gray-300: #cbd5e0;
$gray-400: #a0aec0;
$gray-500: #718096;
$gray-600: #4a5568;
$gray-700: #2d3748;
$gray-800: #1a202c;
$gray-900: #171923;
$black: #000000;

// 状态色
$success-color: #38a169;
$warning-color: #d69e2e;
$error-color: #e53e3e;
$info-color: #3182ce;

// 渐变色
$primary-gradient: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
$secondary-gradient: linear-gradient(135deg, $accent-color 0%, $primary-color 100%);

// 字体
$font-family-primary: 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
$font-family-secondary: 'Roboto', 'Source Han Sans CN', sans-serif;
$font-family-mono: 'Fira Code', 'Monaco', 'Consolas', 'Courier New', monospace;

// 字体大小
$font-size-xs: 0.75rem;    // 12px
$font-size-sm: 0.875rem;   // 14px
$font-size-base: 1rem;     // 16px
$font-size-lg: 1.125rem;   // 18px
$font-size-xl: 1.25rem;    // 20px
$font-size-2xl: 1.5rem;    // 24px
$font-size-3xl: 1.875rem;  // 30px
$font-size-4xl: 2.25rem;   // 36px
$font-size-5xl: 3rem;      // 48px

// 字重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.625;
$line-height-loose: 2;

// 间距
$spacing-0: 0;
$spacing-1: 0.25rem;   // 4px
$spacing-2: 0.5rem;    // 8px
$spacing-3: 0.75rem;   // 12px
$spacing-4: 1rem;      // 16px
$spacing-5: 1.25rem;   // 20px
$spacing-6: 1.5rem;    // 24px
$spacing-8: 2rem;      // 32px
$spacing-10: 2.5rem;   // 40px
$spacing-12: 3rem;     // 48px
$spacing-16: 4rem;     // 64px
$spacing-20: 5rem;     // 80px
$spacing-24: 6rem;     // 96px

// 圆角
$border-radius-sm: 0.125rem;   // 2px
$border-radius-base: 0.25rem;  // 4px
$border-radius-md: 0.375rem;   // 6px
$border-radius-lg: 0.5rem;     // 8px
$border-radius-xl: 0.75rem;    // 12px
$border-radius-2xl: 1rem;      // 16px
$border-radius-full: 9999px;

// 阴影
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
$shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

// 断点
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// Z-index
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 过渡动画
$transition-fast: 0.15s ease-in-out;
$transition-base: 0.3s ease-in-out;
$transition-slow: 0.5s ease-in-out;

// 容器最大宽度
$container-sm: 640px;
$container-md: 768px;
$container-lg: 1024px;
$container-xl: 1280px;
$container-2xl: 1536px;

// ===== 扩展设计系统 =====

// 更多渐变色
$gradient-warm: linear-gradient(135deg, #ff6b35 0%, #d69e2e 100%);
$gradient-cool: linear-gradient(135deg, #1a365d 0%, #4299e1 100%);
$gradient-success: linear-gradient(135deg, #38a169 0%, #4299e1 100%);
$gradient-sunset: linear-gradient(135deg, #ff6b35 0%, #e53e3e 100%);
$gradient-ocean: linear-gradient(135deg, #4299e1 0%, #1a365d 100%);

// 特殊效果色彩
$glass-bg: rgba(255, 255, 255, 0.1);
$glass-border: rgba(255, 255, 255, 0.2);
$backdrop-blur: blur(10px);

// 卡片样式变量
$card-bg: $white;
$card-border: $gray-200;
$card-shadow: $shadow-md;
$card-radius: $border-radius-xl;
$card-padding: $spacing-6;

// 按钮样式变量
$btn-padding-sm: #{$spacing-2} #{$spacing-4};
$btn-padding-md: #{$spacing-3} #{$spacing-6};
$btn-padding-lg: #{$spacing-4} #{$spacing-8};
$btn-radius: $border-radius-lg;
$btn-transition: all 0.3s ease;

// 输入框样式变量
$input-bg: $white;
$input-border: $gray-300;
$input-border-focus: $accent-color;
$input-padding: #{$spacing-3} #{$spacing-4};
$input-radius: $border-radius-md;
$input-shadow-focus: 0 0 0 3px rgba(66, 153, 225, 0.1);

// 导航样式变量
$nav-height: 4rem;
$nav-bg: rgba(255, 255, 255, 0.95);
$nav-backdrop: blur(10px);
$nav-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

// 页脚样式变量
$footer-bg: $gray-900;
$footer-text: $gray-300;
$footer-link: $gray-400;
$footer-link-hover: $white;

// 动画缓动函数
$ease-out-cubic: cubic-bezier(0.215, 0.61, 0.355, 1);
$ease-in-out-cubic: cubic-bezier(0.645, 0.045, 0.355, 1);
$ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
$ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);

// 几何装饰元素
$decoration-size-sm: 2rem;
$decoration-size-md: 4rem;
$decoration-size-lg: 6rem;
$decoration-opacity: 0.1;

// 网格系统
$grid-columns: 12;
$grid-gutter: $spacing-6;
$grid-margin: $spacing-4;

// 字体权重扩展
$font-weight-extrabold: 800;
$font-weight-black: 900;

// 字母间距
$letter-spacing-tight: -0.025em;
$letter-spacing-normal: 0;
$letter-spacing-wide: 0.025em;
$letter-spacing-wider: 0.05em;

// 行高扩展
$line-height-none: 1;
$line-height-snug: 1.375;

// 更多字体大小
$font-size-6xl: 3.75rem;   // 60px
$font-size-7xl: 4.5rem;    // 72px
$font-size-8xl: 6rem;      // 96px

// 边框样式
$border-width-thin: 1px;
$border-width-thick: 2px;
$border-width-thicker: 4px;
$border-style-solid: solid;
$border-style-dashed: dashed;
$border-style-dotted: dotted;

// 透明度级别
$opacity-0: 0;
$opacity-25: 0.25;
$opacity-50: 0.5;
$opacity-75: 0.75;
$opacity-100: 1;

// 模糊效果
$blur-none: 0;
$blur-sm: 4px;
$blur-base: 8px;
$blur-md: 12px;
$blur-lg: 16px;
$blur-xl: 24px;
