<template>
  <AppLayout>
    <Breadcrumb />

    <!-- 页面头部 -->
    <section class="services-hero" ref="heroRef">
      <div class="container">
        <div class="hero-content">
          <h1 class="page-title">我们的服务</h1>
          <p class="page-subtitle">
            专业的包装解决方案，满足您的各种需求
          </p>

          <!-- 服务统计 -->
          <div class="services-stats">
            <div
              v-for="(stat, index) in serviceStats"
              :key="stat.label"
              class="stat-item"
              :ref="el => setStatRef(el, index)"
            >
              <div class="stat-number">{{ animatedStats[stat.key] }}{{ stat.suffix }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 服务筛选和搜索 -->
    <section class="services-filters" ref="filtersRef">
      <div class="container">
        <div class="filters-layout">
          <!-- 搜索框 -->
          <div class="search-section">
            <SearchBox
              v-model="searchQuery"
              placeholder="搜索服务..."
              :suggestions="searchSuggestions"
              @search="handleSearch"
            />
          </div>

          <!-- 筛选器 -->
          <div class="filter-section">
            <FilterPanel
              v-model="filters"
              :filter-groups="filterGroups"
              @change="handleFilterChange"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- 服务列表 -->
    <section class="services-content" ref="contentRef">
      <div class="container">
        <!-- 排序和视图切换 -->
        <div class="content-header">
          <div class="results-info">
            <span class="results-count">找到 {{ filteredServices.length }} 个服务</span>
          </div>

          <div class="content-controls">
            <div class="sort-controls">
              <label for="sort-select" class="sort-label">排序：</label>
              <select
                id="sort-select"
                v-model="sortBy"
                class="sort-select"
                @change="handleSortChange"
              >
                <option value="default">默认排序</option>
                <option value="name-asc">名称升序</option>
                <option value="name-desc">名称降序</option>
                <option value="price-asc">价格升序</option>
                <option value="price-desc">价格降序</option>
              </select>
            </div>

            <div class="view-controls">
              <button
                class="view-btn"
                :class="{ 'active': viewMode === 'grid' }"
                @click="viewMode = 'grid'"
                aria-label="网格视图"
              >
                <i class="icon-grid"></i>
              </button>
              <button
                class="view-btn"
                :class="{ 'active': viewMode === 'list' }"
                @click="viewMode = 'list'"
                aria-label="列表视图"
              >
                <i class="icon-list"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- 服务网格/列表 -->
        <div
          class="services-grid"
          :class="[
            `services-grid--${viewMode}`,
            { 'loading': isLoading }
          ]"
        >
          <ServiceCard
            v-for="(service, index) in paginatedServices"
            :key="service.id"
            :service="service"
            :variant="viewMode === 'list' ? 'compact' : 'default'"
            :featured="service.featured"
            :ref="el => setServiceCardRef(el, index)"
            @click="handleServiceClick"
            @quote="handleQuoteRequest"
            @learn-more="handleLearnMore"
          />
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-container">
          <LoadingSpinner />
        </div>

        <!-- 空状态 -->
        <div v-else-if="filteredServices.length === 0" class="empty-state">
          <div class="empty-icon">
            <i class="icon-search"></i>
          </div>
          <h3 class="empty-title">未找到相关服务</h3>
          <p class="empty-description">
            请尝试调整搜索条件或筛选器
          </p>
          <button class="btn btn--primary" @click="clearFilters">
            清除筛选条件
          </button>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination-container">
          <div class="pagination">
            <button
              class="pagination-btn"
              :disabled="currentPage === 1"
              @click="goToPage(currentPage - 1)"
            >
              <i class="icon-arrow-left"></i>
              上一页
            </button>

            <div class="pagination-numbers">
              <button
                v-for="page in visiblePages"
                :key="page"
                class="pagination-number"
                :class="{ 'active': page === currentPage }"
                @click="goToPage(page)"
              >
                {{ page }}
              </button>
            </div>

            <button
              class="pagination-btn"
              :disabled="currentPage === totalPages"
              @click="goToPage(currentPage + 1)"
            >
              下一页
              <i class="icon-arrow-right"></i>
            </button>
          </div>
        </div>
      </div>
    </section>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import AppLayout from '@/components/layout/AppLayout.vue'
import Breadcrumb from '@/components/layout/Breadcrumb.vue'
import ServiceCard from '@/components/business/ServiceCard.vue'
import { SearchBox, FilterPanel, LoadingSpinner } from '@/components/common'
import { useServicesStore } from '@/stores'
import { useNavigation } from '@/composables'
import type { Service, FilterGroup } from '@/types'

gsap.registerPlugin(ScrollTrigger)

const servicesStore = useServicesStore()
const { navigateToContact } = useNavigation()

// 模板引用
const heroRef = ref<HTMLElement>()
const filtersRef = ref<HTMLElement>()
const contentRef = ref<HTMLElement>()
const statRefs = ref<HTMLElement[]>([])
const serviceCardRefs = ref<HTMLElement[]>([])

// 数据状态
const isLoading = ref(false)
const searchQuery = ref('')
const sortBy = ref('default')
const viewMode = ref<'grid' | 'list'>('grid')
const currentPage = ref(1)
const pageSize = ref(12)

// 筛选器状态
const filters = ref<Record<string, any>>({})

// 统计数据
const serviceStats = ref([
  { key: 'totalServices', label: '服务项目', value: 50, suffix: '+' },
  { key: 'industries', label: '服务行业', value: 20, suffix: '+' },
  { key: 'experience', label: '行业经验', value: 10, suffix: '年' },
  { key: 'satisfaction', label: '客户满意度', value: 98, suffix: '%' }
])

// 动画统计数据
const animatedStats = reactive<Record<string, number>>({
  totalServices: 0,
  industries: 0,
  experience: 0,
  satisfaction: 0
})

// 示例服务数据
const services = ref<Service[]>([
  {
    id: '1',
    title: '包装设计服务',
    description: '专业的包装设计团队，为您的产品打造独特的视觉形象，提升品牌价值和市场竞争力。',
    category: '设计服务',
    icon: 'icon-design',
    features: ['创意设计', '品牌一致性', '市场导向', '用户体验'],
    pricing: { startingPrice: 5000, unit: '起' },
    tags: ['创意', '品牌', '视觉'],
    duration: '7-14天',
    rating: 4.8,
    featured: true
  },
  {
    id: '2',
    title: '包装生产制造',
    description: '先进的生产设备和严格的质量控制，确保每一个包装产品的品质和交付时间。',
    category: '生产服务',
    icon: 'icon-production',
    features: ['质量保证', '快速交付', '成本优化', '环保材料'],
    pricing: { startingPrice: 10000, unit: '起' },
    tags: ['生产', '质量', '环保'],
    duration: '10-20天',
    rating: 4.9
  },
  {
    id: '3',
    title: '包装咨询顾问',
    description: '资深行业专家为您提供专业的包装解决方案和技术支持，助力业务发展。',
    category: '咨询服务',
    icon: 'icon-consulting',
    features: ['专业建议', '技术支持', '成本分析', '市场调研'],
    pricing: { startingPrice: 3000, unit: '起' },
    tags: ['咨询', '专业', '支持'],
    duration: '3-7天',
    rating: 4.7
  },
  {
    id: '4',
    title: '包装测试认证',
    description: '全面的包装测试服务，确保产品在运输和储存过程中的安全性和可靠性。',
    category: '测试服务',
    icon: 'icon-testing',
    features: ['安全测试', '耐久性测试', '环境测试', '质量认证'],
    pricing: { startingPrice: 2000, unit: '起' },
    tags: ['测试', '认证', '安全'],
    duration: '5-10天',
    rating: 4.6
  },
  {
    id: '5',
    title: '包装物流配送',
    description: '完善的物流配送体系，确保包装产品安全快速地到达目的地。',
    category: '物流服务',
    icon: 'icon-logistics',
    features: ['快速配送', '安全包装', '跟踪服务', '全国覆盖'],
    pricing: { startingPrice: 500, unit: '起' },
    tags: ['物流', '配送', '跟踪'],
    duration: '1-3天',
    rating: 4.5
  },
  {
    id: '6',
    title: '环保包装解决方案',
    description: '绿色环保的包装解决方案，为可持续发展贡献力量，符合环保要求。',
    category: '环保服务',
    icon: 'icon-eco',
    features: ['可回收材料', '生物降解', '节能减排', '绿色认证'],
    pricing: { startingPrice: 8000, unit: '起' },
    tags: ['环保', '可持续', '绿色'],
    duration: '10-15天',
    rating: 4.8,
    featured: true
  }
])

// 搜索建议
const searchSuggestions = computed(() => [
  { text: '包装设计', category: '设计' },
  { text: '包装生产', category: '生产' },
  { text: '包装咨询', category: '咨询' },
  { text: '包装测试', category: '测试' },
  { text: '环保包装', category: '环保' }
])

// 筛选器配置
const filterGroups = computed<FilterGroup[]>(() => [
  {
    key: 'category',
    title: '服务类别',
    type: 'checkbox',
    options: [
      { value: '设计服务', label: '设计服务', count: 1 },
      { value: '生产服务', label: '生产服务', count: 1 },
      { value: '咨询服务', label: '咨询服务', count: 1 },
      { value: '测试服务', label: '测试服务', count: 1 },
      { value: '物流服务', label: '物流服务', count: 1 },
      { value: '环保服务', label: '环保服务', count: 1 }
    ]
  },
  {
    key: 'priceRange',
    title: '价格范围',
    type: 'range',
    minPlaceholder: '最低价格',
    maxPlaceholder: '最高价格'
  },
  {
    key: 'duration',
    title: '服务周期',
    type: 'radio',
    options: [
      { value: 'short', label: '1-7天', count: 2 },
      { value: 'medium', label: '7-15天', count: 3 },
      { value: 'long', label: '15天以上', count: 1 }
    ]
  }
])

// 计算属性
const filteredServices = computed(() => {
  let result = [...services.value]

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(service =>
      service.title.toLowerCase().includes(query) ||
      service.description.toLowerCase().includes(query) ||
      service.category.toLowerCase().includes(query)
    )
  }

  // 类别过滤
  if (filters.value.category && filters.value.category.length > 0) {
    result = result.filter(service =>
      filters.value.category.includes(service.category)
    )
  }

  // 价格范围过滤
  if (filters.value.priceRange) {
    const { min, max } = filters.value.priceRange
    result = result.filter(service => {
      if (!service.pricing) return true
      const price = service.pricing.startingPrice
      if (min !== undefined && price < min) return false
      if (max !== undefined && price > max) return false
      return true
    })
  }

  // 排序
  if (sortBy.value !== 'default') {
    result.sort((a, b) => {
      switch (sortBy.value) {
        case 'name-asc':
          return a.title.localeCompare(b.title)
        case 'name-desc':
          return b.title.localeCompare(a.title)
        case 'price-asc':
          return (a.pricing?.startingPrice || 0) - (b.pricing?.startingPrice || 0)
        case 'price-desc':
          return (b.pricing?.startingPrice || 0) - (a.pricing?.startingPrice || 0)
        default:
          return 0
      }
    })
  }

  return result
})

const totalPages = computed(() => Math.ceil(filteredServices.value.length / pageSize.value))

const paginatedServices = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredServices.value.slice(start, end)
})

const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value

  // 显示当前页前后2页
  const start = Math.max(1, current - 2)
  const end = Math.min(total, current + 2)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
})

// 方法
const setStatRef = (el: HTMLElement | null, index: number) => {
  if (el) {
    statRefs.value[index] = el
  }
}

const setServiceCardRef = (el: HTMLElement | null, index: number) => {
  if (el) {
    serviceCardRefs.value[index] = el
  }
}

const handleSearch = (query: string) => {
  searchQuery.value = query
  currentPage.value = 1
}

const handleFilterChange = (newFilters: Record<string, any>) => {
  filters.value = newFilters
  currentPage.value = 1
}

const handleSortChange = () => {
  currentPage.value = 1
}

const handleServiceClick = (service: Service) => {
  // 导航到服务详情页
  console.log('Service clicked:', service)
}

const handleQuoteRequest = (service: Service) => {
  // 处理报价请求
  navigateToContact()
}

const handleLearnMore = (service: Service) => {
  // 了解更多
  console.log('Learn more:', service)
}

const clearFilters = () => {
  searchQuery.value = ''
  filters.value = {}
  currentPage.value = 1
}

const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    // 滚动到内容顶部
    contentRef.value?.scrollIntoView({ behavior: 'smooth' })
  }
}

const animateStats = () => {
  serviceStats.value.forEach(stat => {
    gsap.to(animatedStats, {
      [stat.key]: stat.value,
      duration: 2,
      ease: 'power2.out',
      scrollTrigger: {
        trigger: heroRef.value,
        start: 'top 80%',
        once: true
      }
    })
  })
}

const initAnimations = async () => {
  await nextTick()

  // 页面头部动画
  gsap.from(heroRef.value?.querySelector('.hero-content')?.children || [], {
    y: 50,
    opacity: 0,
    duration: 0.8,
    stagger: 0.2,
    ease: 'power2.out'
  })

  // 筛选器动画
  gsap.from(filtersRef.value, {
    y: 30,
    opacity: 0,
    duration: 0.6,
    ease: 'power2.out',
    delay: 0.3
  })

  // 服务卡片动画
  gsap.from(serviceCardRefs.value, {
    y: 60,
    opacity: 0,
    duration: 0.8,
    stagger: 0.1,
    ease: 'power2.out',
    scrollTrigger: {
      trigger: contentRef.value,
      start: 'top 80%',
      once: true
    }
  })

  // 统计数据动画
  animateStats()
}

// 监听器
watch(() => paginatedServices.value, () => {
  nextTick(() => {
    // 重新初始化卡片动画
    gsap.from(serviceCardRefs.value, {
      y: 30,
      opacity: 0,
      duration: 0.5,
      stagger: 0.05,
      ease: 'power2.out'
    })
  })
})

// 生命周期
onMounted(() => {
  initAnimations()
})
</script>

<style lang="scss" scoped>
.services-hero {
  padding: 4rem 0 2rem;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 20% 80%, rgba(26, 54, 93, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.03) 0%, transparent 50%);
    pointer-events: none;
  }

  .hero-content {
    text-align: center;
    position: relative;
    z-index: 2;

    .page-title {
      font-size: 3rem;
      font-weight: 700;
      color: #1a365d;
      margin-bottom: 1rem;

      @media (max-width: 768px) {
        font-size: 2.5rem;
      }

      @media (max-width: 640px) {
        font-size: 2rem;
      }
    }

    .page-subtitle {
      font-size: 1.25rem;
      color: #4a5568;
      margin-bottom: 3rem;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;

      @media (max-width: 768px) {
        font-size: 1.125rem;
      }
    }
  }

  .services-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .stat-item {
      text-align: center;
      background: white;
      padding: 1.5rem 1rem;
      border-radius: 1rem;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

      .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #ff6b35;
        margin-bottom: 0.5rem;

        @media (max-width: 768px) {
          font-size: 1.75rem;
        }
      }

      .stat-label {
        font-size: 0.875rem;
        color: #4a5568;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }
    }
  }
}

.services-filters {
  padding: 2rem 0;
  background: white;
  border-bottom: 1px solid #e2e8f0;

  .filters-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    align-items: start;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }

  .search-section {
    .search-box {
      max-width: 500px;
    }
  }
}

.services-content {
  padding: 3rem 0;

  .content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f7fafc;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }

    .results-info {
      .results-count {
        color: #4a5568;
        font-size: 0.875rem;
      }
    }

    .content-controls {
      display: flex;
      align-items: center;
      gap: 1.5rem;

      @media (max-width: 768px) {
        justify-content: space-between;
      }

      .sort-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .sort-label {
          font-size: 0.875rem;
          color: #4a5568;
        }

        .sort-select {
          padding: 0.5rem;
          border: 1px solid #e2e8f0;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          background: white;

          &:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
          }
        }
      }

      .view-controls {
        display: flex;
        gap: 0.25rem;
        background: #f7fafc;
        padding: 0.25rem;
        border-radius: 0.5rem;

        .view-btn {
          padding: 0.5rem;
          background: none;
          border: none;
          border-radius: 0.25rem;
          cursor: pointer;
          color: #718096;
          transition: all 0.2s ease;

          &:hover {
            color: #4a5568;
            background: rgba(255, 255, 255, 0.5);
          }

          &.active {
            color: #1a365d;
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          }

          i {
            font-size: 1rem;
          }
        }
      }
    }
  }

  .services-grid {
    display: grid;
    gap: 2rem;
    margin-bottom: 3rem;

    &--grid {
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
    }

    &--list {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    &.loading {
      opacity: 0.6;
      pointer-events: none;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    padding: 3rem 0;
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;

    .empty-icon {
      width: 4rem;
      height: 4rem;
      background: #f7fafc;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1.5rem;

      i {
        font-size: 1.5rem;
        color: #a0aec0;
      }
    }

    .empty-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 0.5rem;
    }

    .empty-description {
      color: #718096;
      margin-bottom: 2rem;
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 3rem;

    .pagination {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .pagination-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        color: #4a5568;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 0.875rem;

        &:hover:not(:disabled) {
          background: #f7fafc;
          border-color: #cbd5e0;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .pagination-numbers {
        display: flex;
        gap: 0.25rem;
        margin: 0 1rem;

        .pagination-number {
          width: 2.5rem;
          height: 2.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          background: white;
          border: 1px solid #e2e8f0;
          border-radius: 0.375rem;
          color: #4a5568;
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: 0.875rem;

          &:hover {
            background: #f7fafc;
            border-color: #cbd5e0;
          }

          &.active {
            background: #1a365d;
            border-color: #1a365d;
            color: white;
          }
        }
      }
    }
  }
}

// 图标
.icon-grid::before { content: '⊞'; }
.icon-list::before { content: '☰'; }
.icon-search::before { content: '🔍'; }
.icon-arrow-left::before { content: '←'; }
.icon-arrow-right::before { content: '→'; }
</style>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.services-page {
  padding: $spacing-20 0;
  
  h1 {
    text-align: center;
    margin-bottom: $spacing-6;
  }
  
  p {
    text-align: center;
    color: $gray-600;
    font-size: $font-size-lg;
  }
}
</style>
