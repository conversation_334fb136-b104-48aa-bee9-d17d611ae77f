import{K as G,b as U,c as f,a as L,N as K,r as B,o as H,J as Y,i as o,p as S,h as e,v as j,j as C,s as m,t as h,D as q,B as J,F as E,k as M,m as a,_ as O,u as Q,f as w,z as F,w as $,l as I,T as R,G as X}from"./index-Dz-_Smvi.js";/* empty css                                                                            */import{a as Z,u as ee}from"./contact-sLg3zrn9.js";const y={HOME:"Home",SERVICES:"Services",SERVICE_DETAIL:"ServiceDetail",PRODUCTS:"Products",PRODUCT_DETAIL:"ProductDetail",CASES:"Cases",CASE_DETAIL:"CaseDetail",CONTACT:"Contact",ABOUT:"About"},N=()=>{const i=G(),t=U(),n=f(()=>[{id:"home",label:"首页",path:"/",icon:"home"},{id:"services",label:"服务展示",path:"/services",icon:"services"},{id:"products",label:"产品目录",path:"/products",icon:"products"},{id:"cases",label:"客户案例",path:"/cases",icon:"cases"},{id:"about",label:"关于我们",path:"/about",icon:"about"},{id:"contact",label:"联系我们",path:"/contact",icon:"contact"}]),c=f(()=>n.value.find(l=>l.path==="/"?t.path==="/":t.path.startsWith(l.path))),r=f(()=>{const l=[];return t.path!=="/"&&l.push({label:"首页",path:"/"}),t.path.startsWith("/services")?(l.push({label:"服务展示",path:"/services"}),t.params.category&&l.push({label:t.params.category,path:t.path})):t.path.startsWith("/products")?(l.push({label:"产品目录",path:"/products"}),t.params.id&&l.push({label:"产品详情",path:t.path})):t.path.startsWith("/cases")?(l.push({label:"客户案例",path:"/cases"}),t.params.id&&l.push({label:"案例详情",path:t.path})):t.path==="/about"?l.push({label:"关于我们",path:"/about"}):t.path==="/contact"&&l.push({label:"联系我们",path:"/contact"}),l}),_=l=>{i.push(l)},g=()=>{i.back()},k=()=>{i.forward()},T=()=>{i.push("/")},A=l=>{l?i.push(`/services/${l}`):i.push("/services")},d=l=>{l?i.push(`/products/${l}`):i.push("/products")},s=l=>{l?i.push(`/cases/${l}`):i.push("/cases")},u=()=>{i.push("/contact")},b=()=>{i.push("/about")},p=l=>l==="/"?t.path==="/":t.path.startsWith(l),D=f(()=>t.name===y.HOME),v=f(()=>t.name===y.SERVICES||t.name===y.SERVICE_DETAIL),P=f(()=>t.name===y.PRODUCTS||t.name===y.PRODUCT_DETAIL),W=f(()=>t.name===y.CASES||t.name===y.CASE_DETAIL),x=f(()=>t.name===y.CONTACT),z=f(()=>t.name===y.ABOUT);return{mainMenuItems:n,activeMenuItem:c,breadcrumbs:r,isHomePage:D,isServicesPage:v,isProductsPage:P,isCasesPage:W,isContactPage:x,isAboutPage:z,navigateTo:_,navigateBack:g,navigateForward:k,navigateToHome:T,navigateToServices:A,navigateToProducts:d,navigateToCases:s,navigateToContact:u,navigateToAbout:b,isCurrentRoute:p}},V="/logo.svg",st=(i,t)=>{let n=null;return(...c)=>{n&&clearTimeout(n),n=setTimeout(()=>i(...c),t)}},te=(i,t)=>{let n=!1;return(...c)=>{n||(i(...c),n=!0,setTimeout(()=>n=!1,t))}},ot=(i,t="YYYY-MM-DD")=>{const n=new Date(i),c=n.getFullYear(),r=String(n.getMonth()+1).padStart(2,"0"),_=String(n.getDate()).padStart(2,"0"),g=String(n.getHours()).padStart(2,"0"),k=String(n.getMinutes()).padStart(2,"0"),T=String(n.getSeconds()).padStart(2,"0");return t.replace("YYYY",c.toString()).replace("MM",r).replace("DD",_).replace("HH",g).replace("mm",k).replace("ss",T)},at=i=>i.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","),se=["aria-label","title"],oe={class:"theme-icon"},ae={key:0,class:"theme-label"},ne=["onClick"],le={class:"option-icon"},ie={class:"option-label"},ce={key:0,class:"check-icon"},re={key:1,class:"theme-buttons"},ue=["onClick","aria-label","title"],de={class:"button-icon"},pe={key:0,class:"button-label"},ve=L({__name:"ThemeToggle",props:{variant:{default:"simple"},showLabel:{type:Boolean,default:!1},showButtonLabels:{type:Boolean,default:!1},size:{default:"md"}},setup(i){const t=i,n=K(),c=B(!1),{currentTheme:r,themeDisplayName:_,themeIcon:g,availableThemes:k,setTheme:T,toggleTheme:A}=n,d=()=>{t.variant==="simple"?A():t.variant==="dropdown"&&(c.value=!c.value)},s=p=>{T(p),t.variant==="dropdown"&&(c.value=!1)},u=p=>{const D=p.target,v=document.querySelector(".theme-toggle");v&&!v.contains(D)&&(c.value=!1)},b=p=>{p.key==="Escape"&&(c.value=!1)};return H(()=>{t.variant==="dropdown"&&(document.addEventListener("click",u),document.addEventListener("keydown",b))}),Y(()=>{t.variant==="dropdown"&&(document.removeEventListener("click",u),document.removeEventListener("keydown",b))}),(p,D)=>(a(),o("div",{class:S(["theme-toggle",{"theme-toggle--expanded":c.value}])},[e("button",{class:S(["theme-toggle-btn",{active:c.value}]),onClick:d,"aria-label":`当前主题: ${m(_)}`,title:`当前主题: ${m(_)}`},[e("span",oe,h(m(g)),1),p.showLabel?(a(),o("span",ae,h(m(_)),1)):C("",!0),p.variant==="dropdown"?(a(),o("i",{key:1,class:S(["dropdown-icon",{rotated:c.value}])},"▼",2)):C("",!0)],10,se),p.variant==="dropdown"?j((a(),o("div",{key:0,class:"theme-options",onClick:D[0]||(D[0]=J(()=>{},["stop"]))},[(a(!0),o(E,null,M(m(k),v=>(a(),o("button",{key:v.value,class:S(["theme-option",{active:m(r)===v.value}]),onClick:P=>s(v.value)},[e("span",le,h(v.icon),1),e("span",ie,h(v.label),1),m(r)===v.value?(a(),o("i",ce,"✓")):C("",!0)],10,ne))),128))],512)),[[q,c.value]]):C("",!0),p.variant==="buttons"?(a(),o("div",re,[(a(!0),o(E,null,M(m(k),v=>(a(),o("button",{key:v.value,class:S(["theme-button",{active:m(r)===v.value}]),onClick:P=>s(v.value),"aria-label":v.label,title:v.label},[e("span",de,h(v.icon),1),p.showButtonLabels?(a(),o("span",pe,h(v.label),1)):C("",!0)],10,ue))),128))])):C("",!0),p.variant==="simple"?(a(),o(E,{key:2},[],64)):C("",!0)],2))}}),he=O(ve,[["__scopeId","data-v-ebc750bf"]]),me={class:"container"},_e={class:"header-content"},be={class:"desktop-nav","aria-label":"主导航"},fe={class:"nav-list"},ge={class:"header-actions"},ke={key:0,class:"mobile-menu"},we={class:"mobile-nav","aria-label":"移动端导航"},Se={class:"mobile-nav-list"},Te=L({__name:"AppHeader",setup(i){const t=Q(),{mainMenuItems:n,isCurrentRoute:c}=N(),r=B(!1),_=f(()=>t.isMobileMenuOpen),g=()=>{t.toggleMobileMenu()},k=()=>{t.closeMobileMenu()},T=()=>{r.value=window.scrollY>50};return H(()=>{window.addEventListener("scroll",T)}),Y(()=>{window.removeEventListener("scroll",T)}),(A,d)=>{const s=F("router-link");return a(),o("header",{class:S(["app-header",{scrolled:r.value}])},[e("div",me,[e("div",_e,[w(s,{to:"/",class:"logo"},{default:$(()=>d[0]||(d[0]=[e("img",{src:V,alt:"包装解决方案",class:"logo-image"},null,-1),e("span",{class:"logo-text"},"包装解决方案",-1)])),_:1,__:[0]}),e("nav",be,[e("ul",fe,[(a(!0),o(E,null,M(m(n),u=>(a(),o("li",{key:u.id,class:"nav-item"},[w(s,{to:u.path,class:S(["nav-link",{active:m(c)(u.path)}]),onClick:k},{default:$(()=>[I(h(u.label),1)]),_:2},1032,["to","class"])]))),128))])]),e("div",ge,[w(m(he),{variant:"simple",size:"sm"}),w(s,{to:"/contact",class:"btn btn--primary btn--sm"},{default:$(()=>d[1]||(d[1]=[I(" 联系我们 ",-1)])),_:1,__:[1]})]),e("button",{class:S(["mobile-menu-button",{active:_.value}]),onClick:g,"aria-label":"切换菜单"},d[2]||(d[2]=[e("span",{class:"hamburger-line"},null,-1),e("span",{class:"hamburger-line"},null,-1),e("span",{class:"hamburger-line"},null,-1)]),2)])]),w(R,{name:"mobile-menu"},{default:$(()=>[_.value?(a(),o("div",ke,[e("nav",we,[e("ul",Se,[(a(!0),o(E,null,M(m(n),u=>(a(),o("li",{key:u.id,class:"mobile-nav-item"},[w(s,{to:u.path,class:S(["mobile-nav-link",{active:m(c)(u.path)}]),onClick:k},{default:$(()=>[I(h(u.label),1)]),_:2},1032,["to","class"])]))),128))])])])):C("",!0)]),_:1}),w(R,{name:"overlay"},{default:$(()=>[_.value?(a(),o("div",{key:0,class:"mobile-menu-overlay",onClick:k})):C("",!0)]),_:1})],2)}}}),Ce=O(Te,[["__scopeId","data-v-d5bbf9ab"]]),ye={class:"app-footer"},$e={class:"container"},Ee={class:"footer-main"},Me={class:"footer-grid"},Ae={class:"footer-section"},De={class:"company-info"},Ie={class:"company-description"},Le={class:"social-media"},Oe=["href","aria-label"],Pe={class:"footer-section"},Re={class:"link-list"},Be={class:"footer-section"},He={class:"link-list"},Ye={class:"footer-section"},Ne={class:"contact-info"},Ue={class:"contact-item"},Fe={class:"contact-item"},Ve=["href"],We={class:"contact-item"},xe=["href"],ze={class:"contact-item"},Ge={class:"business-hours"},Ke={class:"footer-bottom"},je={class:"copyright"},qe=L({__name:"AppFooter",setup(i){const t=Z(),n=ee(),{mainMenuItems:c}=N(),r=f(()=>t.companyInfo),_=f(()=>n.serviceCategories),g=f(()=>new Date().getFullYear()),k=(d,s)=>{const u={wechat:"#",weibo:"https://weibo.com/",linkedin:"https://linkedin.com/company/",facebook:"https://facebook.com/",twitter:"https://twitter.com/",instagram:"https://instagram.com/"};return u[d]?`${u[d]}${s}`:"#"},T=d=>({wechat:"微信",weibo:"微博",linkedin:"LinkedIn",facebook:"Facebook",twitter:"Twitter",instagram:"Instagram"})[d]||d,A=d=>({wechat:"icon-wechat",weibo:"icon-weibo",linkedin:"icon-linkedin",facebook:"icon-facebook",twitter:"icon-twitter",instagram:"icon-instagram"})[d]||"icon-link";return(d,s)=>{const u=F("router-link");return a(),o("footer",ye,[e("div",$e,[e("div",Ee,[e("div",Me,[e("div",Ae,[e("div",De,[s[0]||(s[0]=e("div",{class:"logo"},[e("img",{src:V,alt:"包装解决方案",class:"logo-image"}),e("span",{class:"logo-text"},"包装解决方案")],-1)),e("p",Ie,h(r.value.description),1),e("div",Le,[(a(!0),o(E,null,M(r.value.socialMedia,(b,p)=>(a(),o("a",{key:p,href:k(p,b),class:"social-link","aria-label":`关注我们的${T(p)}`,target:"_blank",rel:"noopener noreferrer"},[e("i",{class:S(A(p))},null,2)],8,Oe))),128))])])]),e("div",Pe,[s[1]||(s[1]=e("h3",{class:"section-title"},"快速链接",-1)),e("ul",Re,[(a(!0),o(E,null,M(m(c),b=>(a(),o("li",{key:b.id},[w(u,{to:b.path,class:"footer-link"},{default:$(()=>[I(h(b.label),1)]),_:2},1032,["to"])]))),128))])]),e("div",Be,[s[2]||(s[2]=e("h3",{class:"section-title"},"我们的服务",-1)),e("ul",He,[(a(!0),o(E,null,M(_.value,b=>(a(),o("li",{key:b},[w(u,{to:`/services/${encodeURIComponent(b)}`,class:"footer-link"},{default:$(()=>[I(h(b),1)]),_:2},1032,["to"])]))),128))])]),e("div",Ye,[s[7]||(s[7]=e("h3",{class:"section-title"},"联系我们",-1)),e("div",Ne,[e("div",Ue,[s[3]||(s[3]=e("i",{class:"icon-location"},null,-1)),e("span",null,h(r.value.address),1)]),e("div",Fe,[s[4]||(s[4]=e("i",{class:"icon-phone"},null,-1)),e("a",{href:`tel:${r.value.phone}`,class:"contact-link"},h(r.value.phone),9,Ve)]),e("div",We,[s[5]||(s[5]=e("i",{class:"icon-email"},null,-1)),e("a",{href:`mailto:${r.value.email}`,class:"contact-link"},h(r.value.email),9,xe)]),e("div",ze,[s[6]||(s[6]=e("i",{class:"icon-clock"},null,-1)),e("div",Ge,[e("div",null,h(r.value.businessHours.weekdays),1),e("div",null,h(r.value.businessHours.weekends),1)])])])])])]),e("div",Ke,[e("div",je,[e("p",null,"© "+h(g.value)+" "+h(r.value.name)+". 保留所有权利。",1)]),s[8]||(s[8]=e("div",{class:"legal-links"},[e("a",{href:"/privacy",class:"legal-link"},"隐私政策"),e("a",{href:"/terms",class:"legal-link"},"服务条款"),e("a",{href:"/sitemap",class:"legal-link"},"网站地图")],-1))])])])}}}),Je=O(qe,[["__scopeId","data-v-88c830ff"]]),Qe={class:"app-layout"},Xe=L({__name:"AppLayout",setup(i){U();const{isHomePage:t}=N(),n=B(!1),c=te(()=>{n.value=window.scrollY>300},100),r=()=>{window.scrollTo({top:0,behavior:"smooth"})};return H(()=>{window.addEventListener("scroll",c)}),Y(()=>{window.removeEventListener("scroll",c)}),(_,g)=>(a(),o("div",Qe,[w(Ce),e("main",{class:S(["main-content",{"with-padding":!m(t)}])},[X(_.$slots,"default",{},void 0,!0)],2),w(Je),w(R,{name:"back-to-top"},{default:$(()=>[n.value?(a(),o("button",{key:0,class:"back-to-top-button",onClick:r,"aria-label":"回到顶部"},g[0]||(g[0]=[e("i",{class:"icon-arrow-up"},null,-1)]))):C("",!0)]),_:1})]))}}),nt=O(Xe,[["__scopeId","data-v-f96ea67d"]]);export{nt as A,ot as a,st as d,at as f,N as u};
