<template>
  <div 
    class="product-card" 
    :class="[
      `product-card--${variant}`,
      { 'product-card--featured': featured }
    ]"
    ref="cardRef"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @click="handleClick"
  >
    <!-- 产品图片 -->
    <div class="product-image" ref="imageRef">
      <img 
        :src="product.image || '/images/product-default.svg'" 
        :alt="product.name"
        @error="handleImageError"
      />
      
      <!-- 图片遮罩 -->
      <div class="image-overlay" ref="overlayRef">
        <div class="overlay-actions">
          <button class="action-btn" @click.stop="handleQuickView" title="快速预览">
            <i class="icon-eye"></i>
          </button>
          <button class="action-btn" @click.stop="handleAddToCart" title="加入购物车">
            <i class="icon-cart"></i>
          </button>
          <button class="action-btn" @click.stop="handleCompare" title="对比产品">
            <i class="icon-compare"></i>
          </button>
        </div>
      </div>

      <!-- 产品标签 -->
      <div class="product-badges">
        <span v-if="product.isNew" class="badge badge--new">新品</span>
        <span v-if="product.isHot" class="badge badge--hot">热销</span>
        <span v-if="product.discount" class="badge badge--discount">
          -{{ product.discount }}%
        </span>
        <span v-if="featured" class="badge badge--featured">推荐</span>
      </div>
    </div>

    <!-- 产品信息 -->
    <div class="product-info">
      <!-- 产品分类 -->
      <div class="product-category">{{ product.category }}</div>
      
      <!-- 产品名称 -->
      <h3 class="product-name">{{ product.name }}</h3>
      
      <!-- 产品描述 -->
      <p v-if="variant !== 'compact'" class="product-description">
        {{ product.description }}
      </p>

      <!-- 产品特性 -->
      <div v-if="product.features && variant === 'detailed'" class="product-features">
        <div 
          v-for="feature in product.features.slice(0, 3)" 
          :key="feature"
          class="feature-item"
        >
          <i class="icon-check"></i>
          <span>{{ feature }}</span>
        </div>
      </div>

      <!-- 产品规格 -->
      <div v-if="product.specifications && variant !== 'compact'" class="product-specs">
        <div
          v-for="(value, key) in getDisplaySpecs"
          :key="key"
          class="spec-item"
        >
          <span class="spec-label">{{ key }}:</span>
          <span class="spec-value">{{ value }}</span>
        </div>
      </div>

      <!-- 价格信息 -->
      <div class="product-pricing">
        <div class="price-main">
          <span v-if="product.originalPrice && product.originalPrice > product.price" class="price-original">
            ¥{{ formatPrice(product.originalPrice) }}
          </span>
          <span class="price-current">¥{{ formatPrice(product.price) }}</span>
        </div>
        <div v-if="product.priceUnit" class="price-unit">{{ product.priceUnit }}</div>
      </div>

      <!-- 产品评分 -->
      <div v-if="product.rating" class="product-rating">
        <div class="rating-stars">
          <i 
            v-for="star in 5" 
            :key="star"
            class="star"
            :class="{ 'filled': star <= Math.floor(product.rating) }"
          ></i>
        </div>
        <span class="rating-text">({{ product.rating }}) {{ product.reviewCount || 0 }}条评价</span>
      </div>

      <!-- 库存状态 -->
      <div class="product-stock">
        <div 
          class="stock-indicator"
          :class="{
            'in-stock': product.stock > 10,
            'low-stock': product.stock > 0 && product.stock <= 10,
            'out-of-stock': product.stock === 0
          }"
        >
          <span v-if="product.stock > 10" class="stock-text">现货充足</span>
          <span v-else-if="product.stock > 0" class="stock-text">库存紧张 ({{ product.stock }}件)</span>
          <span v-else class="stock-text">暂时缺货</span>
        </div>
      </div>
    </div>

    <!-- 产品操作 -->
    <div class="product-actions">
      <button 
        class="btn btn--secondary btn--sm"
        @click.stop="handleQuote"
        :disabled="product.stock === 0"
      >
        获取报价
      </button>
      <button 
        class="btn btn--primary btn--sm"
        @click.stop="handleViewDetails"
      >
        查看详情
        <i class="icon-arrow-right"></i>
      </button>
    </div>

    <!-- 悬停效果 -->
    <div class="hover-effect" ref="hoverEffectRef"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { gsap } from 'gsap'
import { useNavigation } from '@/composables'
import { formatNumber } from '@/utils'
import type { Product } from '@/types'

interface Props {
  product: Product
  variant?: 'default' | 'compact' | 'detailed'
  featured?: boolean
}

interface Emits {
  (e: 'click', product: Product): void
  (e: 'quick-view', product: Product): void
  (e: 'add-to-cart', product: Product): void
  (e: 'compare', product: Product): void
  (e: 'quote', product: Product): void
  (e: 'view-details', product: Product): void
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  featured: false
})

const emit = defineEmits<Emits>()

const { navigateToProducts } = useNavigation()

// 模板引用
const cardRef = ref<HTMLElement>()
const imageRef = ref<HTMLElement>()
const overlayRef = ref<HTMLElement>()
const hoverEffectRef = ref<HTMLElement>()

// 动画时间线
let hoverTimeline: gsap.core.Timeline

// 计算属性
const getDisplaySpecs = computed(() => {
  if (!props.product.specifications) return {}
  
  const specs = props.product.specifications
  const maxSpecs = props.variant === 'detailed' ? 4 : 2
  
  return Object.fromEntries(
    Object.entries(specs).slice(0, maxSpecs)
  )
})

// 方法
const formatPrice = (price: number): string => {
  return formatNumber(price)
}

const handleClick = () => {
  emit('click', props.product)
}

const handleQuickView = () => {
  emit('quick-view', props.product)
}

const handleAddToCart = () => {
  emit('add-to-cart', props.product)
}

const handleCompare = () => {
  emit('compare', props.product)
}

const handleQuote = () => {
  emit('quote', props.product)
}

const handleViewDetails = () => {
  emit('view-details', props.product)
  navigateToProducts(props.product.id)
}

const handleImageError = () => {
  // 处理图片加载错误
  const img = imageRef.value?.querySelector('img') as HTMLImageElement
  if (img) {
    img.src = '/images/product-default.svg'
  }
}

const handleMouseEnter = () => {
  if (hoverTimeline) {
    hoverTimeline.kill()
  }

  hoverTimeline = gsap.timeline()

  // 卡片整体效果
  hoverTimeline.to(cardRef.value, {
    y: -8,
    scale: 1.02,
    duration: 0.3,
    ease: 'power2.out'
  })

  // 图片缩放
  hoverTimeline.to(imageRef.value?.querySelector('img'), {
    scale: 1.1,
    duration: 0.4,
    ease: 'power2.out'
  }, 0)

  // 遮罩层显示
  hoverTimeline.to(overlayRef.value, {
    opacity: 1,
    duration: 0.3,
    ease: 'power2.out'
  }, 0.1)

  // 操作按钮动画
  hoverTimeline.from(overlayRef.value?.querySelectorAll('.action-btn') || [], {
    y: 20,
    opacity: 0,
    duration: 0.3,
    stagger: 0.05,
    ease: 'power2.out'
  }, 0.2)

  // 悬停效果
  hoverTimeline.to(hoverEffectRef.value, {
    opacity: 1,
    duration: 0.3,
    ease: 'power2.out'
  }, 0)
}

const handleMouseLeave = () => {
  if (hoverTimeline) {
    hoverTimeline.kill()
  }

  hoverTimeline = gsap.timeline()

  // 恢复卡片状态
  hoverTimeline.to(cardRef.value, {
    y: 0,
    scale: 1,
    duration: 0.3,
    ease: 'power2.out'
  })

  // 恢复图片状态
  hoverTimeline.to(imageRef.value?.querySelector('img'), {
    scale: 1,
    duration: 0.4,
    ease: 'power2.out'
  }, 0)

  // 隐藏遮罩层
  hoverTimeline.to(overlayRef.value, {
    opacity: 0,
    duration: 0.3,
    ease: 'power2.out'
  }, 0)

  // 隐藏悬停效果
  hoverTimeline.to(hoverEffectRef.value, {
    opacity: 0,
    duration: 0.3,
    ease: 'power2.out'
  }, 0)
}

// 生命周期
onMounted(() => {
  // 初始化动画状态
  gsap.set(overlayRef.value, { opacity: 0 })
  gsap.set(hoverEffectRef.value, { opacity: 0 })
  gsap.set(overlayRef.value?.querySelectorAll('.action-btn') || [], { y: 20, opacity: 0 })
})
</script>

<style lang="scss" scoped>
.product-card {
  position: relative;
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #f7fafc;

  &--featured {
    border-color: #ff6b35;
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.15);
  }

  &--compact {
    .product-info {
      padding: 1rem;
    }

    .product-image {
      height: 200px;
    }
  }

  &--detailed {
    .product-info {
      padding: 1.5rem;
    }

    .product-image {
      height: 280px;
    }
  }
}

.product-image {
  position: relative;
  height: 240px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
  }

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;

    .overlay-actions {
      display: flex;
      gap: 0.75rem;

      .action-btn {
        width: 2.5rem;
        height: 2.5rem;
        background: white;
        border: none;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #1a365d;

        &:hover {
          background: #ff6b35;
          color: white;
          transform: scale(1.1);
        }

        i {
          font-size: 0.875rem;
        }
      }
    }
  }

  .product-badges {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .badge {
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-size: 0.7rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.05em;

      &--new {
        background: #38a169;
        color: white;
      }

      &--hot {
        background: #e53e3e;
        color: white;
      }

      &--discount {
        background: #ff6b35;
        color: white;
      }

      &--featured {
        background: linear-gradient(135deg, #1a365d 0%, #ff6b35 100%);
        color: white;
      }
    }
  }
}

.product-info {
  padding: 1.25rem;

  .product-category {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
  }

  .product-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1a365d;
    margin-bottom: 0.75rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .product-description {
    color: #4a5568;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .product-features {
    margin-bottom: 1rem;

    .feature-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;
      font-size: 0.8rem;
      color: #718096;

      &:last-child {
        margin-bottom: 0;
      }

      .icon-check {
        color: #38a169;
        font-size: 0.7rem;
        flex-shrink: 0;
      }
    }
  }

  .product-specs {
    margin-bottom: 1rem;

    .spec-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.25rem;
      font-size: 0.8rem;

      &:last-child {
        margin-bottom: 0;
      }

      .spec-label {
        color: #718096;
      }

      .spec-value {
        color: #2d3748;
        font-weight: 500;
      }
    }
  }

  .product-pricing {
    margin-bottom: 0.75rem;

    .price-main {
      display: flex;
      align-items: baseline;
      gap: 0.5rem;
      margin-bottom: 0.25rem;

      .price-original {
        font-size: 0.875rem;
        color: #a0aec0;
        text-decoration: line-through;
      }

      .price-current {
        font-size: 1.25rem;
        font-weight: 700;
        color: #e53e3e;
      }
    }

    .price-unit {
      font-size: 0.75rem;
      color: #718096;
    }
  }

  .product-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;

    .rating-stars {
      display: flex;
      gap: 0.125rem;

      .star {
        color: #e2e8f0;
        font-size: 0.8rem;

        &.filled {
          color: #ffd700;
        }

        &::before {
          content: '★';
        }
      }
    }

    .rating-text {
      font-size: 0.75rem;
      color: #718096;
    }
  }

  .product-stock {
    margin-bottom: 1rem;

    .stock-indicator {
      .stock-text {
        font-size: 0.8rem;
        font-weight: 500;
      }

      &.in-stock .stock-text {
        color: #38a169;
      }

      &.low-stock .stock-text {
        color: #d69e2e;
      }

      &.out-of-stock .stock-text {
        color: #e53e3e;
      }
    }
  }
}

.product-actions {
  padding: 0 1.25rem 1.25rem;
  display: flex;
  gap: 0.75rem;

  .btn {
    flex: 1;
    font-size: 0.8rem;
    padding: 0.5rem 1rem;

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

.hover-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(26, 54, 93, 0.02) 0%, rgba(255, 107, 53, 0.02) 100%);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

// 图标
.icon-check::before { content: '✓'; }
.icon-arrow-right::before { content: '→'; }
.icon-eye::before { content: '👁'; }
.icon-cart::before { content: '🛒'; }
.icon-compare::before { content: '⚖️'; }

// 响应式
@media (max-width: 640px) {
  .product-card {
    .product-actions {
      flex-direction: column;

      .btn {
        width: 100%;
      }
    }
  }
}
</style>
