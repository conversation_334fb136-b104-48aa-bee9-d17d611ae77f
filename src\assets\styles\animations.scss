// 动画效果系统

@use 'variables' as *;

// ===== 关键帧动画定义 =====

// 淡入动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 淡出动画
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

// 向上滑入
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 向下滑入
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 向左滑入
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 向右滑入
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 缩放进入
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 缩放退出
@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.9);
  }
}

// 旋转进入
@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-180deg);
  }
  to {
    opacity: 1;
    transform: rotate(0deg);
  }
}

// 弹跳进入
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 摇摆动画
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// 旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 心跳动画
@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

// 橡皮筋动画
@keyframes rubberBand {
  from {
    transform: scale3d(1, 1, 1);
  }
  30% {
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    transform: scale3d(1.05, 0.95, 1);
  }
  to {
    transform: scale3d(1, 1, 1);
  }
}

// 摆动动画
@keyframes swing {
  20% {
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    transform: rotate3d(0, 0, 1, -5deg);
  }
  to {
    transform: rotate3d(0, 0, 1, 0deg);
  }
}

// 闪烁动画
@keyframes flash {
  from, 50%, to {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0;
  }
}

// 渐变背景动画
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// 浮动动画
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

// ===== 动画工具类 =====

// 基础动画类
.animate {
  &-fade-in {
    animation: fadeIn 0.5s ease-out;
  }
  
  &-fade-out {
    animation: fadeOut 0.5s ease-out;
  }
  
  &-slide-in-up {
    animation: slideInUp 0.6s $ease-out-cubic;
  }
  
  &-slide-in-down {
    animation: slideInDown 0.6s $ease-out-cubic;
  }
  
  &-slide-in-left {
    animation: slideInLeft 0.6s $ease-out-cubic;
  }
  
  &-slide-in-right {
    animation: slideInRight 0.6s $ease-out-cubic;
  }
  
  &-scale-in {
    animation: scaleIn 0.4s $ease-out-cubic;
  }
  
  &-scale-out {
    animation: scaleOut 0.4s $ease-out-cubic;
  }
  
  &-rotate-in {
    animation: rotateIn 0.6s $ease-out-cubic;
  }
  
  &-bounce-in {
    animation: bounceIn 0.8s $ease-out-cubic;
  }
  
  &-shake {
    animation: shake 0.6s ease-in-out;
  }
  
  &-pulse {
    animation: pulse 2s infinite;
  }
  
  &-spin {
    animation: spin 1s linear infinite;
  }
  
  &-heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
  }
  
  &-rubber-band {
    animation: rubberBand 1s ease-out;
  }
  
  &-swing {
    animation: swing 1s ease-in-out;
  }
  
  &-flash {
    animation: flash 2s infinite;
  }
  
  &-float {
    animation: float 3s ease-in-out infinite;
  }
}

// 动画延迟类
.animate-delay {
  &-100 {
    animation-delay: 0.1s;
  }
  
  &-200 {
    animation-delay: 0.2s;
  }
  
  &-300 {
    animation-delay: 0.3s;
  }
  
  &-500 {
    animation-delay: 0.5s;
  }
  
  &-700 {
    animation-delay: 0.7s;
  }
  
  &-1000 {
    animation-delay: 1s;
  }
}

// 动画持续时间类
.animate-duration {
  &-fast {
    animation-duration: 0.3s;
  }
  
  &-normal {
    animation-duration: 0.5s;
  }
  
  &-slow {
    animation-duration: 0.8s;
  }
  
  &-slower {
    animation-duration: 1.2s;
  }
}

// 动画重复类
.animate-repeat {
  &-1 {
    animation-iteration-count: 1;
  }
  
  &-2 {
    animation-iteration-count: 2;
  }
  
  &-3 {
    animation-iteration-count: 3;
  }
  
  &-infinite {
    animation-iteration-count: infinite;
  }
}

// ===== 悬停动画效果 =====
.hover-animate {
  transition: all 0.3s ease;
  
  &:hover {
    &.hover-lift {
      transform: translateY(-4px);
      box-shadow: $shadow-lg;
    }
    
    &.hover-scale {
      transform: scale(1.05);
    }
    
    &.hover-rotate {
      transform: rotate(5deg);
    }
    
    &.hover-skew {
      transform: skew(-5deg);
    }
    
    &.hover-bounce {
      animation: pulse 0.6s ease-in-out;
    }
    
    &.hover-shake {
      animation: shake 0.6s ease-in-out;
    }
    
    &.hover-glow {
      box-shadow: 0 0 20px rgba($primary-color, 0.4);
    }
    
    &.hover-shadow {
      box-shadow: $shadow-2xl;
    }
  }
}

// ===== 加载动画 =====
.loading {
  &-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  &-dots {
    display: inline-flex;
    gap: 0.25rem;
    
    .dot {
      width: 0.5rem;
      height: 0.5rem;
      background: currentColor;
      border-radius: 50%;
      animation: pulse 1.4s ease-in-out infinite both;
      
      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
  
  &-bars {
    display: inline-flex;
    gap: 0.125rem;
    
    .bar {
      width: 0.25rem;
      height: 1rem;
      background: currentColor;
      animation: pulse 1.2s ease-in-out infinite;
      
      &:nth-child(1) { animation-delay: -0.24s; }
      &:nth-child(2) { animation-delay: -0.12s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
}

// ===== 进度动画 =====
.progress-animate {
  position: relative;
  overflow: hidden;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

// ===== 响应式动画控制 =====
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// 移动端动画优化
@media (max-width: 768px) {
  .animate {
    &-slide-in-up,
    &-slide-in-down,
    &-slide-in-left,
    &-slide-in-right {
      animation-duration: 0.4s;
    }

    &-bounce-in,
    &-rubber-band {
      animation-duration: 0.6s;
    }
  }

  .hover-animate:hover {
    &.hover-lift {
      transform: translateY(-2px);
    }

    &.hover-scale {
      transform: scale(1.02);
    }
  }
}
