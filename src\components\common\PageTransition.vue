<template>
  <Transition
    :name="transitionName"
    mode="out-in"
    @before-enter="onBeforeEnter"
    @enter="onEnter"
    @after-enter="onAfterEnter"
    @before-leave="onBeforeLeave"
    @leave="onLeave"
    @after-leave="onAfterLeave"
  >
    <slot />
  </Transition>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'

interface Props {
  name?: string
  duration?: number
}

const props = withDefaults(defineProps<Props>(), {
  name: 'fade',
  duration: 300
})

const route = useRoute()

// 根据路由变化决定过渡动画类型
const transitionName = computed(() => {
  // 可以根据路由的层级或其他条件来决定动画类型
  return props.name
})

// 动画生命周期钩子
const onBeforeEnter = (el: Element) => {
  // 进入前的准备工作
}

const onEnter = (el: Element, done: () => void) => {
  // 进入动画
  setTimeout(done, props.duration)
}

const onAfterEnter = (el: Element) => {
  // 进入完成后的清理工作
}

const onBeforeLeave = (el: Element) => {
  // 离开前的准备工作
}

const onLeave = (el: Element, done: () => void) => {
  // 离开动画
  setTimeout(done, props.duration)
}

const onAfterLeave = (el: Element) => {
  // 离开完成后的清理工作
}
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

// 淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 滑动动画
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease-in-out;
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

// 缩放动画
.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease-in-out;
}

.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

// 向上滑动动画
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease-in-out;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

// 旋转动画
.rotate-enter-active,
.rotate-leave-active {
  transition: all 0.5s ease-in-out;
}

.rotate-enter-from {
  opacity: 0;
  transform: rotateY(90deg);
}

.rotate-leave-to {
  opacity: 0;
  transform: rotateY(-90deg);
}
</style>
