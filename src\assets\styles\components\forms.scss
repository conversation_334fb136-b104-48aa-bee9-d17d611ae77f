// 表单组件样式系统

@use '../variables' as *;
@use '../mixins' as *;

// ===== 表单容器 =====
.form {
  .form-section {
    margin-bottom: $spacing-8;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .form-header {
    margin-bottom: $spacing-6;
    
    .form-title {
      font-size: $font-size-2xl;
      font-weight: $font-weight-bold;
      color: $primary-color;
      margin-bottom: $spacing-2;
    }
    
    .form-subtitle {
      color: $gray-600;
      font-size: $font-size-base;
    }
  }
  
  .form-footer {
    margin-top: $spacing-8;
    padding-top: $spacing-6;
    border-top: 1px solid $gray-200;
  }
}

// ===== 表单组 =====
.form-group {
  margin-bottom: $spacing-6;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .form-label {
    display: block;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    color: $gray-700;
    margin-bottom: $spacing-2;
    
    .required {
      color: $error-color;
      margin-left: $spacing-1;
    }
    
    .optional {
      color: $gray-500;
      font-weight: $font-weight-normal;
      margin-left: $spacing-1;
    }
  }
  
  .form-help {
    font-size: $font-size-sm;
    color: $gray-500;
    margin-top: $spacing-1;
  }
  
  .form-error {
    font-size: $font-size-sm;
    color: $error-color;
    margin-top: $spacing-1;
    display: flex;
    align-items: center;
    gap: $spacing-1;
    
    .error-icon {
      font-size: $font-size-xs;
    }
  }
  
  .form-success {
    font-size: $font-size-sm;
    color: $success-color;
    margin-top: $spacing-1;
    display: flex;
    align-items: center;
    gap: $spacing-1;
    
    .success-icon {
      font-size: $font-size-xs;
    }
  }
}

// ===== 输入框样式 =====
.form-input {
  @include input-field;
  
  // 尺寸变体
  &--sm {
    padding: $spacing-2 $spacing-3;
    font-size: $font-size-sm;
  }
  
  &--lg {
    padding: $spacing-4 $spacing-5;
    font-size: $font-size-lg;
  }
  
  // 状态变体
  &--success {
    border-color: $success-color;
    
    &:focus {
      border-color: $success-color;
      box-shadow: 0 0 0 3px rgba($success-color, 0.1);
    }
  }
  
  &--warning {
    border-color: $warning-color;
    
    &:focus {
      border-color: $warning-color;
      box-shadow: 0 0 0 3px rgba($warning-color, 0.1);
    }
  }
  
  &--error {
    border-color: $error-color;
    
    &:focus {
      border-color: $error-color;
      box-shadow: 0 0 0 3px rgba($error-color, 0.1);
    }
  }
  
  // 圆角变体
  &--rounded {
    border-radius: $border-radius-full;
  }
  
  &--square {
    border-radius: 0;
  }
}

// ===== 文本域样式 =====
.form-textarea {
  @extend .form-input;
  resize: vertical;
  min-height: 120px;
  font-family: $font-family-primary;
  line-height: $line-height-relaxed;
}

// ===== 选择框样式 =====
.form-select {
  @extend .form-input;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right $spacing-3 center;
  background-repeat: no-repeat;
  background-size: 1rem;
  padding-right: $spacing-10;
  cursor: pointer;
  
  &:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%234299e1' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }
}

// ===== 复选框和单选框样式 =====
.form-checkbox,
.form-radio {
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid $gray-300;
  background: $white;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:checked {
    background: $primary-color;
    border-color: $primary-color;
  }
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
  }
  
  &:disabled {
    background: $gray-100;
    border-color: $gray-300;
    cursor: not-allowed;
  }
}

.form-checkbox {
  border-radius: $border-radius-sm;
  
  &:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
    background-size: 0.75rem;
    background-position: center;
    background-repeat: no-repeat;
  }
}

.form-radio {
  border-radius: 50%;
  
  &:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
    background-size: 0.5rem;
    background-position: center;
    background-repeat: no-repeat;
  }
}

// ===== 复选框和单选框标签 =====
.checkbox-label,
.radio-label {
  display: flex;
  align-items: flex-start;
  gap: $spacing-3;
  cursor: pointer;
  font-size: $font-size-sm;
  color: $gray-700;
  line-height: $line-height-relaxed;
  
  &:hover {
    .form-checkbox,
    .form-radio {
      border-color: $primary-color;
    }
  }
  
  &.disabled {
    color: $gray-500;
    cursor: not-allowed;
  }
}

// ===== 开关样式 =====
.form-switch {
  position: relative;
  display: inline-block;
  width: 3rem;
  height: 1.5rem;
  
  input {
    opacity: 0;
    width: 0;
    height: 0;
    
    &:checked + .switch-slider {
      background: $primary-color;
      
      &::before {
        transform: translateX(1.5rem);
      }
    }
    
    &:focus + .switch-slider {
      box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
    }
    
    &:disabled + .switch-slider {
      background: $gray-300;
      cursor: not-allowed;
      
      &::before {
        background: $gray-400;
      }
    }
  }
  
  .switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: $gray-300;
    border-radius: 1.5rem;
    transition: all 0.3s ease;
    
    &::before {
      content: '';
      position: absolute;
      height: 1.25rem;
      width: 1.25rem;
      left: 0.125rem;
      bottom: 0.125rem;
      background: $white;
      border-radius: 50%;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

// ===== 文件上传样式 =====
.form-file {
  .file-input {
    display: none;
  }
  
  .file-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-8;
    border: 2px dashed $gray-300;
    border-radius: $border-radius-lg;
    background: $gray-50;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: $primary-color;
      background: rgba($primary-color, 0.05);
    }
    
    .file-icon {
      font-size: $font-size-3xl;
      color: $gray-400;
      margin-bottom: $spacing-2;
    }
    
    .file-text {
      text-align: center;
      
      .file-primary {
        font-weight: $font-weight-medium;
        color: $gray-700;
        margin-bottom: $spacing-1;
      }
      
      .file-secondary {
        font-size: $font-size-sm;
        color: $gray-500;
      }
    }
  }
  
  &.dragover .file-label {
    border-color: $primary-color;
    background: rgba($primary-color, 0.1);
  }
}

// ===== 输入组样式 =====
.input-group {
  display: flex;
  
  .form-input {
    border-radius: 0;
    
    &:first-child {
      border-top-left-radius: $border-radius-md;
      border-bottom-left-radius: $border-radius-md;
    }
    
    &:last-child {
      border-top-right-radius: $border-radius-md;
      border-bottom-right-radius: $border-radius-md;
    }
    
    &:not(:last-child) {
      border-right: none;
    }
    
    &:focus {
      z-index: 1;
    }
  }
  
  .input-addon {
    display: flex;
    align-items: center;
    padding: $spacing-3 $spacing-4;
    background: $gray-100;
    border: 2px solid $gray-300;
    color: $gray-600;
    font-size: $font-size-sm;
    white-space: nowrap;
    
    &:first-child {
      border-top-left-radius: $border-radius-md;
      border-bottom-left-radius: $border-radius-md;
      border-right: none;
    }
    
    &:last-child {
      border-top-right-radius: $border-radius-md;
      border-bottom-right-radius: $border-radius-md;
      border-left: none;
    }
  }
}

// ===== 表单网格布局 =====
.form-grid {
  display: grid;
  gap: $spacing-6;
  
  &--2 {
    grid-template-columns: repeat(2, 1fr);
    
    @include mobile {
      grid-template-columns: 1fr;
    }
  }
  
  &--3 {
    grid-template-columns: repeat(3, 1fr);
    
    @include tablet {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include mobile {
      grid-template-columns: 1fr;
    }
  }
  
  &--4 {
    grid-template-columns: repeat(4, 1fr);
    
    @include desktop {
      grid-template-columns: repeat(3, 1fr);
    }
    
    @include tablet {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include mobile {
      grid-template-columns: 1fr;
    }
  }
}

// ===== 表单操作按钮 =====
.form-actions {
  display: flex;
  gap: $spacing-4;
  justify-content: flex-end;
  
  &--center {
    justify-content: center;
  }
  
  &--start {
    justify-content: flex-start;
  }
  
  &--between {
    justify-content: space-between;
  }
  
  @include mobile {
    flex-direction: column;
    
    .btn {
      width: 100%;
    }
  }
}

// ===== 响应式调整 =====
@include mobile {
  .form-group {
    margin-bottom: $spacing-4;
  }
  
  .form-header .form-title {
    font-size: $font-size-xl;
  }
  
  .input-group {
    flex-direction: column;
    
    .form-input,
    .input-addon {
      border-radius: $border-radius-md !important;
      border: 2px solid $gray-300 !important;
    }
    
    .form-input:not(:last-child),
    .input-addon:not(:last-child) {
      margin-bottom: $spacing-2;
    }
  }
}
