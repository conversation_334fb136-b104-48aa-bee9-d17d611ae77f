<template>
  <div class="image-gallery">
    <!-- 主图显示区域 -->
    <div class="gallery-main">
      <div class="main-image-container">
        <img
          :src="currentImage.src"
          :alt="currentImage.alt || `图片 ${currentIndex + 1}`"
          class="main-image"
          @click="openLightbox"
        />
        
        <!-- 图片信息覆盖层 -->
        <div v-if="showInfo && currentImage.title" class="image-info">
          <h3 class="image-title">{{ currentImage.title }}</h3>
          <p v-if="currentImage.description" class="image-description">
            {{ currentImage.description }}
          </p>
        </div>
        
        <!-- 导航箭头 -->
        <button
          v-if="showArrows && images.length > 1"
          class="gallery-arrow gallery-arrow--prev"
          @click="prev"
          aria-label="上一张图片"
        >
          <i class="icon-arrow-left"></i>
        </button>
        
        <button
          v-if="showArrows && images.length > 1"
          class="gallery-arrow gallery-arrow--next"
          @click="next"
          aria-label="下一张图片"
        >
          <i class="icon-arrow-right"></i>
        </button>
      </div>
    </div>

    <!-- 缩略图列表 -->
    <div v-if="showThumbnails && images.length > 1" class="gallery-thumbnails">
      <div class="thumbnails-container" ref="thumbnailsRef">
        <button
          v-for="(image, index) in images"
          :key="index"
          class="thumbnail-item"
          :class="{ 'active': index === currentIndex }"
          @click="goTo(index)"
        >
          <img
            :src="image.thumbnail || image.src"
            :alt="image.alt || `缩略图 ${index + 1}`"
            class="thumbnail-image"
          />
        </button>
      </div>
    </div>

    <!-- 灯箱模式 -->
    <Modal
      v-model="lightboxVisible"
      :closable="true"
      :close-on-overlay="true"
      :show-header="false"
      :show-footer="false"
      fullscreen
      no-padding
    >
      <div class="lightbox-content">
        <img
          :src="currentImage.src"
          :alt="currentImage.alt || `图片 ${currentIndex + 1}`"
          class="lightbox-image"
        />
        
        <!-- 灯箱导航 -->
        <button
          v-if="images.length > 1"
          class="lightbox-arrow lightbox-arrow--prev"
          @click="prev"
          aria-label="上一张图片"
        >
          <i class="icon-arrow-left"></i>
        </button>
        
        <button
          v-if="images.length > 1"
          class="lightbox-arrow lightbox-arrow--next"
          @click="next"
          aria-label="下一张图片"
        >
          <i class="icon-arrow-right"></i>
        </button>
        
        <!-- 图片计数器 -->
        <div class="lightbox-counter">
          {{ currentIndex + 1 }} / {{ images.length }}
        </div>
        
        <!-- 图片信息 -->
        <div v-if="currentImage.title || currentImage.description" class="lightbox-info">
          <h3 v-if="currentImage.title" class="lightbox-title">{{ currentImage.title }}</h3>
          <p v-if="currentImage.description" class="lightbox-description">
            {{ currentImage.description }}
          </p>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import Modal from './Modal.vue'

interface GalleryImage {
  src: string
  thumbnail?: string
  alt?: string
  title?: string
  description?: string
}

interface Props {
  images: GalleryImage[]
  initialIndex?: number
  showThumbnails?: boolean
  showArrows?: boolean
  showInfo?: boolean
  aspectRatio?: string
  thumbnailSize?: string
}

interface Emits {
  (e: 'change', currentIndex: number, image: GalleryImage): void
  (e: 'click', currentIndex: number, image: GalleryImage): void
}

const props = withDefaults(defineProps<Props>(), {
  initialIndex: 0,
  showThumbnails: true,
  showArrows: true,
  showInfo: true,
  aspectRatio: '16/9',
  thumbnailSize: '80px'
})

const emit = defineEmits<Emits>()

// 响应式数据
const currentIndex = ref(props.initialIndex)
const lightboxVisible = ref(false)
const thumbnailsRef = ref<HTMLElement>()

// 计算属性
const currentImage = computed(() => {
  return props.images[currentIndex.value] || { src: '', alt: '' }
})

// 方法
const goTo = (index: number) => {
  if (index >= 0 && index < props.images.length && index !== currentIndex.value) {
    currentIndex.value = index
    emit('change', currentIndex.value, currentImage.value)
    scrollThumbnailIntoView()
  }
}

const next = () => {
  const nextIndex = currentIndex.value < props.images.length - 1 
    ? currentIndex.value + 1 
    : 0
  goTo(nextIndex)
}

const prev = () => {
  const prevIndex = currentIndex.value > 0 
    ? currentIndex.value - 1 
    : props.images.length - 1
  goTo(prevIndex)
}

const openLightbox = () => {
  lightboxVisible.value = true
  emit('click', currentIndex.value, currentImage.value)
}

const scrollThumbnailIntoView = async () => {
  if (!props.showThumbnails || !thumbnailsRef.value) return
  
  await nextTick()
  
  const activeThumb = thumbnailsRef.value.querySelector('.thumbnail-item.active') as HTMLElement
  if (activeThumb) {
    activeThumb.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
      inline: 'center'
    })
  }
}

// 键盘导航
const handleKeydown = (event: KeyboardEvent) => {
  if (!lightboxVisible.value) return
  
  switch (event.key) {
    case 'ArrowLeft':
      event.preventDefault()
      prev()
      break
    case 'ArrowRight':
      event.preventDefault()
      next()
      break
  }
}

// 监听器
watch(() => props.initialIndex, (newIndex) => {
  if (newIndex >= 0 && newIndex < props.images.length) {
    currentIndex.value = newIndex
  }
})

watch(lightboxVisible, (visible) => {
  if (visible) {
    document.addEventListener('keydown', handleKeydown)
  } else {
    document.removeEventListener('keydown', handleKeydown)
  }
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.image-gallery {
  width: 100%;
}

.gallery-main {
  margin-bottom: 1rem;
}

.main-image-container {
  position: relative;
  width: 100%;
  aspect-ratio: v-bind(aspectRatio);
  border-radius: 0.5rem;
  overflow: hidden;
  background: #f7fafc;
  cursor: pointer;
}

.main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease-in-out;
  
  &:hover {
    transform: scale(1.05);
  }
}

.image-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 2rem 1.5rem 1.5rem;
  
  .image-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  
  .image-description {
    font-size: 0.875rem;
    opacity: 0.9;
    margin: 0;
  }
}

.gallery-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  z-index: 10;
  
  &:hover {
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &--prev {
    left: 1rem;
  }
  
  &--next {
    right: 1rem;
  }
}

.gallery-thumbnails {
  .thumbnails-container {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    padding: 0.5rem 0;
    scroll-behavior: smooth;
    
    &::-webkit-scrollbar {
      height: 4px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #cbd5e0;
      border-radius: 2px;
      
      &:hover {
        background: #a0aec0;
      }
    }
  }
}

.thumbnail-item {
  flex: 0 0 auto;
  width: v-bind(thumbnailSize);
  height: v-bind(thumbnailSize);
  border: 2px solid transparent;
  border-radius: 0.375rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  background: none;
  padding: 0;
  
  &:hover {
    border-color: #cbd5e0;
  }
  
  &.active {
    border-color: #1a365d;
    box-shadow: 0 0 0 2px rgba(26, 54, 93, 0.2);
  }
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// 灯箱样式
.lightbox-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.9);
}

.lightbox-image {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}

.lightbox-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 3rem;
  height: 3rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  z-index: 10;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
  
  &--prev {
    left: 2rem;
  }
  
  &--next {
    right: 2rem;
  }
}

.lightbox-counter {
  position: absolute;
  top: 2rem;
  right: 2rem;
  color: white;
  background: rgba(0, 0, 0, 0.5);
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  font-size: 0.875rem;
}

.lightbox-info {
  position: absolute;
  bottom: 2rem;
  left: 2rem;
  right: 2rem;
  color: white;
  background: rgba(0, 0, 0, 0.7);
  padding: 1.5rem;
  border-radius: 0.5rem;
  text-align: center;
  
  .lightbox-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  
  .lightbox-description {
    font-size: 0.875rem;
    opacity: 0.9;
    margin: 0;
  }
}

// 图标
.icon-arrow-left::before {
  content: '‹';
  font-size: 1.5rem;
  font-weight: bold;
}

.icon-arrow-right::before {
  content: '›';
  font-size: 1.5rem;
  font-weight: bold;
}

// 响应式
@media (max-width: 768px) {
  .gallery-arrow {
    width: 2rem;
    height: 2rem;
    
    &--prev {
      left: 0.5rem;
    }
    
    &--next {
      right: 0.5rem;
    }
  }
  
  .lightbox-arrow {
    width: 2.5rem;
    height: 2.5rem;
    
    &--prev {
      left: 1rem;
    }
    
    &--next {
      right: 1rem;
    }
  }
  
  .lightbox-counter {
    top: 1rem;
    right: 1rem;
    font-size: 0.8rem;
    padding: 0.375rem 0.75rem;
  }
  
  .lightbox-info {
    bottom: 1rem;
    left: 1rem;
    right: 1rem;
    padding: 1rem;
    
    .lightbox-title {
      font-size: 1rem;
    }
    
    .lightbox-description {
      font-size: 0.8rem;
    }
  }
  
  .image-info {
    padding: 1.5rem 1rem 1rem;
    
    .image-title {
      font-size: 1rem;
    }
    
    .image-description {
      font-size: 0.8rem;
    }
  }
}
</style>
