import{a as J,r as c,o as K,g,i,m as o,h as e,j as y,B as j,p as _,t as r,s as F,F as $,k as R,l as G,_ as X,S as ye,y as fe,c as V,q as me,e as Y,w as ge,f as P,v as pe,x as he,L as be,n as H}from"./index-Dz-_Smvi.js";import{u as Z,a as ke,A as _e}from"./AppLayout-DAG3tKFc.js";import{B as we}from"./Breadcrumb-DX9d_gU2.js";/* empty css                                                                            */import{S as Ce,F as Se}from"./FilterPanel-C3hsGuJ4.js";import{u as $e}from"./cases-D4SmhFfO.js";import"./contact-sLg3zrn9.js";const Re=["src","alt"],Te={class:"overlay-content"},Ve={class:"case-badges"},Me={key:0,class:"badge badge--new"},De={key:1,class:"badge badge--featured"},Le={key:2,class:"badge badge--award"},qe={class:"industry-tag"},Be={class:"case-info"},Ne={class:"case-header"},Ie={class:"case-title"},Ee={class:"case-meta"},Pe={class:"case-client"},Fe={class:"case-date"},Ae={key:0,class:"case-description"},Ue={key:1,class:"case-results"},ze={class:"results-list"},je={key:2,class:"case-metrics"},Ge={class:"metric-value"},Oe={class:"metric-label"},Qe={key:3,class:"case-technologies"},We={key:4,class:"case-testimonial"},Ye={class:"testimonial-content"},He={class:"testimonial-author"},Je={class:"case-footer"},Ke={class:"case-actions"},Xe={key:0,class:"featured-indicator"},Ze=J({__name:"CaseStudyCard",props:{caseStudy:{},variant:{default:"default"},featured:{type:Boolean,default:!1}},emits:["click","view-details","contact"],setup(O,{emit:A}){const w=O,C=A,{navigateToContact:q,navigateToCases:M}=Z(),D=c(),b=c(),p=c(),f=c();let n;const k=l=>({电子科技:"icon-electronics",食品饮料:"icon-food",奢侈品:"icon-luxury",医疗健康:"icon-medical",汽车:"icon-automotive",化妆品:"icon-cosmetics",服装:"icon-fashion",家居:"icon-home"})[l]||"icon-industry",d=()=>{C("click",w.caseStudy)},T=()=>{C("view-details",w.caseStudy),M(w.caseStudy.id)},h=()=>{C("contact",w.caseStudy),q()},u=()=>{const l=b.value?.querySelector("img");l&&(l.src="/images/case-default.svg")},B=()=>{n&&n.kill(),n=g.timeline(),n.to(D.value,{y:-12,scale:1.02,duration:.4,ease:"power2.out"}),n.to(b.value?.querySelector("img"),{scale:1.1,duration:.6,ease:"power2.out"},0),n.to(p.value,{opacity:1,duration:.3,ease:"power2.out"},.1),n.from(p.value?.querySelector(".overlay-content"),{y:30,opacity:0,duration:.3,ease:"power2.out"},.2),n.to(f.value,{opacity:1,duration:.3,ease:"power2.out"},0)},N=()=>{n&&n.kill(),n=g.timeline(),n.to(D.value,{y:0,scale:1,duration:.4,ease:"power2.out"}),n.to(b.value?.querySelector("img"),{scale:1,duration:.6,ease:"power2.out"},0),n.to(p.value,{opacity:0,duration:.3,ease:"power2.out"},0),n.to(f.value,{opacity:0,duration:.3,ease:"power2.out"},0)};return K(()=>{g.set(p.value,{opacity:0}),g.set(f.value,{opacity:0}),g.set(p.value?.querySelector(".overlay-content"),{y:30,opacity:0})}),(l,v)=>(o(),i("div",{class:_(["case-study-card",[`case-study-card--${l.variant}`,{"case-study-card--featured":l.featured}]]),ref_key:"cardRef",ref:D,onMouseenter:B,onMouseleave:N,onClick:d},[e("div",{class:"case-image",ref_key:"imageRef",ref:b},[e("img",{src:l.caseStudy.image||"/images/case-default.svg",alt:l.caseStudy.title,onError:u},null,40,Re),e("div",{class:"image-overlay",ref_key:"overlayRef",ref:p},[e("div",Te,[e("button",{class:"overlay-btn",onClick:j(T,["stop"])},v[0]||(v[0]=[e("i",{class:"icon-eye"},null,-1),e("span",null,"查看详情",-1)]))])],512),e("div",Ve,[l.caseStudy.isNew?(o(),i("span",Me,"最新")):y("",!0),l.featured?(o(),i("span",De,"精选")):y("",!0),l.caseStudy.award?(o(),i("span",Le,"获奖")):y("",!0)]),e("div",qe,[e("i",{class:_(k(l.caseStudy.industry))},null,2),e("span",null,r(l.caseStudy.industry),1)])],512),e("div",Be,[e("div",Ne,[e("h3",Ie,r(l.caseStudy.title),1),e("div",Ee,[e("span",Pe,r(l.caseStudy.client),1),e("span",Fe,r(F(ke)(l.caseStudy.date)),1)])]),l.variant!=="compact"?(o(),i("p",Ae,r(l.caseStudy.description),1)):y("",!0),l.caseStudy.results&&l.variant==="detailed"?(o(),i("div",Ue,[v[2]||(v[2]=e("h4",{class:"results-title"},"项目成果",-1)),e("ul",ze,[(o(!0),i($,null,R(l.caseStudy.results.slice(0,3),m=>(o(),i("li",{key:m,class:"result-item"},[v[1]||(v[1]=e("i",{class:"icon-check"},null,-1)),e("span",null,r(m),1)]))),128))])])):y("",!0),l.caseStudy.metrics&&l.variant!=="compact"?(o(),i("div",je,[(o(!0),i($,null,R(l.caseStudy.metrics.slice(0,3),m=>(o(),i("div",{key:m.label,class:"metric-item"},[e("div",Ge,r(m.value),1),e("div",Oe,r(m.label),1)]))),128))])):y("",!0),l.caseStudy.technologies?(o(),i("div",Qe,[(o(!0),i($,null,R(l.caseStudy.technologies.slice(0,3),m=>(o(),i("span",{key:m,class:"tech-tag"},r(m),1))),128))])):y("",!0),l.caseStudy.testimonial&&l.variant==="detailed"?(o(),i("div",We,[e("blockquote",Ye,' "'+r(l.caseStudy.testimonial.content)+'" ',1),e("cite",He,r(l.caseStudy.testimonial.author)+" - "+r(l.caseStudy.testimonial.position),1)])):y("",!0)]),e("div",Je,[e("div",Ke,[e("button",{class:"btn btn--secondary btn--sm",onClick:j(h,["stop"])}," 咨询类似项目 "),e("button",{class:"btn btn--primary btn--sm",onClick:j(T,["stop"])},v[3]||(v[3]=[G(" 查看详情 ",-1),e("i",{class:"icon-arrow-right"},null,-1)]))])]),e("div",{class:"hover-effect",ref_key:"hoverEffectRef",ref:f},null,512),l.featured?(o(),i("div",Xe,v[4]||(v[4]=[e("i",{class:"icon-star"},null,-1)]))):y("",!0)],34))}}),xe=X(Ze,[["__scopeId","data-v-c732853e"]]),et={class:"container"},tt={class:"hero-content"},st={class:"cases-stats"},at={class:"stat-number"},lt={class:"stat-label"},ot={class:"container"},it={class:"filters-layout"},nt={class:"industry-filters"},ct={class:"industry-buttons"},rt=["onClick"],ut={class:"count"},dt={class:"search-filters"},vt={class:"container"},yt={class:"content-header"},ft={class:"results-info"},mt={class:"results-count"},gt={key:0,class:"industry-filter"},pt={class:"content-controls"},ht={class:"sort-controls"},bt={class:"view-controls"},kt={key:0,class:"loading-container"},_t={key:1,class:"empty-state"},wt={key:2,class:"pagination-container"},Ct={class:"pagination"},St=["disabled"],$t={class:"pagination-numbers"},Rt=["onClick"],Tt=["disabled"],Vt=J({__name:"CasesPage",setup(O){g.registerPlugin(ye),$e();const{navigateToContact:A,navigateToCases:w}=Z(),C=c(),q=c(),M=c(),D=c([]),b=c([]),p=c(!1),f=c(""),n=c("default"),k=c("grid"),d=c(1),T=c(9),h=c("all"),u=c({}),B=c([{key:"totalCases",label:"成功案例",value:150,suffix:"+"},{key:"industries",label:"服务行业",value:20,suffix:"+"},{key:"satisfaction",label:"客户满意度",value:98,suffix:"%"},{key:"awards",label:"获得奖项",value:15,suffix:"+"}]),N=fe({totalCases:0,industries:0,satisfaction:0,awards:0}),l=c([{key:"all",name:"全部",icon:"icon-all",count:6},{key:"electronics",name:"电子科技",icon:"icon-electronics",count:1},{key:"food",name:"食品饮料",icon:"icon-food",count:1},{key:"luxury",name:"奢侈品",icon:"icon-luxury",count:1},{key:"medical",name:"医疗健康",icon:"icon-medical",count:1},{key:"automotive",name:"汽车",icon:"icon-automotive",count:1},{key:"cosmetics",name:"化妆品",icon:"icon-cosmetics",count:1}]),v=c([{id:"1",title:"智能手机包装设计革新",description:"为知名手机品牌设计的全新包装系列，融合科技感与环保理念，提升了品牌形象和用户体验。",client:"科技公司A",industry:"电子科技",date:"2024-01-15",image:"/images/case1.svg",featured:!0,isNew:!0,results:["包装成本降低30%","品牌认知度提升50%","客户满意度达到95%","环保评分提升40%"],metrics:[{label:"成本节省",value:"30%"},{label:"满意度",value:"95%"},{label:"环保提升",value:"40%"}],technologies:["环保材料","智能包装","防伪技术"],testimonial:{content:"新的包装设计不仅降低了成本，还大大提升了我们的品牌形象。客户反馈非常积极。",author:"张总",position:"CEO"}},{id:"2",title:"有机食品包装安全升级",description:"为有机食品品牌提供了符合最新安全标准的包装解决方案，确保产品质量和安全。",client:"绿色食品公司",industry:"食品饮料",date:"2024-02-20",image:"/images/case2.svg",results:["保鲜期延长20%","安全性提升100%","客户投诉减少80%","通过有机认证"],metrics:[{label:"保鲜延长",value:"20%"},{label:"投诉减少",value:"80%"},{label:"安全提升",value:"100%"}],technologies:["食品级材料","真空包装","可降解材料"]},{id:"3",title:"奢侈品包装定制项目",description:"为国际奢侈品牌打造了独特的包装设计，完美诠释了品牌的奢华理念和工艺精神。",client:"奢侈品牌C",industry:"奢侈品",date:"2024-03-10",image:"/images/case3.svg",featured:!0,award:!0,results:["品牌价值提升60%","包装识别度提升80%","客户复购率提升45%","获得国际设计大奖"],metrics:[{label:"品牌价值",value:"60%"},{label:"识别度",value:"80%"},{label:"复购率",value:"45%"}],technologies:["奢华材料","手工工艺","限量设计"]},{id:"4",title:"医疗器械包装标准化",description:"为医疗器械公司设计的标准化包装系统，确保产品在运输和储存过程中的安全性。",client:"医疗设备公司",industry:"医疗健康",date:"2024-04-05",image:"/images/case4.svg",results:["包装标准化100%","运输损坏率降低90%","通过医疗认证","成本优化25%"],metrics:[{label:"标准化",value:"100%"},{label:"损坏减少",value:"90%"},{label:"成本优化",value:"25%"}],technologies:["医用材料","防震设计","无菌包装"]},{id:"5",title:"汽车配件包装优化",description:"为汽车配件制造商优化包装设计，提高了包装效率和产品保护性能。",client:"汽车配件公司",industry:"汽车",date:"2024-05-12",image:"/images/case5.svg",results:["包装效率提升40%","运输成本降低35%","产品保护性提升50%","客户满意度92%"],metrics:[{label:"效率提升",value:"40%"},{label:"成本降低",value:"35%"},{label:"保护提升",value:"50%"}],technologies:["工业包装","模块化设计","防护材料"]},{id:"6",title:"化妆品包装创新设计",description:"为新兴化妆品品牌创造了独特的包装视觉识别系统，帮助品牌在竞争激烈的市场中脱颖而出。",client:"美妆品牌D",industry:"化妆品",date:"2024-06-18",image:"/images/case6.svg",isNew:!0,results:["品牌知名度提升70%","销售额增长85%","社交媒体曝光增加200%","获得包装设计奖"],metrics:[{label:"知名度",value:"70%"},{label:"销售增长",value:"85%"},{label:"曝光增加",value:"200%"}],technologies:["时尚设计","可持续材料","互动包装"]}]),m=V(()=>[{text:"电子产品",category:"行业"},{text:"食品包装",category:"行业"},{text:"奢侈品",category:"行业"},{text:"环保包装",category:"技术"},{text:"智能包装",category:"技术"}]),x=V(()=>[{key:"year",title:"项目年份",type:"checkbox",options:[{value:"2024",label:"2024年",count:6}]},{key:"technologies",title:"技术特点",type:"checkbox",options:[{value:"环保材料",label:"环保材料",count:1},{value:"智能包装",label:"智能包装",count:1},{value:"防伪技术",label:"防伪技术",count:1},{value:"可降解材料",label:"可降解材料",count:1}]},{key:"featured",title:"案例类型",type:"radio",options:[{value:"all",label:"全部案例",count:6},{value:"featured",label:"精选案例",count:2},{value:"award",label:"获奖案例",count:1}]}]),I=V(()=>{let a=[...v.value];if(h.value!=="all"){const t=W(h.value);a=a.filter(s=>s.industry===t)}if(f.value){const t=f.value.toLowerCase();a=a.filter(s=>s.title.toLowerCase().includes(t)||s.description.toLowerCase().includes(t)||s.client.toLowerCase().includes(t)||s.industry.toLowerCase().includes(t))}return u.value.year&&u.value.year.length>0&&(a=a.filter(t=>{const s=new Date(t.date).getFullYear().toString();return u.value.year.includes(s)})),u.value.technologies&&u.value.technologies.length>0&&(a=a.filter(t=>t.technologies?.some(s=>u.value.technologies.includes(s)))),u.value.featured&&u.value.featured!=="all"&&(u.value.featured==="featured"?a=a.filter(t=>t.featured):u.value.featured==="award"&&(a=a.filter(t=>t.award))),n.value!=="default"&&a.sort((t,s)=>{switch(n.value){case"date-desc":return new Date(s.date).getTime()-new Date(t.date).getTime();case"date-asc":return new Date(t.date).getTime()-new Date(s.date).getTime();case"title-asc":return t.title.localeCompare(s.title);case"title-desc":return s.title.localeCompare(t.title);default:return 0}}),a}),E=V(()=>Math.ceil(I.value.length/T.value)),Q=V(()=>{const a=(d.value-1)*T.value,t=a+T.value;return I.value.slice(a,t)}),ee=V(()=>{const a=[],t=E.value,s=d.value,S=Math.max(1,s-2),L=Math.min(t,s+2);for(let z=S;z<=L;z++)a.push(z);return a}),W=a=>{const t=l.value.find(s=>s.key===a);return t?t.name:a},te=(a,t)=>{a&&(D.value[t]=a)},se=(a,t)=>{a&&(b.value[t]=a)},ae=a=>{h.value=a,d.value=1},le=a=>{f.value=a,d.value=1},oe=a=>{u.value=a,d.value=1},ie=()=>{d.value=1},ne=a=>{w(a.id)},ce=a=>{w(a.id)},re=a=>{A()},ue=()=>{f.value="",u.value={},h.value="all",d.value=1},U=a=>{a>=1&&a<=E.value&&(d.value=a,M.value?.scrollIntoView({behavior:"smooth"}))},de=()=>{B.value.forEach(a=>{g.to(N,{[a.key]:a.value,duration:2,ease:"power2.out",scrollTrigger:{trigger:C.value,start:"top 80%",once:!0}})})},ve=async()=>{await H(),g.from(C.value?.querySelector(".hero-content")?.children||[],{y:50,opacity:0,duration:.8,stagger:.2,ease:"power2.out"}),g.from(q.value?.children||[],{y:30,opacity:0,duration:.6,stagger:.1,ease:"power2.out",delay:.3}),g.from(b.value,{y:60,opacity:0,duration:.8,stagger:.1,ease:"power2.out",scrollTrigger:{trigger:M.value,start:"top 80%",once:!0}}),de()};return me(()=>Q.value,()=>{H(()=>{g.from(b.value,{y:30,opacity:0,duration:.5,stagger:.05,ease:"power2.out"})})}),K(()=>{ve()}),(a,t)=>(o(),Y(_e,null,{default:ge(()=>[P(we),e("section",{class:"cases-hero",ref_key:"heroRef",ref:C},[e("div",et,[e("div",tt,[t[7]||(t[7]=e("h1",{class:"page-title"},"客户案例",-1)),t[8]||(t[8]=e("p",{class:"page-subtitle"}," 真实的项目案例，见证我们的专业实力和服务品质 ",-1)),e("div",st,[(o(!0),i($,null,R(B.value,(s,S)=>(o(),i("div",{key:s.label,class:"stat-item",ref_for:!0,ref:L=>te(L,S)},[e("div",at,r(N[s.key])+r(s.suffix),1),e("div",lt,r(s.label),1)]))),128))])])])],512),e("section",{class:"cases-filters",ref_key:"filtersRef",ref:q},[e("div",ot,[e("div",it,[e("div",nt,[t[9]||(t[9]=e("h3",{class:"filter-title"},"按行业筛选",-1)),e("div",ct,[(o(!0),i($,null,R(l.value,s=>(o(),i("button",{key:s.key,class:_(["industry-btn",{active:h.value===s.key}]),onClick:S=>ae(s.key)},[e("i",{class:_(s.icon)},null,2),e("span",null,r(s.name),1),e("span",ut,"("+r(s.count)+")",1)],10,rt))),128))])]),e("div",dt,[P(F(Ce),{modelValue:f.value,"onUpdate:modelValue":t[0]||(t[0]=s=>f.value=s),placeholder:"搜索案例...",suggestions:m.value,onSearch:le},null,8,["modelValue","suggestions"]),P(F(Se),{modelValue:u.value,"onUpdate:modelValue":t[1]||(t[1]=s=>u.value=s),"filter-groups":x.value,onChange:oe},null,8,["modelValue","filter-groups"])])])])],512),e("section",{class:"cases-content",ref_key:"contentRef",ref:M},[e("div",vt,[e("div",yt,[e("div",ft,[e("span",mt,"找到 "+r(I.value.length)+" 个案例",1),h.value!=="all"?(o(),i("span",gt," 行业："+r(W(h.value)),1)):y("",!0)]),e("div",pt,[e("div",ht,[t[11]||(t[11]=e("label",{for:"sort-select",class:"sort-label"},"排序：",-1)),pe(e("select",{id:"sort-select","onUpdate:modelValue":t[2]||(t[2]=s=>n.value=s),class:"sort-select",onChange:ie},t[10]||(t[10]=[e("option",{value:"default"},"默认排序",-1),e("option",{value:"date-desc"},"最新案例",-1),e("option",{value:"date-asc"},"最早案例",-1),e("option",{value:"title-asc"},"标题升序",-1),e("option",{value:"title-desc"},"标题降序",-1)]),544),[[he,n.value]])]),e("div",bt,[e("button",{class:_(["view-btn",{active:k.value==="grid"}]),onClick:t[3]||(t[3]=s=>k.value="grid"),"aria-label":"网格视图"},t[12]||(t[12]=[e("i",{class:"icon-grid"},null,-1)]),2),e("button",{class:_(["view-btn",{active:k.value==="masonry"}]),onClick:t[4]||(t[4]=s=>k.value="masonry"),"aria-label":"瀑布流视图"},t[13]||(t[13]=[e("i",{class:"icon-masonry"},null,-1)]),2)])])]),e("div",{class:_(["cases-grid",[`cases-grid--${k.value}`,{loading:p.value}]])},[(o(!0),i($,null,R(Q.value,(s,S)=>(o(),Y(xe,{key:s.id,"case-study":s,variant:k.value==="masonry"?"detailed":"default",featured:s.featured,ref_for:!0,ref:L=>se(L,S),onClick:ne,onViewDetails:ce,onContact:re},null,8,["case-study","variant","featured"]))),128))],2),p.value?(o(),i("div",kt,[P(F(be))])):I.value.length===0?(o(),i("div",_t,[t[14]||(t[14]=e("div",{class:"empty-icon"},[e("i",{class:"icon-search"})],-1)),t[15]||(t[15]=e("h3",{class:"empty-title"},"未找到相关案例",-1)),t[16]||(t[16]=e("p",{class:"empty-description"}," 请尝试调整搜索条件或筛选器 ",-1)),e("button",{class:"btn btn--primary",onClick:ue}," 清除筛选条件 ")])):y("",!0),E.value>1?(o(),i("div",wt,[e("div",Ct,[e("button",{class:"pagination-btn",disabled:d.value===1,onClick:t[5]||(t[5]=s=>U(d.value-1))},t[17]||(t[17]=[e("i",{class:"icon-arrow-left"},null,-1),G(" 上一页 ",-1)]),8,St),e("div",$t,[(o(!0),i($,null,R(ee.value,s=>(o(),i("button",{key:s,class:_(["pagination-number",{active:s===d.value}]),onClick:S=>U(s)},r(s),11,Rt))),128))]),e("button",{class:"pagination-btn",disabled:d.value===E.value,onClick:t[6]||(t[6]=s=>U(d.value+1))},t[18]||(t[18]=[G(" 下一页 ",-1),e("i",{class:"icon-arrow-right"},null,-1)]),8,Tt)])])):y("",!0)])],512)]),_:1}))}}),Et=X(Vt,[["__scopeId","data-v-24bec364"]]);export{Et as default};
