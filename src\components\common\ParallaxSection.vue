<template>
  <div 
    class="parallax-section"
    :class="[
      `parallax-section--${direction}`,
      { 'parallax-section--fixed': fixed }
    ]"
    ref="sectionRef"
  >
    <!-- 视差背景层 -->
    <div 
      class="parallax-background"
      ref="backgroundRef"
      :style="backgroundStyle"
    >
      <div class="parallax-overlay" :style="overlayStyle"></div>
    </div>
    
    <!-- 视差内容层 -->
    <div 
      class="parallax-content"
      ref="contentRef"
    >
      <slot />
    </div>
    
    <!-- 装饰元素 -->
    <div class="parallax-decorations" ref="decorationsRef">
      <div 
        v-for="(decoration, index) in decorations"
        :key="index"
        class="decoration-item"
        :class="`decoration-item--${decoration.type}`"
        :style="getDecorationStyle(decoration, index)"
        :ref="el => setDecorationRef(el, index)"
      >
        <i v-if="decoration.icon" :class="decoration.icon"></i>
        <div v-else-if="decoration.shape" :class="`shape-${decoration.shape}`"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

// 注册插件
gsap.registerPlugin(ScrollTrigger)

interface Decoration {
  type: 'icon' | 'shape' | 'image'
  icon?: string
  shape?: 'circle' | 'square' | 'triangle' | 'star'
  image?: string
  size?: number
  color?: string
  opacity?: number
  speed?: number
  x?: number
  y?: number
  rotation?: number
}

interface Props {
  // 视差设置
  speed?: number
  direction?: 'up' | 'down' | 'left' | 'right'
  fixed?: boolean
  
  // 背景设置
  backgroundImage?: string
  backgroundColor?: string
  overlayColor?: string
  overlayOpacity?: number
  
  // 高度设置
  height?: string
  minHeight?: string
  
  // 装饰元素
  decorations?: Decoration[]
  
  // 动画设置
  animateContent?: boolean
  contentAnimation?: 'fadeIn' | 'slideUp' | 'slideDown' | 'scaleIn'
  contentDelay?: number
  
  // 触发设置
  trigger?: string
  start?: string
  end?: string
  scrub?: boolean | number
}

const props = withDefaults(defineProps<Props>(), {
  speed: 0.5,
  direction: 'up',
  fixed: false,
  overlayOpacity: 0.3,
  height: '100vh',
  minHeight: '400px',
  decorations: () => [],
  animateContent: true,
  contentAnimation: 'fadeIn',
  contentDelay: 0,
  start: 'top bottom',
  end: 'bottom top',
  scrub: true
})

// 模板引用
const sectionRef = ref<HTMLElement>()
const backgroundRef = ref<HTMLElement>()
const contentRef = ref<HTMLElement>()
const decorationsRef = ref<HTMLElement>()
const decorationRefs = ref<HTMLElement[]>([])

// 动画实例
const scrollTriggers = ref<ScrollTrigger[]>([])

// 计算属性
const backgroundStyle = computed(() => ({
  backgroundImage: props.backgroundImage ? `url(${props.backgroundImage})` : undefined,
  backgroundColor: props.backgroundColor,
  height: props.height,
  minHeight: props.minHeight
}))

const overlayStyle = computed(() => ({
  backgroundColor: props.overlayColor,
  opacity: props.overlayOpacity
}))

// 方法
const setDecorationRef = (el: HTMLElement | null, index: number) => {
  if (el) {
    decorationRefs.value[index] = el
  }
}

const getDecorationStyle = (decoration: Decoration, index: number) => ({
  fontSize: decoration.size ? `${decoration.size}px` : undefined,
  color: decoration.color,
  opacity: decoration.opacity,
  left: decoration.x ? `${decoration.x}%` : `${Math.random() * 80 + 10}%`,
  top: decoration.y ? `${decoration.y}%` : `${Math.random() * 80 + 10}%`,
  transform: decoration.rotation ? `rotate(${decoration.rotation}deg)` : undefined
})

const initParallax = () => {
  if (!sectionRef.value || !backgroundRef.value) return

  // 背景视差效果
  const backgroundST = ScrollTrigger.create({
    trigger: props.trigger || sectionRef.value,
    start: props.start,
    end: props.end,
    scrub: props.scrub,
    onUpdate: (self) => {
      const progress = self.progress
      let yPos = 0

      switch (props.direction) {
        case 'up':
          yPos = progress * props.speed * -100
          break
        case 'down':
          yPos = progress * props.speed * 100
          break
        case 'left':
          gsap.set(backgroundRef.value, { x: progress * props.speed * -100 })
          return
        case 'right':
          gsap.set(backgroundRef.value, { x: progress * props.speed * 100 })
          return
      }

      gsap.set(backgroundRef.value, { y: yPos })
    }
  })

  scrollTriggers.value.push(backgroundST)

  // 装饰元素视差效果
  decorationRefs.value.forEach((decoration, index) => {
    if (!decoration) return

    const decorationData = props.decorations[index]
    const speed = decorationData?.speed || (Math.random() * 0.5 + 0.2)

    const decorationST = ScrollTrigger.create({
      trigger: props.trigger || sectionRef.value,
      start: props.start,
      end: props.end,
      scrub: props.scrub,
      onUpdate: (self) => {
        const progress = self.progress
        const yPos = progress * speed * 100

        gsap.set(decoration, {
          y: yPos,
          rotation: progress * 360 * speed
        })
      }
    })

    scrollTriggers.value.push(decorationST)
  })
}

const initContentAnimation = () => {
  if (!props.animateContent || !contentRef.value) return

  let fromProps: any = {}
  let toProps: any = {}

  switch (props.contentAnimation) {
    case 'fadeIn':
      fromProps = { opacity: 0 }
      toProps = { opacity: 1 }
      break
    case 'slideUp':
      fromProps = { opacity: 0, y: 50 }
      toProps = { opacity: 1, y: 0 }
      break
    case 'slideDown':
      fromProps = { opacity: 0, y: -50 }
      toProps = { opacity: 1, y: 0 }
      break
    case 'scaleIn':
      fromProps = { opacity: 0, scale: 0.8 }
      toProps = { opacity: 1, scale: 1 }
      break
  }

  gsap.set(contentRef.value, fromProps)

  const contentST = ScrollTrigger.create({
    trigger: props.trigger || sectionRef.value,
    start: 'top 80%',
    once: true,
    onEnter: () => {
      gsap.to(contentRef.value, {
        ...toProps,
        duration: 1,
        delay: props.contentDelay,
        ease: 'power2.out'
      })
    }
  })

  scrollTriggers.value.push(contentST)
}

const initDecorationAnimations = () => {
  decorationRefs.value.forEach((decoration, index) => {
    if (!decoration) return

    // 初始动画
    gsap.fromTo(decoration,
      {
        opacity: 0,
        scale: 0
      },
      {
        opacity: props.decorations[index]?.opacity || 0.7,
        scale: 1,
        duration: 0.8,
        delay: index * 0.1,
        ease: 'back.out(1.7)'
      }
    )

    // 悬停动画
    decoration.addEventListener('mouseenter', () => {
      gsap.to(decoration, {
        scale: 1.2,
        duration: 0.3,
        ease: 'power2.out'
      })
    })

    decoration.addEventListener('mouseleave', () => {
      gsap.to(decoration, {
        scale: 1,
        duration: 0.3,
        ease: 'power2.out'
      })
    })
  })
}

const cleanup = () => {
  scrollTriggers.value.forEach(st => st.kill())
  scrollTriggers.value = []
}

// 生命周期
onMounted(async () => {
  await nextTick()
  initParallax()
  initContentAnimation()
  initDecorationAnimations()
})

onUnmounted(() => {
  cleanup()
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.parallax-section {
  position: relative;
  overflow: hidden;
  
  &--fixed {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: -1;
  }
}

.parallax-background {
  position: absolute;
  top: -20%;
  left: -20%;
  width: 140%;
  height: 140%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  will-change: transform;
}

.parallax-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
}

.parallax-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-8;
  will-change: transform;
}

.parallax-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-item {
  position: absolute;
  will-change: transform;
  pointer-events: auto;
  cursor: pointer;
  
  &--icon {
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.7);
  }
  
  &--shape {
    .shape-circle {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
    }
    
    .shape-square {
      width: 20px;
      height: 20px;
      background: rgba(255, 255, 255, 0.3);
    }
    
    .shape-triangle {
      width: 0;
      height: 0;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-bottom: 20px solid rgba(255, 255, 255, 0.3);
    }
    
    .shape-star {
      position: relative;
      width: 20px;
      height: 20px;
      
      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        background: rgba(255, 255, 255, 0.3);
      }
      
      &::before {
        transform: rotate(45deg);
      }
      
      &::after {
        transform: rotate(-45deg);
      }
    }
  }
  
  &--image {
    img {
      max-width: 100%;
      height: auto;
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .parallax-content {
    padding: $spacing-4;
  }
  
  .decoration-item {
    &--icon {
      font-size: 1.5rem;
    }
  }
}

// 减少动画偏好
@media (prefers-reduced-motion: reduce) {
  .parallax-background,
  .parallax-content,
  .decoration-item {
    transform: none !important;
  }
}
</style>
