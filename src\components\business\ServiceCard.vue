<template>
  <div 
    class="service-card" 
    :class="[
      `service-card--${variant}`,
      { 'service-card--featured': featured }
    ]"
    ref="cardRef"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @click="handleClick"
  >
    <!-- 卡片背景装饰 -->
    <div class="card-background">
      <div class="background-pattern"></div>
      <div class="background-gradient"></div>
    </div>

    <!-- 服务图标 -->
    <div class="service-icon" ref="iconRef">
      <i :class="service.icon"></i>
      <div class="icon-glow"></div>
    </div>

    <!-- 服务内容 -->
    <div class="service-content">
      <div class="service-header">
        <h3 class="service-title">{{ service.title }}</h3>
        <span v-if="service.category" class="service-category">
          {{ service.category }}
        </span>
      </div>

      <p class="service-description">{{ service.description }}</p>

      <!-- 服务特点 -->
      <div v-if="service.features && service.features.length > 0" class="service-features">
        <h4 class="features-title">核心特点</h4>
        <ul class="features-list">
          <li 
            v-for="(feature, index) in service.features.slice(0, maxFeatures)" 
            :key="index"
            class="feature-item"
          >
            <i class="icon-check"></i>
            <span>{{ feature }}</span>
          </li>
        </ul>
      </div>

      <!-- 价格信息 -->
      <div v-if="service.pricing" class="service-pricing">
        <div class="price-label">起始价格</div>
        <div class="price-value">
          <span class="currency">¥</span>
          <span class="amount">{{ formatPrice(service.pricing.startingPrice) }}</span>
          <span class="unit">{{ service.pricing.unit }}</span>
        </div>
      </div>

      <!-- 服务标签 -->
      <div v-if="service.tags && service.tags.length > 0" class="service-tags">
        <span 
          v-for="tag in service.tags.slice(0, 3)" 
          :key="tag" 
          class="service-tag"
        >
          {{ tag }}
        </span>
      </div>
    </div>

    <!-- 卡片底部操作 -->
    <div class="service-footer">
      <div class="service-meta">
        <div v-if="service.duration" class="meta-item">
          <i class="icon-clock"></i>
          <span>{{ service.duration }}</span>
        </div>
        <div v-if="service.rating" class="meta-item">
          <div class="rating">
            <i 
              v-for="star in 5" 
              :key="star"
              class="star"
              :class="{ 'filled': star <= Math.floor(service.rating) }"
            ></i>
            <span class="rating-value">({{ service.rating }})</span>
          </div>
        </div>
      </div>

      <div class="service-actions">
        <button class="btn btn--secondary btn--sm" @click.stop="handleQuote">
          获取报价
        </button>
        <button class="btn btn--primary btn--sm" @click.stop="handleLearnMore">
          了解详情
          <i class="icon-arrow-right"></i>
        </button>
      </div>
    </div>

    <!-- 悬停效果遮罩 -->
    <div class="hover-overlay" ref="overlayRef"></div>

    <!-- 特色标签 -->
    <div v-if="featured" class="featured-badge">
      <i class="icon-star"></i>
      <span>推荐</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { gsap } from 'gsap'
import { useNavigation } from '@/composables'
import { formatNumber } from '@/utils'
import type { Service } from '@/types'

interface Props {
  service: Service
  variant?: 'default' | 'compact' | 'detailed'
  featured?: boolean
  maxFeatures?: number
}

interface Emits {
  (e: 'click', service: Service): void
  (e: 'quote', service: Service): void
  (e: 'learn-more', service: Service): void
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  featured: false,
  maxFeatures: 4
})

const emit = defineEmits<Emits>()

const { navigateToServices } = useNavigation()

// 模板引用
const cardRef = ref<HTMLElement>()
const iconRef = ref<HTMLElement>()
const overlayRef = ref<HTMLElement>()

// 动画时间线
let hoverTimeline: gsap.core.Timeline

// 方法
const formatPrice = (price: number): string => {
  return formatNumber(price)
}

const handleClick = () => {
  emit('click', props.service)
  navigateToServices(props.service.category)
}

const handleQuote = () => {
  emit('quote', props.service)
}

const handleLearnMore = () => {
  emit('learn-more', props.service)
  navigateToServices(props.service.category)
}

const handleMouseEnter = () => {
  if (hoverTimeline) {
    hoverTimeline.kill()
  }

  hoverTimeline = gsap.timeline()

  // 卡片整体效果
  hoverTimeline.to(cardRef.value, {
    y: -8,
    scale: 1.02,
    duration: 0.3,
    ease: 'power2.out'
  })

  // 图标动画
  hoverTimeline.to(iconRef.value, {
    scale: 1.1,
    rotation: 5,
    duration: 0.3,
    ease: 'power2.out'
  }, 0)

  // 遮罩层动画
  hoverTimeline.to(overlayRef.value, {
    opacity: 1,
    duration: 0.3,
    ease: 'power2.out'
  }, 0)

  // 图标发光效果
  hoverTimeline.to(iconRef.value?.querySelector('.icon-glow'), {
    scale: 1.5,
    opacity: 0.6,
    duration: 0.3,
    ease: 'power2.out'
  }, 0)
}

const handleMouseLeave = () => {
  if (hoverTimeline) {
    hoverTimeline.kill()
  }

  hoverTimeline = gsap.timeline()

  // 恢复卡片状态
  hoverTimeline.to(cardRef.value, {
    y: 0,
    scale: 1,
    duration: 0.3,
    ease: 'power2.out'
  })

  // 恢复图标状态
  hoverTimeline.to(iconRef.value, {
    scale: 1,
    rotation: 0,
    duration: 0.3,
    ease: 'power2.out'
  }, 0)

  // 隐藏遮罩层
  hoverTimeline.to(overlayRef.value, {
    opacity: 0,
    duration: 0.3,
    ease: 'power2.out'
  }, 0)

  // 隐藏图标发光
  hoverTimeline.to(iconRef.value?.querySelector('.icon-glow'), {
    scale: 1,
    opacity: 0,
    duration: 0.3,
    ease: 'power2.out'
  }, 0)
}

// 生命周期
onMounted(() => {
  // 初始化动画
  gsap.set(overlayRef.value, { opacity: 0 })
  gsap.set(iconRef.value?.querySelector('.icon-glow'), { opacity: 0 })
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.service-card {
  position: relative;
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  border: 1px solid #f7fafc;

  &--featured {
    border-color: #ff6b35;
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.15);
  }

  &--compact {
    padding: 1.5rem;

    .service-content {
      .service-description {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }

  &--detailed {
    padding: 2.5rem;
  }
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;

  .background-pattern {
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background-image: 
      radial-gradient(circle at 20% 80%, rgba(26, 54, 93, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.03) 0%, transparent 50%);
    transform: rotate(45deg);
  }

  .background-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 107, 53, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
}

.service-icon {
  position: relative;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #1a365d 0%, #2c5282 100%);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  z-index: 2;

  i {
    font-size: 1.5rem;
    color: white;
    z-index: 2;
  }

  .icon-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 107, 53, 0.4) 0%, transparent 70%);
    border-radius: 1rem;
    z-index: 1;
  }

  .service-card--featured & {
    background: linear-gradient(135deg, #ff6b35 0%, #ff8a65 100%);
  }
}

.service-content {
  position: relative;
  z-index: 2;
  margin-bottom: 1.5rem;
}

.service-header {
  margin-bottom: 1rem;

  .service-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a365d;
    margin-bottom: 0.5rem;
    line-height: 1.3;
  }

  .service-category {
    display: inline-block;
    background: rgba(26, 54, 93, 0.1);
    color: #1a365d;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

.service-description {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.service-features {
  margin-bottom: 1.5rem;

  .features-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.75rem;
  }

  .features-list {
    list-style: none;
    padding: 0;
    margin: 0;

    .feature-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;
      font-size: 0.8rem;
      color: #718096;

      &:last-child {
        margin-bottom: 0;
      }

      .icon-check {
        color: #38a169;
        font-size: 0.7rem;
        flex-shrink: 0;
      }
    }
  }
}

.service-pricing {
  background: #f7fafc;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
  text-align: center;

  .price-label {
    font-size: 0.75rem;
    color: #718096;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .price-value {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.25rem;

    .currency {
      font-size: 0.875rem;
      color: #4a5568;
    }

    .amount {
      font-size: 1.5rem;
      font-weight: 700;
      color: #1a365d;
    }

    .unit {
      font-size: 0.75rem;
      color: #718096;
    }
  }
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;

  .service-tag {
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
  }
}

.service-footer {
  position: relative;
  z-index: 2;
  padding-top: 1rem;
  border-top: 1px solid #f7fafc;

  .service-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.75rem;
    color: #718096;

    .meta-item {
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }

    .rating {
      display: flex;
      align-items: center;
      gap: 0.25rem;

      .star {
        color: #e2e8f0;
        font-size: 0.7rem;

        &.filled {
          color: #ffd700;
        }

        &::before {
          content: '★';
        }
      }

      .rating-value {
        margin-left: 0.25rem;
        font-size: 0.7rem;
      }
    }
  }

  .service-actions {
    display: flex;
    gap: 0.75rem;

    .btn {
      flex: 1;
      font-size: 0.8rem;
      padding: 0.5rem 1rem;
    }
  }
}

.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(26, 54, 93, 0.05) 0%, rgba(255, 107, 53, 0.05) 100%);
  pointer-events: none;
  z-index: 1;
}

.featured-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8a65 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.7rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  z-index: 3;

  .icon-star::before {
    content: '★';
  }
}

// 图标
.icon-check::before { content: '✓'; }
.icon-arrow-right::before { content: '→'; }
.icon-clock::before { content: '🕒'; }

// 响应式
@media (max-width: 640px) {
  .service-card {
    padding: 1.5rem;

    &--detailed {
      padding: 1.5rem;
    }
  }

  .service-footer {
    .service-actions {
      flex-direction: column;

      .btn {
        width: 100%;
      }
    }
  }
}
</style>
