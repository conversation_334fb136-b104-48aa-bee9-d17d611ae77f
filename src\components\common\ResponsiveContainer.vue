<template>
  <div 
    :class="containerClasses"
    :style="containerStyles"
    ref="containerRef"
  >
    <slot 
      :breakpoint="currentBreakpoint"
      :isMobile="isMobile"
      :isTablet="isTablet"
      :isDesktop="isDesktop"
      :deviceInfo="deviceInfo"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useGlobalResponsive } from '@/composables/useResponsive'

interface Props {
  // 容器类型
  type?: 'container' | 'fluid' | 'section' | 'wrapper'
  
  // 最大宽度
  maxWidth?: string | number
  
  // 内边距
  padding?: string | number | {
    xs?: string | number
    sm?: string | number
    md?: string | number
    lg?: string | number
    xl?: string | number
    xxl?: string | number
  }
  
  // 外边距
  margin?: string | number | {
    xs?: string | number
    sm?: string | number
    md?: string | number
    lg?: string | number
    xl?: string | number
    xxl?: string | number
  }
  
  // 网格列数
  columns?: number | {
    xs?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
    xxl?: number
  }
  
  // 间距
  gap?: string | number | {
    xs?: string | number
    sm?: string | number
    md?: string | number
    lg?: string | number
    xl?: string | number
    xxl?: string | number
  }
  
  // 对齐方式
  align?: 'left' | 'center' | 'right'
  verticalAlign?: 'top' | 'center' | 'bottom'
  
  // 显示控制
  hideOn?: string[] // 在指定断点隐藏
  showOn?: string[] // 只在指定断点显示
  
  // 自定义类名
  customClass?: string
  
  // 是否启用容器查询
  containerQuery?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'container',
  align: 'center',
  verticalAlign: 'top',
  containerQuery: false
})

// 响应式工具
const responsive = useGlobalResponsive()
const { currentBreakpoint, isMobile, isTablet, isDesktop, deviceInfo } = responsive

// 模板引用
const containerRef = ref<HTMLElement>()

// 计算容器类名
const containerClasses = computed(() => {
  const classes = [
    'responsive-container',
    `responsive-container--${props.type}`,
    `responsive-container--${currentBreakpoint.value}`,
    `responsive-container--align-${props.align}`,
    `responsive-container--valign-${props.verticalAlign}`
  ]
  
  // 设备类型类名
  if (isMobile.value) classes.push('responsive-container--mobile')
  if (isTablet.value) classes.push('responsive-container--tablet')
  if (isDesktop.value) classes.push('responsive-container--desktop')
  
  // 显示/隐藏控制
  if (props.hideOn?.includes(currentBreakpoint.value)) {
    classes.push('responsive-container--hidden')
  }
  
  if (props.showOn && !props.showOn.includes(currentBreakpoint.value)) {
    classes.push('responsive-container--hidden')
  }
  
  // 自定义类名
  if (props.customClass) {
    classes.push(props.customClass)
  }
  
  return classes
})

// 计算容器样式
const containerStyles = computed(() => {
  const styles: Record<string, any> = {}
  
  // 最大宽度
  if (props.maxWidth) {
    styles.maxWidth = typeof props.maxWidth === 'number' 
      ? `${props.maxWidth}px` 
      : props.maxWidth
  }
  
  // 响应式内边距
  if (props.padding) {
    if (typeof props.padding === 'object') {
      const paddingValue = responsive.getResponsiveValue(props.padding)
      if (paddingValue !== undefined) {
        styles.padding = typeof paddingValue === 'number' 
          ? `${paddingValue}px` 
          : paddingValue
      }
    } else {
      styles.padding = typeof props.padding === 'number' 
        ? `${props.padding}px` 
        : props.padding
    }
  }
  
  // 响应式外边距
  if (props.margin) {
    if (typeof props.margin === 'object') {
      const marginValue = responsive.getResponsiveValue(props.margin)
      if (marginValue !== undefined) {
        styles.margin = typeof marginValue === 'number' 
          ? `${marginValue}px` 
          : marginValue
      }
    } else {
      styles.margin = typeof props.margin === 'number' 
        ? `${props.margin}px` 
        : props.margin
    }
  }
  
  // 网格列数
  if (props.columns) {
    if (typeof props.columns === 'object') {
      const columnsValue = responsive.getResponsiveValue(props.columns)
      if (columnsValue !== undefined) {
        styles.gridTemplateColumns = `repeat(${columnsValue}, 1fr)`
        styles.display = 'grid'
      }
    } else {
      styles.gridTemplateColumns = `repeat(${props.columns}, 1fr)`
      styles.display = 'grid'
    }
  }
  
  // 间距
  if (props.gap) {
    if (typeof props.gap === 'object') {
      const gapValue = responsive.getResponsiveValue(props.gap)
      if (gapValue !== undefined) {
        styles.gap = typeof gapValue === 'number' 
          ? `${gapValue}px` 
          : gapValue
      }
    } else {
      styles.gap = typeof props.gap === 'number' 
        ? `${props.gap}px` 
        : props.gap
    }
  }
  
  return styles
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.responsive-container {
  width: 100%;
  
  // 容器类型
  &--container {
    margin-left: auto;
    margin-right: auto;
    padding-left: $spacing-4;
    padding-right: $spacing-4;
    
    @media (min-width: 640px) {
      padding-left: $spacing-6;
      padding-right: $spacing-6;
    }
    
    @media (min-width: 1024px) {
      padding-left: $spacing-8;
      padding-right: $spacing-8;
    }
  }
  
  &--fluid {
    width: 100%;
    max-width: none;
  }
  
  &--section {
    padding-top: $spacing-8;
    padding-bottom: $spacing-8;
    
    @media (min-width: 768px) {
      padding-top: $spacing-12;
      padding-bottom: $spacing-12;
    }
    
    @media (min-width: 1024px) {
      padding-top: $spacing-16;
      padding-bottom: $spacing-16;
    }
  }
  
  &--wrapper {
    position: relative;
    overflow: hidden;
  }
  
  // 对齐方式
  &--align-left {
    margin-left: 0;
    margin-right: auto;
  }
  
  &--align-center {
    margin-left: auto;
    margin-right: auto;
  }
  
  &--align-right {
    margin-left: auto;
    margin-right: 0;
  }
  
  // 垂直对齐
  &--valign-top {
    align-items: flex-start;
  }
  
  &--valign-center {
    align-items: center;
  }
  
  &--valign-bottom {
    align-items: flex-end;
  }
  
  // 隐藏状态
  &--hidden {
    display: none !important;
  }
  
  // 断点特定样式
  &--xs {
    max-width: 100%;
  }
  
  &--sm {
    max-width: 640px;
  }
  
  &--md {
    max-width: 768px;
  }
  
  &--lg {
    max-width: 1024px;
  }
  
  &--xl {
    max-width: 1280px;
  }
  
  &--xxl {
    max-width: 1536px;
  }
  
  // 设备特定样式
  &--mobile {
    // 移动端特定样式
    .responsive-container--section & {
      padding-top: $spacing-6;
      padding-bottom: $spacing-6;
    }
  }
  
  &--tablet {
    // 平板端特定样式
    .responsive-container--section & {
      padding-top: $spacing-10;
      padding-bottom: $spacing-10;
    }
  }
  
  &--desktop {
    // 桌面端特定样式
    .responsive-container--section & {
      padding-top: $spacing-20;
      padding-bottom: $spacing-20;
    }
  }
}

// 容器查询支持
@supports (container-type: inline-size) {
  .responsive-container {
    container-type: inline-size;
    
    // 容器查询样式
    @container (max-width: 400px) {
      .responsive-container--container {
        padding-left: $spacing-2;
        padding-right: $spacing-2;
      }
    }
    
    @container (min-width: 600px) {
      .responsive-container--container {
        padding-left: $spacing-6;
        padding-right: $spacing-6;
      }
    }
    
    @container (min-width: 900px) {
      .responsive-container--container {
        padding-left: $spacing-8;
        padding-right: $spacing-8;
      }
    }
  }
}

// 打印样式
@media print {
  .responsive-container {
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    
    &--hidden {
      display: block !important;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .responsive-container {
    border: 1px solid transparent;
  }
}

// 减少动画偏好
@media (prefers-reduced-motion: reduce) {
  .responsive-container {
    transition: none !important;
  }
}
</style>
