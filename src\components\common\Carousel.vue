<template>
  <div class="carousel" :class="{ 'carousel--vertical': vertical }">
    <div class="carousel-container" ref="containerRef">
      <div
        class="carousel-track"
        :style="trackStyle"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <div
          v-for="(item, index) in items"
          :key="getItemKey(item, index)"
          class="carousel-item"
          :class="{ 'active': index === currentIndex }"
        >
          <slot :item="item" :index="index" :active="index === currentIndex">
            <div class="default-item">
              <img v-if="item.image" :src="item.image" :alt="item.title || `轮播项 ${index + 1}`" />
              <div v-if="item.title || item.description" class="item-content">
                <h3 v-if="item.title" class="item-title">{{ item.title }}</h3>
                <p v-if="item.description" class="item-description">{{ item.description }}</p>
              </div>
            </div>
          </slot>
        </div>
      </div>
    </div>

    <!-- 导航箭头 -->
    <button
      v-if="showArrows && items.length > 1"
      class="carousel-arrow carousel-arrow--prev"
      :class="{ 'disabled': currentIndex === 0 && !loop }"
      @click="prev"
      aria-label="上一项"
    >
      <i class="icon-arrow-left"></i>
    </button>
    
    <button
      v-if="showArrows && items.length > 1"
      class="carousel-arrow carousel-arrow--next"
      :class="{ 'disabled': currentIndex === items.length - 1 && !loop }"
      @click="next"
      aria-label="下一项"
    >
      <i class="icon-arrow-right"></i>
    </button>

    <!-- 指示器 -->
    <div v-if="showDots && items.length > 1" class="carousel-dots">
      <button
        v-for="(item, index) in items"
        :key="index"
        class="carousel-dot"
        :class="{ 'active': index === currentIndex }"
        @click="goTo(index)"
        :aria-label="`跳转到第 ${index + 1} 项`"
      ></button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import type { CarouselItem } from '@/types'

interface Props {
  items: CarouselItem[]
  autoplay?: boolean
  interval?: number
  loop?: boolean
  showArrows?: boolean
  showDots?: boolean
  vertical?: boolean
  height?: string
  itemKey?: string
}

interface Emits {
  (e: 'change', currentIndex: number, prevIndex: number): void
  (e: 'click', item: CarouselItem, index: number): void
}

const props = withDefaults(defineProps<Props>(), {
  autoplay: false,
  interval: 3000,
  loop: true,
  showArrows: true,
  showDots: true,
  vertical: false,
  height: '300px',
  itemKey: 'id'
})

const emit = defineEmits<Emits>()

// 响应式数据
const currentIndex = ref(0)
const containerRef = ref<HTMLElement>()
const autoplayTimer = ref<NodeJS.Timeout>()
const touchStartX = ref(0)
const touchStartY = ref(0)
const touchEndX = ref(0)
const touchEndY = ref(0)

// 计算属性
const trackStyle = computed(() => {
  const translateValue = -currentIndex.value * 100
  const translateProperty = props.vertical ? 'translateY' : 'translateX'
  
  return {
    transform: `${translateProperty}(${translateValue}%)`,
    height: props.vertical ? `${props.items.length * 100}%` : '100%'
  }
})

// 方法
const getItemKey = (item: CarouselItem, index: number): string | number => {
  return props.itemKey && item[props.itemKey as keyof CarouselItem] 
    ? item[props.itemKey as keyof CarouselItem] as string | number
    : index
}

const goTo = (index: number) => {
  if (index === currentIndex.value) return
  
  const prevIndex = currentIndex.value
  currentIndex.value = index
  emit('change', currentIndex.value, prevIndex)
}

const next = () => {
  if (currentIndex.value < props.items.length - 1) {
    goTo(currentIndex.value + 1)
  } else if (props.loop) {
    goTo(0)
  }
}

const prev = () => {
  if (currentIndex.value > 0) {
    goTo(currentIndex.value - 1)
  } else if (props.loop) {
    goTo(props.items.length - 1)
  }
}

const startAutoplay = () => {
  if (props.autoplay && props.items.length > 1) {
    autoplayTimer.value = setInterval(() => {
      next()
    }, props.interval)
  }
}

const stopAutoplay = () => {
  if (autoplayTimer.value) {
    clearInterval(autoplayTimer.value)
    autoplayTimer.value = undefined
  }
}

// 触摸事件处理
const handleTouchStart = (event: TouchEvent) => {
  touchStartX.value = event.touches[0].clientX
  touchStartY.value = event.touches[0].clientY
  stopAutoplay()
}

const handleTouchMove = (event: TouchEvent) => {
  event.preventDefault()
}

const handleTouchEnd = (event: TouchEvent) => {
  touchEndX.value = event.changedTouches[0].clientX
  touchEndY.value = event.changedTouches[0].clientY
  
  const deltaX = touchEndX.value - touchStartX.value
  const deltaY = touchEndY.value - touchStartY.value
  const threshold = 50
  
  if (props.vertical) {
    if (Math.abs(deltaY) > threshold) {
      if (deltaY > 0) {
        prev()
      } else {
        next()
      }
    }
  } else {
    if (Math.abs(deltaX) > threshold) {
      if (deltaX > 0) {
        prev()
      } else {
        next()
      }
    }
  }
  
  startAutoplay()
}

// 监听器
watch(() => props.autoplay, (newValue) => {
  if (newValue) {
    startAutoplay()
  } else {
    stopAutoplay()
  }
})

watch(() => props.items.length, () => {
  if (currentIndex.value >= props.items.length) {
    currentIndex.value = Math.max(0, props.items.length - 1)
  }
})

// 生命周期
onMounted(() => {
  startAutoplay()
})

onUnmounted(() => {
  stopAutoplay()
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.carousel {
  position: relative;
  width: 100%;
  height: v-bind(height);
  overflow: hidden;
  border-radius: 0.5rem;
}

.carousel-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.carousel-track {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease-in-out;
  
  .carousel--vertical & {
    flex-direction: column;
    width: 100%;
  }
}

.carousel-item {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
  position: relative;
  
  .carousel--vertical & {
    flex: 0 0 100%;
    height: 100%;
  }
}

.default-item {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .item-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 2rem 1.5rem 1.5rem;
    
    .item-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    
    .item-description {
      font-size: 0.875rem;
      opacity: 0.9;
      margin: 0;
    }
  }
}

.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  z-index: 10;
  
  &:hover {
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &--prev {
    left: 1rem;
  }
  
  &--next {
    right: 1rem;
  }
  
  .carousel--vertical & {
    left: 50%;
    transform: translateX(-50%);
    
    &--prev {
      top: 1rem;
    }
    
    &--next {
      bottom: 1rem;
      top: auto;
    }
  }
}

.carousel-dots {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
  z-index: 10;
  
  .carousel--vertical & {
    right: 1rem;
    left: auto;
    top: 50%;
    bottom: auto;
    transform: translateY(-50%);
    flex-direction: column;
  }
}

.carousel-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  
  &:hover {
    background: rgba(255, 255, 255, 0.8);
  }
  
  &.active {
    background: white;
    transform: scale(1.2);
  }
}

// 图标
.icon-arrow-left::before {
  content: '‹';
  font-size: 1.25rem;
  font-weight: bold;
}

.icon-arrow-right::before {
  content: '›';
  font-size: 1.25rem;
  font-weight: bold;
}

// 响应式
@media (max-width: 640px) {
  .carousel-arrow {
    width: 2rem;
    height: 2rem;
    
    &--prev {
      left: 0.5rem;
    }
    
    &--next {
      right: 0.5rem;
    }
  }
  
  .carousel-dots {
    bottom: 0.5rem;
  }
  
  .default-item .item-content {
    padding: 1.5rem 1rem 1rem;
    
    .item-title {
      font-size: 1rem;
    }
    
    .item-description {
      font-size: 0.8rem;
    }
  }
}
</style>
