<template>
  <div class="error-page">
    <div class="container">
      <div class="error-content">
        <!-- 错误图标 -->
        <div class="error-icon" v-scroll-animation="'scaleIn'">
          <i class="icon-alert-triangle"></i>
        </div>
        
        <!-- 错误信息 -->
        <div class="error-info" v-scroll-animation="{ animation: 'slideUp', delay: 0.2 }">
          <h1 class="error-title">{{ errorTitle }}</h1>
          <p class="error-message">{{ errorMessage }}</p>
          <p class="error-details" v-if="errorDetails">{{ errorDetails }}</p>
        </div>
        
        <!-- 操作按钮 -->
        <div class="error-actions" v-scroll-animation="{ animation: 'fadeIn', delay: 0.4 }">
          <EnhancedButton
            variant="primary"
            size="lg"
            @click="goHome"
            interaction-type="magnetic"
            :intensity="0.8"
          >
            返回首页
          </EnhancedButton>
          
          <EnhancedButton
            variant="outline"
            size="lg"
            @click="goBack"
            interaction-type="ripple"
          >
            返回上页
          </EnhancedButton>
          
          <EnhancedButton
            variant="ghost"
            size="lg"
            @click="reload"
            interaction-type="glow"
            color="#ff6b35"
          >
            刷新页面
          </EnhancedButton>
        </div>
        
        <!-- 联系信息 -->
        <div class="error-contact" v-scroll-animation="{ animation: 'fadeIn', delay: 0.6 }">
          <p>如果问题持续存在，请联系我们：</p>
          <div class="contact-info">
            <a href="tel:************" class="contact-link">
              <i class="icon-phone"></i>
              ************
            </a>
            <a href="mailto:<EMAIL>" class="contact-link">
              <i class="icon-mail"></i>
              <EMAIL>
            </a>
          </div>
        </div>
        
        <!-- 错误ID -->
        <div class="error-id" v-if="errorId">
          <small>错误ID: {{ errorId }}</small>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { EnhancedButton } from '@/components/common'

const router = useRouter()
const route = useRoute()

// 错误信息
const errorTitle = ref('系统出现错误')
const errorMessage = ref('抱歉，系统遇到了一个意外错误。我们正在努力修复这个问题。')
const errorDetails = ref('')
const errorId = ref('')

// 计算错误类型
const errorType = computed(() => {
  return route.query.type as string || 'unknown'
})

// 根据错误类型设置错误信息
const setErrorInfo = () => {
  const type = errorType.value
  const message = route.query.message as string
  const details = route.query.details as string
  const id = route.query.id as string
  
  switch (type) {
    case 'network':
      errorTitle.value = '网络连接错误'
      errorMessage.value = '无法连接到服务器，请检查您的网络连接。'
      break
    case 'timeout':
      errorTitle.value = '请求超时'
      errorMessage.value = '服务器响应超时，请稍后重试。'
      break
    case 'server':
      errorTitle.value = '服务器错误'
      errorMessage.value = '服务器内部错误，我们正在处理这个问题。'
      break
    case 'permission':
      errorTitle.value = '权限不足'
      errorMessage.value = '您没有权限访问此资源。'
      break
    case 'validation':
      errorTitle.value = '数据验证错误'
      errorMessage.value = '提交的数据格式不正确，请检查后重试。'
      break
    default:
      errorTitle.value = '系统出现错误'
      errorMessage.value = message || '抱歉，系统遇到了一个意外错误。'
  }
  
  if (details) {
    errorDetails.value = details
  }
  
  if (id) {
    errorId.value = id
  } else {
    // 生成随机错误ID
    errorId.value = Math.random().toString(36).substr(2, 9).toUpperCase()
  }
}

// 操作方法
const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

const reload = () => {
  window.location.reload()
}

// 生命周期
onMounted(() => {
  setErrorInfo()
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: $spacing-8 0;
}

.error-content {
  text-align: center;
  max-width: 600px;
  padding: $spacing-8;
  background: var(--color-surface);
  border-radius: $border-radius-xl;
  box-shadow: var(--color-card-shadow);
  border: 1px solid var(--color-border);
}

.error-icon {
  margin-bottom: $spacing-6;
  
  i {
    font-size: 4rem;
    color: $error-color;
    display: block;
  }
}

.error-info {
  margin-bottom: $spacing-8;
  
  .error-title {
    font-size: $font-size-3xl;
    font-weight: $font-weight-bold;
    color: var(--color-text-primary);
    margin-bottom: $spacing-4;
  }
  
  .error-message {
    font-size: $font-size-lg;
    color: var(--color-text-secondary);
    line-height: $line-height-relaxed;
    margin-bottom: $spacing-3;
  }
  
  .error-details {
    font-size: $font-size-sm;
    color: var(--color-text-muted);
    background: var(--color-surface-variant);
    padding: $spacing-3;
    border-radius: $border-radius-md;
    border-left: 4px solid $warning-color;
  }
}

.error-actions {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-4;
  justify-content: center;
  margin-bottom: $spacing-8;
}

.error-contact {
  margin-bottom: $spacing-6;
  
  p {
    font-size: $font-size-sm;
    color: var(--color-text-secondary);
    margin-bottom: $spacing-3;
  }
  
  .contact-info {
    display: flex;
    gap: $spacing-6;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .contact-link {
    display: flex;
    align-items: center;
    gap: $spacing-2;
    color: var(--color-primary);
    text-decoration: none;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    transition: color 0.3s ease;
    
    &:hover {
      color: var(--color-secondary);
    }
    
    i {
      font-size: 1rem;
    }
  }
}

.error-id {
  padding-top: $spacing-4;
  border-top: 1px solid var(--color-border);
  
  small {
    font-size: $font-size-xs;
    color: var(--color-text-muted);
    font-family: $font-family-mono;
  }
}

// 响应式调整
@media (max-width: 640px) {
  .error-page {
    padding: $spacing-4 0;
  }
  
  .error-content {
    margin: 0 $spacing-4;
    padding: $spacing-6;
  }
  
  .error-icon i {
    font-size: 3rem;
  }
  
  .error-info .error-title {
    font-size: $font-size-2xl;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
    
    .enhanced-btn {
      width: 100%;
      max-width: 200px;
    }
  }
  
  .contact-info {
    flex-direction: column;
    gap: $spacing-3;
  }
}

// 深色模式调整
[data-theme="dark"] {
  .error-page {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }
}

// 高对比度模式调整
[data-theme="high-contrast"] {
  .error-content {
    border: 2px solid var(--color-border);
  }
  
  .error-icon i {
    color: var(--color-error);
  }
  
  .contact-link {
    border: 1px solid var(--color-primary);
    padding: $spacing-1 $spacing-2;
    border-radius: $border-radius-sm;
  }
}

// 减少动画偏好
@media (prefers-reduced-motion: reduce) {
  .error-content * {
    animation: none !important;
    transition: none !important;
  }
}
</style>
