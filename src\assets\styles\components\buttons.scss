// 按钮组件样式系统

@use '../variables' as *;
@use '../mixins' as *;

// ===== 基础按钮样式 =====
.btn {
  @include button-base;
  
  // 尺寸变体
  &--sm {
    padding: $spacing-2 $spacing-4;
    font-size: $font-size-sm;
    border-radius: $border-radius-md;
  }
  
  &--md {
    padding: $spacing-3 $spacing-6;
    font-size: $font-size-base;
    border-radius: $border-radius-lg;
  }
  
  &--lg {
    padding: $spacing-4 $spacing-8;
    font-size: $font-size-lg;
    border-radius: $border-radius-xl;
  }
  
  &--xl {
    padding: $spacing-5 $spacing-10;
    font-size: $font-size-xl;
    border-radius: $border-radius-xl;
  }
  
  // 宽度变体
  &--block {
    width: 100%;
  }
  
  &--auto {
    width: auto;
  }
  
  // 形状变体
  &--rounded {
    border-radius: $border-radius-full;
  }
  
  &--square {
    border-radius: 0;
  }
  
  // 图标按钮
  &--icon {
    padding: $spacing-3;
    
    &.btn--sm {
      padding: $spacing-2;
    }
    
    &.btn--lg {
      padding: $spacing-4;
    }
    
    &.btn--xl {
      padding: $spacing-5;
    }
  }
}

// ===== 主要按钮样式 =====
.btn--primary {
  @include btn-variant($primary-color, $white);
  background: $primary-gradient;
  
  &:hover:not(:disabled) {
    background: linear-gradient(135deg, darken($primary-color, 5%) 0%, darken($secondary-color, 5%) 100%);
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: $shadow-md;
  }
  
  &:focus {
    box-shadow: 0 0 0 3px rgba($primary-color, 0.3);
  }
}

// ===== 次要按钮样式 =====
.btn--secondary {
  @include btn-variant($white, $primary-color);
  border: 2px solid $primary-color;
  
  &:hover:not(:disabled) {
    background: $primary-color;
    color: $white;
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: $shadow-md;
  }
  
  &:focus {
    box-shadow: 0 0 0 3px rgba($primary-color, 0.2);
  }
}

// ===== 轮廓按钮样式 =====
.btn--outline {
  @include btn-variant(transparent, $primary-color);
  border: 2px solid $primary-color;
  
  &:hover:not(:disabled) {
    background: rgba($primary-color, 0.1);
    transform: translateY(-1px);
  }
  
  &:focus {
    box-shadow: 0 0 0 3px rgba($primary-color, 0.2);
  }
}

// ===== 幽灵按钮样式 =====
.btn--ghost {
  @include btn-variant(transparent, $primary-color);
  
  &:hover:not(:disabled) {
    background: rgba($primary-color, 0.05);
    color: darken($primary-color, 10%);
  }
  
  &:focus {
    box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
  }
}

// ===== 成功按钮样式 =====
.btn--success {
  @include btn-variant($success-color, $white);
  
  &:hover:not(:disabled) {
    background: darken($success-color, 5%);
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
  
  &:focus {
    box-shadow: 0 0 0 3px rgba($success-color, 0.3);
  }
}

// ===== 警告按钮样式 =====
.btn--warning {
  @include btn-variant($warning-color, $white);
  
  &:hover:not(:disabled) {
    background: darken($warning-color, 5%);
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
  
  &:focus {
    box-shadow: 0 0 0 3px rgba($warning-color, 0.3);
  }
}

// ===== 危险按钮样式 =====
.btn--danger {
  @include btn-variant($error-color, $white);
  
  &:hover:not(:disabled) {
    background: darken($error-color, 5%);
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
  
  &:focus {
    box-shadow: 0 0 0 3px rgba($error-color, 0.3);
  }
}

// ===== 信息按钮样式 =====
.btn--info {
  @include btn-variant($info-color, $white);
  
  &:hover:not(:disabled) {
    background: darken($info-color, 5%);
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
  
  &:focus {
    box-shadow: 0 0 0 3px rgba($info-color, 0.3);
  }
}

// ===== 渐变按钮样式 =====
.btn--gradient {
  &.btn--warm {
    background: $gradient-warm;
    color: $white;
    
    &:hover:not(:disabled) {
      filter: brightness(1.1);
      transform: translateY(-2px);
      box-shadow: $shadow-lg;
    }
  }
  
  &.btn--cool {
    background: $gradient-cool;
    color: $white;
    
    &:hover:not(:disabled) {
      filter: brightness(1.1);
      transform: translateY(-2px);
      box-shadow: $shadow-lg;
    }
  }
  
  &.btn--success {
    background: $gradient-success;
    color: $white;
    
    &:hover:not(:disabled) {
      filter: brightness(1.1);
      transform: translateY(-2px);
      box-shadow: $shadow-lg;
    }
  }
}

// ===== 玻璃态按钮样式 =====
.btn--glass {
  @include glass-effect(0.1);
  color: $primary-color;
  border: 1px solid rgba($primary-color, 0.2);
  
  &:hover:not(:disabled) {
    @include glass-effect(0.2);
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
  
  &:focus {
    box-shadow: 0 0 0 3px rgba($primary-color, 0.2);
  }
}

// ===== 按钮组样式 =====
.btn-group {
  display: inline-flex;
  
  .btn {
    border-radius: 0;
    
    &:first-child {
      border-top-left-radius: $border-radius-lg;
      border-bottom-left-radius: $border-radius-lg;
    }
    
    &:last-child {
      border-top-right-radius: $border-radius-lg;
      border-bottom-right-radius: $border-radius-lg;
    }
    
    &:not(:last-child) {
      border-right: none;
    }
    
    &:hover {
      z-index: 1;
    }
  }
  
  &--vertical {
    flex-direction: column;
    
    .btn {
      &:first-child {
        border-radius: $border-radius-lg $border-radius-lg 0 0;
      }
      
      &:last-child {
        border-radius: 0 0 $border-radius-lg $border-radius-lg;
      }
      
      &:not(:last-child) {
        border-right: 2px solid;
        border-bottom: none;
      }
    }
  }
}

// ===== 加载状态按钮 =====
.btn--loading {
  position: relative;
  pointer-events: none;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .btn-text {
    opacity: 0;
  }
}

// ===== 浮动操作按钮 =====
.btn--fab {
  position: fixed;
  bottom: $spacing-6;
  right: $spacing-6;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  background: $primary-gradient;
  color: $white;
  box-shadow: $shadow-xl;
  z-index: $z-index-fixed;
  
  &:hover:not(:disabled) {
    transform: scale(1.1);
    box-shadow: $shadow-2xl;
  }
  
  &:active:not(:disabled) {
    transform: scale(1.05);
  }
  
  @include mobile {
    bottom: $spacing-4;
    right: $spacing-4;
    width: 3rem;
    height: 3rem;
  }
}

// ===== 响应式按钮调整 =====
@include mobile {
  .btn {
    &--lg {
      padding: $spacing-3 $spacing-6;
      font-size: $font-size-base;
    }
    
    &--xl {
      padding: $spacing-4 $spacing-8;
      font-size: $font-size-lg;
    }
  }
  
  .btn-group {
    &:not(.btn-group--vertical) {
      flex-direction: column;
      
      .btn {
        &:first-child {
          border-radius: $border-radius-lg $border-radius-lg 0 0;
        }
        
        &:last-child {
          border-radius: 0 0 $border-radius-lg $border-radius-lg;
        }
        
        &:not(:last-child) {
          border-right: 2px solid;
          border-bottom: none;
        }
      }
    }
  }
}
