import{a as M,c as S,q,e as z,E as A,f as I,T as Q,w as U,i as s,j as l,h as o,G as T,t as c,p as b,B as v,m as t,n as G,_ as P,r as w,o as K,g as h,F as V,k as E,l as W}from"./index-Dz-_Smvi.js";/* empty css                                                                            */import{u as J,f as X}from"./AppLayout-DAG3tKFc.js";const Y={key:0,class:"modal-header"},Z={key:0,class:"modal-title"},x={key:1,class:"modal-footer"},ee={class:"modal-actions"},oe=["disabled"],te=M({__name:"Modal",props:{modelValue:{type:Boolean},title:{},size:{default:"md"},fullscreen:{type:Boolean,default:!1},closable:{type:Boolean,default:!0},closeOnOverlay:{type:<PERSON>olean,default:!0},showHeader:{type:Boolean,default:!0},showFooter:{type:Boolean,default:!1},showCancel:{type:Boolean,default:!1},showConfirm:{type:Boolean,default:!1},cancelText:{default:"取消"},confirmText:{default:"确定"},confirmDisabled:{type:Boolean,default:!1},noPadding:{type:Boolean,default:!1}},emits:["update:modelValue","close","cancel","confirm","opened","closed"],setup(C,{emit:_}){const n=C,i=_,B=S(()=>n.showHeader&&(n.title||n.closable)),k=S(()=>n.showFooter||n.showCancel||n.showConfirm),u=()=>{i("update:modelValue",!1),i("close")},p=()=>{i("cancel"),u()},m=()=>{i("confirm")},r=()=>{n.closeOnOverlay&&u()};q(()=>n.modelValue,async a=>{a?(await G(),i("opened"),document.body.style.overflow="hidden"):(i("closed"),document.body.style.overflow="")});const g=a=>{a.key==="Escape"&&n.closable&&u()};return q(()=>n.modelValue,a=>{a?document.addEventListener("keydown",g):document.removeEventListener("keydown",g)}),(a,y)=>(t(),z(A,{to:"body"},[I(Q,{name:"modal",appear:""},{default:U(()=>[a.modelValue?(t(),s("div",{key:0,class:"modal-overlay",onClick:r},[o("div",{class:b(["modal-container",[`modal--${a.size}`,{"modal--fullscreen":a.fullscreen}]]),onClick:y[0]||(y[0]=v(()=>{},["stop"]))},[B.value?(t(),s("header",Y,[a.title?(t(),s("h2",Z,c(a.title),1)):l("",!0),T(a.$slots,"header",{},void 0,!0),a.closable?(t(),s("button",{key:1,class:"modal-close-button",onClick:u,"aria-label":"关闭"},y[1]||(y[1]=[o("i",{class:"icon-close"},null,-1)]))):l("",!0)])):l("",!0),o("main",{class:b(["modal-body",{"modal-body--no-padding":a.noPadding}])},[T(a.$slots,"default",{},void 0,!0)],2),k.value?(t(),s("footer",x,[T(a.$slots,"footer",{},()=>[o("div",ee,[a.showCancel?(t(),s("button",{key:0,class:"btn btn--secondary",onClick:p},c(a.cancelText),1)):l("",!0),a.showConfirm?(t(),s("button",{key:1,class:"btn btn--primary",disabled:a.confirmDisabled,onClick:m},c(a.confirmText),9,oe)):l("",!0)])],!0)])):l("",!0)],2)])):l("",!0)]),_:3})]))}}),De=P(te,[["__scopeId","data-v-d04777b1"]]),se=["src","alt"],ae={class:"overlay-actions"},ne={class:"product-badges"},re={key:0,class:"badge badge--new"},le={key:1,class:"badge badge--hot"},ce={key:2,class:"badge badge--discount"},ie={key:3,class:"badge badge--featured"},de={class:"product-info"},ue={class:"product-category"},pe={class:"product-name"},fe={key:0,class:"product-description"},me={key:1,class:"product-features"},ye={key:2,class:"product-specs"},ve={class:"spec-label"},ke={class:"spec-value"},he={class:"product-pricing"},be={class:"price-main"},ge={key:0,class:"price-original"},we={class:"price-current"},Ce={key:0,class:"price-unit"},_e={key:3,class:"product-rating"},Be={class:"rating-stars"},Te={class:"rating-text"},Ve={class:"product-stock"},Ee={key:0,class:"stock-text"},Se={key:1,class:"stock-text"},$e={key:2,class:"stock-text"},qe={class:"product-actions"},Me=["disabled"],Pe=M({__name:"ProductCard",props:{product:{},variant:{default:"default"},featured:{type:Boolean,default:!1}},emits:["click","quick-view","add-to-cart","compare","quote","view-details"],setup(C,{emit:_}){const n=C,i=_,{navigateToProducts:B}=J(),k=w(),u=w(),p=w(),m=w();let r;const g=S(()=>{if(!n.product.specifications)return{};const e=n.product.specifications,d=n.variant==="detailed"?4:2;return Object.fromEntries(Object.entries(e).slice(0,d))}),a=e=>X(e),y=()=>{i("click",n.product)},R=()=>{i("quick-view",n.product)},N=()=>{i("add-to-cart",n.product)},O=()=>{i("compare",n.product)},D=()=>{i("quote",n.product)},F=()=>{i("view-details",n.product),B(n.product.id)},H=()=>{const e=u.value?.querySelector("img");e&&(e.src="/images/product-default.svg")},L=()=>{r&&r.kill(),r=h.timeline(),r.to(k.value,{y:-8,scale:1.02,duration:.3,ease:"power2.out"}),r.to(u.value?.querySelector("img"),{scale:1.1,duration:.4,ease:"power2.out"},0),r.to(p.value,{opacity:1,duration:.3,ease:"power2.out"},.1),r.from(p.value?.querySelectorAll(".action-btn")||[],{y:20,opacity:0,duration:.3,stagger:.05,ease:"power2.out"},.2),r.to(m.value,{opacity:1,duration:.3,ease:"power2.out"},0)},j=()=>{r&&r.kill(),r=h.timeline(),r.to(k.value,{y:0,scale:1,duration:.3,ease:"power2.out"}),r.to(u.value?.querySelector("img"),{scale:1,duration:.4,ease:"power2.out"},0),r.to(p.value,{opacity:0,duration:.3,ease:"power2.out"},0),r.to(m.value,{opacity:0,duration:.3,ease:"power2.out"},0)};return K(()=>{h.set(p.value,{opacity:0}),h.set(m.value,{opacity:0}),h.set(p.value?.querySelectorAll(".action-btn")||[],{y:20,opacity:0})}),(e,d)=>(t(),s("div",{class:b(["product-card",[`product-card--${e.variant}`,{"product-card--featured":e.featured}]]),ref_key:"cardRef",ref:k,onMouseenter:L,onMouseleave:j,onClick:y},[o("div",{class:"product-image",ref_key:"imageRef",ref:u},[o("img",{src:e.product.image||"/images/product-default.svg",alt:e.product.name,onError:H},null,40,se),o("div",{class:"image-overlay",ref_key:"overlayRef",ref:p},[o("div",ae,[o("button",{class:"action-btn",onClick:v(R,["stop"]),title:"快速预览"},d[0]||(d[0]=[o("i",{class:"icon-eye"},null,-1)])),o("button",{class:"action-btn",onClick:v(N,["stop"]),title:"加入购物车"},d[1]||(d[1]=[o("i",{class:"icon-cart"},null,-1)])),o("button",{class:"action-btn",onClick:v(O,["stop"]),title:"对比产品"},d[2]||(d[2]=[o("i",{class:"icon-compare"},null,-1)]))])],512),o("div",ne,[e.product.isNew?(t(),s("span",re,"新品")):l("",!0),e.product.isHot?(t(),s("span",le,"热销")):l("",!0),e.product.discount?(t(),s("span",ce," -"+c(e.product.discount)+"% ",1)):l("",!0),e.featured?(t(),s("span",ie,"推荐")):l("",!0)])],512),o("div",de,[o("div",ue,c(e.product.category),1),o("h3",pe,c(e.product.name),1),e.variant!=="compact"?(t(),s("p",fe,c(e.product.description),1)):l("",!0),e.product.features&&e.variant==="detailed"?(t(),s("div",me,[(t(!0),s(V,null,E(e.product.features.slice(0,3),f=>(t(),s("div",{key:f,class:"feature-item"},[d[3]||(d[3]=o("i",{class:"icon-check"},null,-1)),o("span",null,c(f),1)]))),128))])):l("",!0),e.product.specifications&&e.variant!=="compact"?(t(),s("div",ye,[(t(!0),s(V,null,E(g.value,(f,$)=>(t(),s("div",{key:$,class:"spec-item"},[o("span",ve,c($)+":",1),o("span",ke,c(f),1)]))),128))])):l("",!0),o("div",he,[o("div",be,[e.product.originalPrice&&e.product.originalPrice>e.product.price?(t(),s("span",ge," ¥"+c(a(e.product.originalPrice)),1)):l("",!0),o("span",we,"¥"+c(a(e.product.price)),1)]),e.product.priceUnit?(t(),s("div",Ce,c(e.product.priceUnit),1)):l("",!0)]),e.product.rating?(t(),s("div",_e,[o("div",Be,[(t(),s(V,null,E(5,f=>o("i",{key:f,class:b(["star",{filled:f<=Math.floor(e.product.rating)}])},null,2)),64))]),o("span",Te,"("+c(e.product.rating)+") "+c(e.product.reviewCount||0)+"条评价",1)])):l("",!0),o("div",Ve,[o("div",{class:b(["stock-indicator",{"in-stock":e.product.stock>10,"low-stock":e.product.stock>0&&e.product.stock<=10,"out-of-stock":e.product.stock===0}])},[e.product.stock>10?(t(),s("span",Ee,"现货充足")):e.product.stock>0?(t(),s("span",Se,"库存紧张 ("+c(e.product.stock)+"件)",1)):(t(),s("span",$e,"暂时缺货"))],2)])]),o("div",qe,[o("button",{class:"btn btn--secondary btn--sm",onClick:v(D,["stop"]),disabled:e.product.stock===0}," 获取报价 ",8,Me),o("button",{class:"btn btn--primary btn--sm",onClick:v(F,["stop"])},d[4]||(d[4]=[W(" 查看详情 ",-1),o("i",{class:"icon-arrow-right"},null,-1)]))]),o("div",{class:"hover-effect",ref_key:"hoverEffectRef",ref:m},null,512)],34))}}),Fe=P(Pe,[["__scopeId","data-v-a33f281b"]]);export{De as M,Fe as P};
