<template>
  <picture 
    class="responsive-image"
    :class="imageClasses"
    ref="pictureRef"
  >
    <!-- WebP 源 -->
    <source 
      v-if="webpSources.length > 0"
      :srcset="webpSources.join(', ')"
      :sizes="computedSizes"
      type="image/webp"
    />
    
    <!-- AVIF 源 -->
    <source 
      v-if="avifSources.length > 0"
      :srcset="avifSources.join(', ')"
      :sizes="computedSizes"
      type="image/avif"
    />
    
    <!-- 默认图片 -->
    <img
      :src="computedSrc"
      :srcset="computedSrcset"
      :sizes="computedSizes"
      :alt="alt"
      :loading="loading"
      :decoding="decoding"
      :width="computedWidth"
      :height="computedHeight"
      :style="imageStyles"
      @load="onLoad"
      @error="onError"
      @loadstart="onLoadStart"
      ref="imgRef"
    />
    
    <!-- 加载状态 -->
    <div v-if="isLoading" class="image-loading">
      <div class="loading-spinner"></div>
      <span v-if="showLoadingText">{{ loadingText }}</span>
    </div>
    
    <!-- 错误状态 -->
    <div v-if="hasError" class="image-error">
      <i class="error-icon">⚠️</i>
      <span v-if="showErrorText">{{ errorText }}</span>
      <button v-if="showRetry" @click="retry" class="retry-button">
        重试
      </button>
    </div>
  </picture>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useGlobalResponsive } from '@/composables/useResponsive'

interface ImageSource {
  src: string
  width?: number
  density?: number
  media?: string
}

interface Props {
  // 基础属性
  src: string
  alt: string
  
  // 响应式源
  sources?: ImageSource[]
  sizes?: string
  
  // 尺寸
  width?: number | string
  height?: number | string
  aspectRatio?: number
  
  // 加载属性
  loading?: 'lazy' | 'eager'
  decoding?: 'async' | 'sync' | 'auto'
  
  // 格式支持
  webp?: boolean
  avif?: boolean
  
  // 状态显示
  showLoadingText?: boolean
  showErrorText?: boolean
  showRetry?: boolean
  loadingText?: string
  errorText?: string
  
  // 样式
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
  objectPosition?: string
  borderRadius?: string
  
  // 行为
  retryAttempts?: number
  retryDelay?: number
  
  // 优化
  priority?: boolean
  preload?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: 'lazy',
  decoding: 'async',
  webp: true,
  avif: false,
  showLoadingText: false,
  showErrorText: true,
  showRetry: true,
  loadingText: '加载中...',
  errorText: '图片加载失败',
  objectFit: 'cover',
  objectPosition: 'center',
  retryAttempts: 3,
  retryDelay: 1000,
  priority: false,
  preload: false
})

const emit = defineEmits<{
  load: [event: Event]
  error: [event: Event]
  loadstart: [event: Event]
}>()

// 响应式工具
const responsive = useGlobalResponsive()

// 模板引用
const pictureRef = ref<HTMLPictureElement>()
const imgRef = ref<HTMLImageElement>()

// 状态
const isLoading = ref(false)
const hasError = ref(false)
const currentAttempt = ref(0)
const loadedSrc = ref('')

// 计算当前应该使用的图片源
const computedSrc = computed(() => {
  if (!props.sources || props.sources.length === 0) {
    return props.src
  }
  
  const deviceWidth = responsive.windowWidth.value
  const pixelRatio = responsive.pixelRatio.value
  
  // 根据设备宽度和像素密度选择最合适的图片
  const suitableSources = props.sources
    .filter(source => {
      if (source.media) {
        return window.matchMedia(source.media).matches
      }
      return true
    })
    .sort((a, b) => {
      const aWidth = a.width || 0
      const bWidth = b.width || 0
      return Math.abs(aWidth - deviceWidth * pixelRatio) - Math.abs(bWidth - deviceWidth * pixelRatio)
    })
  
  return suitableSources[0]?.src || props.src
})

// 计算 srcset
const computedSrcset = computed(() => {
  if (!props.sources || props.sources.length === 0) {
    return undefined
  }
  
  return props.sources
    .map(source => {
      if (source.width) {
        return `${source.src} ${source.width}w`
      }
      if (source.density) {
        return `${source.src} ${source.density}x`
      }
      return source.src
    })
    .join(', ')
})

// 计算 sizes
const computedSizes = computed(() => {
  if (props.sizes) {
    return props.sizes
  }
  
  // 默认 sizes 基于断点
  const breakpoint = responsive.currentBreakpoint.value
  switch (breakpoint) {
    case 'xs':
    case 'sm':
      return '100vw'
    case 'md':
      return '50vw'
    case 'lg':
    case 'xl':
    case 'xxl':
      return '33vw'
    default:
      return '100vw'
  }
})

// WebP 源
const webpSources = computed(() => {
  if (!props.webp || !props.sources) return []
  
  return props.sources
    .map(source => {
      const webpSrc = source.src.replace(/\.(jpg|jpeg|png)$/i, '.webp')
      if (source.width) {
        return `${webpSrc} ${source.width}w`
      }
      if (source.density) {
        return `${webpSrc} ${source.density}x`
      }
      return webpSrc
    })
})

// AVIF 源
const avifSources = computed(() => {
  if (!props.avif || !props.sources) return []
  
  return props.sources
    .map(source => {
      const avifSrc = source.src.replace(/\.(jpg|jpeg|png)$/i, '.avif')
      if (source.width) {
        return `${avifSrc} ${source.width}w`
      }
      if (source.density) {
        return `${avifSrc} ${source.density}x`
      }
      return avifSrc
    })
})

// 计算尺寸
const computedWidth = computed(() => {
  if (typeof props.width === 'number') {
    return props.width
  }
  if (typeof props.width === 'string') {
    return undefined // 让 CSS 处理
  }
  return undefined
})

const computedHeight = computed(() => {
  if (typeof props.height === 'number') {
    return props.height
  }
  if (typeof props.height === 'string') {
    return undefined // 让 CSS 处理
  }
  if (props.aspectRatio && computedWidth.value) {
    return Math.round(computedWidth.value / props.aspectRatio)
  }
  return undefined
})

// 图片样式
const imageStyles = computed(() => ({
  objectFit: props.objectFit,
  objectPosition: props.objectPosition,
  borderRadius: props.borderRadius,
  width: typeof props.width === 'string' ? props.width : undefined,
  height: typeof props.height === 'string' ? props.height : undefined,
  aspectRatio: props.aspectRatio ? `${props.aspectRatio}` : undefined
}))

// 图片类名
const imageClasses = computed(() => ({
  'responsive-image--loading': isLoading.value,
  'responsive-image--error': hasError.value,
  'responsive-image--loaded': loadedSrc.value === computedSrc.value,
  [`responsive-image--${responsive.currentBreakpoint.value}`]: true
}))

// 事件处理
const onLoadStart = (event: Event) => {
  isLoading.value = true
  hasError.value = false
  emit('loadstart', event)
}

const onLoad = (event: Event) => {
  isLoading.value = false
  hasError.value = false
  loadedSrc.value = computedSrc.value
  currentAttempt.value = 0
  emit('load', event)
}

const onError = (event: Event) => {
  isLoading.value = false
  hasError.value = true
  emit('error', event)
  
  // 自动重试
  if (currentAttempt.value < props.retryAttempts) {
    setTimeout(() => {
      retry()
    }, props.retryDelay * Math.pow(2, currentAttempt.value))
  }
}

const retry = () => {
  if (currentAttempt.value >= props.retryAttempts) {
    return
  }
  
  currentAttempt.value++
  hasError.value = false
  
  if (imgRef.value) {
    imgRef.value.src = computedSrc.value
  }
}

// 预加载
const preloadImage = () => {
  if (!props.preload) return
  
  const link = document.createElement('link')
  link.rel = 'preload'
  link.as = 'image'
  link.href = computedSrc.value
  
  if (computedSrcset.value) {
    link.setAttribute('imagesrcset', computedSrcset.value)
  }
  
  if (computedSizes.value) {
    link.setAttribute('imagesizes', computedSizes.value)
  }
  
  document.head.appendChild(link)
}

// 监听源变化
watch(computedSrc, (newSrc, oldSrc) => {
  if (newSrc !== oldSrc && newSrc !== loadedSrc.value) {
    hasError.value = false
    currentAttempt.value = 0
  }
})

// 生命周期
onMounted(() => {
  if (props.preload) {
    preloadImage()
  }
  
  // 高优先级图片立即加载
  if (props.priority && imgRef.value) {
    imgRef.value.loading = 'eager'
  }
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.responsive-image {
  position: relative;
  display: inline-block;
  overflow: hidden;
  
  img {
    display: block;
    max-width: 100%;
    height: auto;
    transition: opacity 0.3s ease;
  }
  
  &--loading img {
    opacity: 0.7;
  }
  
  &--error img {
    opacity: 0.3;
  }
  
  &--loaded img {
    opacity: 1;
  }
}

.image-loading,
.image-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-2;
  padding: $spacing-4;
  background: rgba(255, 255, 255, 0.9);
  border-radius: $border-radius-md;
  font-size: $font-size-sm;
  color: var(--color-text-secondary);
  z-index: 1;
}

.loading-spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid transparent;
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-icon {
  font-size: 1.5rem;
}

.retry-button {
  padding: $spacing-1 $spacing-2;
  background: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: $border-radius-sm;
  font-size: $font-size-xs;
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background: var(--color-secondary);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式调整
@media (max-width: 640px) {
  .image-loading,
  .image-error {
    padding: $spacing-2;
    font-size: $font-size-xs;
  }
  
  .loading-spinner {
    width: 1rem;
    height: 1rem;
  }
}

// 深色模式
[data-theme="dark"] {
  .image-loading,
  .image-error {
    background: rgba(0, 0, 0, 0.8);
    color: var(--color-text-primary);
  }
}

// 减少动画偏好
@media (prefers-reduced-motion: reduce) {
  .responsive-image img {
    transition: none;
  }
  
  .loading-spinner {
    animation: none;
  }
}
</style>
