import{a as ue,g as k,S as de,r as a,c as f,q as ve,o as pe,e as z,w as H,f as C,h as t,i as r,k as T,F as q,s as x,j as _,t as c,v as ge,x as me,p as y,L as fe,l as I,n as j,m as l,_ as ye}from"./index-Dz-_Smvi.js";import{u as ke,A as Ce,f as _e}from"./AppLayout-DAG3tKFc.js";import{B as he}from"./Breadcrumb-DX9d_gU2.js";import{M as be,P as we}from"./ProductCard-BWOk5Fwa.js";/* empty css                                                                            */import{S as Pe,F as xe}from"./FilterPanel-C3hsGuJ4.js";import{u as Ve}from"./products-BsGbxZI5.js";import"./contact-sLg3zrn9.js";const Se={class:"container"},Ne={class:"hero-content"},Re={class:"category-nav"},Ue=["onClick"],Te={class:"count"},qe={class:"container"},Le={class:"filters-layout"},$e={class:"search-section"},Ae={class:"filter-section"},Be={class:"container"},Fe={class:"content-header"},Me={class:"results-info"},Qe={class:"results-count"},De={key:0,class:"category-filter"},Ee={class:"content-controls"},ze={class:"sort-controls"},He={class:"view-controls"},Ie={key:0,class:"loading-container"},je={key:1,class:"empty-state"},Ge={key:2,class:"pagination-container"},We={class:"pagination"},Je=["disabled"],Ke={class:"pagination-numbers"},Oe=["onClick"],Xe=["disabled"],Ye={key:0,class:"quick-view-content"},Ze={class:"quick-view-layout"},et={class:"quick-view-image"},tt=["src","alt"],st={class:"quick-view-info"},ot={class:"product-name"},at={class:"product-category"},nt={class:"product-description"},it={class:"product-price"},lt={class:"price-current"},rt={key:0,class:"price-unit"},ct={class:"quick-view-actions"},ut=ue({__name:"ProductsPage",setup(dt){k.registerPlugin(de),Ve();const{navigateToContact:G,navigateToProducts:L}=ke(),$=a(),A=a(),V=a(),S=a([]),B=a(!1),p=a(""),h=a("default"),g=a("grid"),n=a(1),N=a(12),d=a("all"),u=a({}),b=a(!1),i=a(null),F=a([{key:"all",name:"全部",icon:"icon-all",count:6},{key:"box",name:"包装盒",icon:"icon-box",count:2},{key:"bag",name:"包装袋",icon:"icon-bag",count:2},{key:"bottle",name:"包装瓶",icon:"icon-bottle",count:1},{key:"label",name:"标签贴纸",icon:"icon-label",count:1}]),W=a([{id:"1",name:"高端礼品包装盒",description:"精美的高端礼品包装盒，适用于各种高档商品包装，提升产品价值。",category:"包装盒",price:25,originalPrice:30,priceUnit:"/个",image:"/images/product1.svg",rating:4.8,reviewCount:156,stock:500,isNew:!0,featured:!0,discount:17,features:["环保材料","精美印刷","可定制尺寸","快速交货"],specifications:{材质:"高档纸板",尺寸:"20x15x8cm",颜色:"多色可选",工艺:"烫金印刷"}},{id:"2",name:"食品级包装袋",description:"符合食品安全标准的包装袋，密封性好，保鲜效果佳。",category:"包装袋",price:.5,priceUnit:"/个",image:"/images/product2.svg",rating:4.6,reviewCount:89,stock:1e3,isHot:!0,features:["食品级材料","密封性好","透明设计","环保可回收"],specifications:{材质:"PE食品级",厚度:"0.08mm",尺寸:"多规格",认证:"FDA认证"}},{id:"3",name:"玻璃包装瓶",description:"高品质玻璃包装瓶，适用于化妆品、香水等高端产品包装。",category:"包装瓶",price:15,priceUnit:"/个",image:"/images/product3.svg",rating:4.9,reviewCount:234,stock:200,features:["高硼硅玻璃","精美造型","可定制容量","配套盖子"],specifications:{材质:"高硼硅玻璃",容量:"50ml-500ml",颜色:"透明/磨砂",工艺:"机制成型"}},{id:"4",name:"防水标签贴纸",description:"高品质防水标签贴纸，粘性强，不易脱落，适用于各种环境。",category:"标签贴纸",price:.1,priceUnit:"/张",image:"/images/product4.svg",rating:4.5,reviewCount:67,stock:5e3,features:["防水防油","强力粘性","不易褪色","可定制印刷"],specifications:{材质:"PVC/PET",规格:"多种尺寸",胶水:"永久性胶",印刷:"数码印刷"}},{id:"5",name:"环保纸质包装盒",description:"100%可回收环保纸质包装盒，绿色环保，符合可持续发展理念。",category:"包装盒",price:12,priceUnit:"/个",image:"/images/product5.svg",rating:4.7,reviewCount:123,stock:800,isNew:!0,features:["100%可回收","环保印刷","结构牢固","成本经济"],specifications:{材质:"再生纸板",厚度:"3mm",印刷:"环保油墨",认证:"FSC认证"}},{id:"6",name:"真空包装袋",description:"专业真空包装袋，延长食品保质期，保持新鲜度。",category:"包装袋",price:1.2,priceUnit:"/个",image:"/images/product6.svg",rating:4.8,reviewCount:198,stock:600,isHot:!0,features:["真空密封","延长保质期","透明材质","耐高温"],specifications:{材质:"PA/PE复合",厚度:"0.12mm",耐温:"-40°C~121°C",透氧率:"<1cc/m²"}}]),J=f(()=>[{text:"包装盒",category:"盒类"},{text:"包装袋",category:"袋类"},{text:"包装瓶",category:"瓶类"},{text:"标签贴纸",category:"标签"},{text:"环保包装",category:"环保"}]),K=f(()=>[{key:"category",title:"产品分类",type:"checkbox",options:[{value:"包装盒",label:"包装盒",count:2},{value:"包装袋",label:"包装袋",count:2},{value:"包装瓶",label:"包装瓶",count:1},{value:"标签贴纸",label:"标签贴纸",count:1}]},{key:"priceRange",title:"价格范围",type:"range",minPlaceholder:"最低价格",maxPlaceholder:"最高价格"},{key:"features",title:"产品特性",type:"checkbox",options:[{value:"环保材料",label:"环保材料",count:3},{value:"防水防油",label:"防水防油",count:2},{value:"食品级",label:"食品级",count:2},{value:"可定制",label:"可定制",count:4}]}]),w=f(()=>{let o=[...W.value];if(d.value!=="all"){const e=Q(d.value);o=o.filter(s=>s.category===e)}if(p.value){const e=p.value.toLowerCase();o=o.filter(s=>s.name.toLowerCase().includes(e)||s.description.toLowerCase().includes(e)||s.category.toLowerCase().includes(e))}if(u.value.category&&u.value.category.length>0&&(o=o.filter(e=>u.value.category.includes(e.category))),u.value.priceRange){const{min:e,max:s}=u.value.priceRange;o=o.filter(v=>{const m=v.price;return!(e!==void 0&&m<e||s!==void 0&&m>s)})}return h.value!=="default"&&o.sort((e,s)=>{switch(h.value){case"name-asc":return e.name.localeCompare(s.name);case"name-desc":return s.name.localeCompare(e.name);case"price-asc":return e.price-s.price;case"price-desc":return s.price-e.price;case"rating-desc":return(s.rating||0)-(e.rating||0);case"newest":return(s.isNew?1:0)-(e.isNew?1:0);default:return 0}}),o}),P=f(()=>Math.ceil(w.value.length/N.value)),M=f(()=>{const o=(n.value-1)*N.value,e=o+N.value;return w.value.slice(o,e)}),O=f(()=>{const o=[],e=P.value,s=n.value,v=Math.max(1,s-2),m=Math.min(e,s+2);for(let U=v;U<=m;U++)o.push(U);return o}),X=o=>_e(o),Q=o=>{const e=F.value.find(s=>s.key===o);return e?e.name:o},Y=(o,e)=>{o&&(S.value[e]=o)},Z=o=>{d.value=o,n.value=1},ee=o=>{p.value=o,n.value=1},te=o=>{u.value=o,n.value=1},se=()=>{n.value=1},oe=o=>{L(o.id)},ae=o=>{i.value=o,b.value=!0},ne=()=>{b.value=!1,i.value=null},ie=o=>{console.log("Add to cart:",o)},le=o=>{console.log("Compare product:",o)},D=o=>{G()},E=o=>{L(o.id)},re=()=>{p.value="",u.value={},d.value="all",n.value=1},R=o=>{o>=1&&o<=P.value&&(n.value=o,V.value?.scrollIntoView({behavior:"smooth"}))},ce=async()=>{await j(),k.from($.value?.querySelector(".hero-content")?.children||[],{y:50,opacity:0,duration:.8,stagger:.2,ease:"power2.out"}),k.from(A.value,{y:30,opacity:0,duration:.6,ease:"power2.out",delay:.3}),k.from(S.value,{y:60,opacity:0,duration:.8,stagger:.1,ease:"power2.out",scrollTrigger:{trigger:V.value,start:"top 80%",once:!0}})};return ve(()=>M.value,()=>{j(()=>{k.from(S.value,{y:30,opacity:0,duration:.5,stagger:.05,ease:"power2.out"})})}),pe(()=>{ce()}),(o,e)=>(l(),z(Ce,null,{default:H(()=>[C(he),t("section",{class:"products-hero",ref_key:"heroRef",ref:$},[t("div",Se,[t("div",Ne,[e[10]||(e[10]=t("h1",{class:"page-title"},"产品目录",-1)),e[11]||(e[11]=t("p",{class:"page-subtitle"}," 专业的包装产品，满足您的各种需求 ",-1)),t("div",Re,[(l(!0),r(q,null,T(F.value,s=>(l(),r("button",{key:s.key,class:y(["category-btn",{active:d.value===s.key}]),onClick:v=>Z(s.key)},[t("i",{class:y(s.icon)},null,2),t("span",null,c(s.name),1),t("span",Te,"("+c(s.count)+")",1)],10,Ue))),128))])])])],512),t("section",{class:"products-filters",ref_key:"filtersRef",ref:A},[t("div",qe,[t("div",Le,[t("div",$e,[C(x(Pe),{modelValue:p.value,"onUpdate:modelValue":e[0]||(e[0]=s=>p.value=s),placeholder:"搜索产品...",suggestions:J.value,onSearch:ee},null,8,["modelValue","suggestions"])]),t("div",Ae,[C(x(xe),{modelValue:u.value,"onUpdate:modelValue":e[1]||(e[1]=s=>u.value=s),"filter-groups":K.value,onChange:te},null,8,["modelValue","filter-groups"])])])])],512),t("section",{class:"products-content",ref_key:"contentRef",ref:V},[t("div",Be,[t("div",Fe,[t("div",Me,[t("span",Qe,"找到 "+c(w.value.length)+" 个产品",1),d.value!=="all"?(l(),r("span",De," 分类："+c(Q(d.value)),1)):_("",!0)]),t("div",Ee,[t("div",ze,[e[13]||(e[13]=t("label",{for:"sort-select",class:"sort-label"},"排序：",-1)),ge(t("select",{id:"sort-select","onUpdate:modelValue":e[2]||(e[2]=s=>h.value=s),class:"sort-select",onChange:se},e[12]||(e[12]=[t("option",{value:"default"},"默认排序",-1),t("option",{value:"name-asc"},"名称升序",-1),t("option",{value:"name-desc"},"名称降序",-1),t("option",{value:"price-asc"},"价格升序",-1),t("option",{value:"price-desc"},"价格降序",-1),t("option",{value:"rating-desc"},"评分降序",-1),t("option",{value:"newest"},"最新产品",-1)]),544),[[me,h.value]])]),t("div",He,[t("button",{class:y(["view-btn",{active:g.value==="grid"}]),onClick:e[3]||(e[3]=s=>g.value="grid"),"aria-label":"网格视图"},e[14]||(e[14]=[t("i",{class:"icon-grid"},null,-1)]),2),t("button",{class:y(["view-btn",{active:g.value==="list"}]),onClick:e[4]||(e[4]=s=>g.value="list"),"aria-label":"列表视图"},e[15]||(e[15]=[t("i",{class:"icon-list"},null,-1)]),2)])])]),t("div",{class:y(["products-grid",[`products-grid--${g.value}`,{loading:B.value}]])},[(l(!0),r(q,null,T(M.value,(s,v)=>(l(),z(we,{key:s.id,product:s,variant:g.value==="list"?"compact":"default",featured:s.featured,ref_for:!0,ref:m=>Y(m,v),onClick:oe,onQuickView:ae,onAddToCart:ie,onCompare:le,onQuote:D,onViewDetails:E},null,8,["product","variant","featured"]))),128))],2),B.value?(l(),r("div",Ie,[C(x(fe))])):w.value.length===0?(l(),r("div",je,[e[16]||(e[16]=t("div",{class:"empty-icon"},[t("i",{class:"icon-search"})],-1)),e[17]||(e[17]=t("h3",{class:"empty-title"},"未找到相关产品",-1)),e[18]||(e[18]=t("p",{class:"empty-description"}," 请尝试调整搜索条件或筛选器 ",-1)),t("button",{class:"btn btn--primary",onClick:re}," 清除筛选条件 ")])):_("",!0),P.value>1?(l(),r("div",Ge,[t("div",We,[t("button",{class:"pagination-btn",disabled:n.value===1,onClick:e[5]||(e[5]=s=>R(n.value-1))},e[19]||(e[19]=[t("i",{class:"icon-arrow-left"},null,-1),I(" 上一页 ",-1)]),8,Je),t("div",Ke,[(l(!0),r(q,null,T(O.value,s=>(l(),r("button",{key:s,class:y(["pagination-number",{active:s===n.value}]),onClick:v=>R(s)},c(s),11,Oe))),128))]),t("button",{class:"pagination-btn",disabled:n.value===P.value,onClick:e[6]||(e[6]=s=>R(n.value+1))},e[20]||(e[20]=[I(" 下一页 ",-1),t("i",{class:"icon-arrow-right"},null,-1)]),8,Xe)])])):_("",!0)])],512),C(x(be),{modelValue:b.value,"onUpdate:modelValue":e[9]||(e[9]=s=>b.value=s),title:"产品预览",size:"large",onClose:ne},{default:H(()=>[i.value?(l(),r("div",Ye,[t("div",Ze,[t("div",et,[t("img",{src:i.value.image,alt:i.value.name},null,8,tt)]),t("div",st,[t("h3",ot,c(i.value.name),1),t("div",at,c(i.value.category),1),t("p",nt,c(i.value.description),1),t("div",it,[t("span",lt,"¥"+c(X(i.value.price)),1),i.value.priceUnit?(l(),r("span",rt,c(i.value.priceUnit),1)):_("",!0)]),t("div",ct,[t("button",{class:"btn btn--secondary",onClick:e[7]||(e[7]=s=>D(i.value))}," 获取报价 "),t("button",{class:"btn btn--primary",onClick:e[8]||(e[8]=s=>E(i.value))}," 查看详情 ")])])])])):_("",!0)]),_:1},8,["modelValue"])]),_:1}))}}),_t=ye(ut,[["__scopeId","data-v-5a8e17d6"]]);export{_t as default};
