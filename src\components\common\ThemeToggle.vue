<template>
  <div class="theme-toggle" :class="{ 'theme-toggle--expanded': showOptions }">
    <!-- 主题切换按钮 -->
    <button
      class="theme-toggle-btn"
      :class="{ 'active': showOptions }"
      @click="toggleOptions"
      :aria-label="`当前主题: ${themeDisplayName}`"
      :title="`当前主题: ${themeDisplayName}`"
    >
      <span class="theme-icon">{{ themeIcon }}</span>
      <span v-if="showLabel" class="theme-label">{{ themeDisplayName }}</span>
      <i v-if="variant === 'dropdown'" class="dropdown-icon" :class="{ 'rotated': showOptions }">▼</i>
    </button>

    <!-- 主题选项（下拉模式） -->
    <div 
      v-if="variant === 'dropdown'" 
      v-show="showOptions"
      class="theme-options"
      @click.stop
    >
      <button
        v-for="theme in availableThemes"
        :key="theme.value"
        class="theme-option"
        :class="{ 'active': currentTheme === theme.value }"
        @click="selectTheme(theme.value)"
      >
        <span class="option-icon">{{ theme.icon }}</span>
        <span class="option-label">{{ theme.label }}</span>
        <i v-if="currentTheme === theme.value" class="check-icon">✓</i>
      </button>
    </div>

    <!-- 主题选项（按钮组模式） -->
    <div v-if="variant === 'buttons'" class="theme-buttons">
      <button
        v-for="theme in availableThemes"
        :key="theme.value"
        class="theme-button"
        :class="{ 'active': currentTheme === theme.value }"
        @click="selectTheme(theme.value)"
        :aria-label="theme.label"
        :title="theme.label"
      >
        <span class="button-icon">{{ theme.icon }}</span>
        <span v-if="showButtonLabels" class="button-label">{{ theme.label }}</span>
      </button>
    </div>

    <!-- 简单切换模式 -->
    <template v-if="variant === 'simple'">
      <!-- 只在 light 和 dark 之间切换 -->
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useGlobalTheme, type Theme } from '@/composables'

interface Props {
  variant?: 'simple' | 'dropdown' | 'buttons'
  showLabel?: boolean
  showButtonLabels?: boolean
  size?: 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'simple',
  showLabel: false,
  showButtonLabels: false,
  size: 'md'
})

const theme = useGlobalTheme()
const showOptions = ref(false)

// 从主题 composable 获取状态
const {
  currentTheme,
  themeDisplayName,
  themeIcon,
  availableThemes,
  setTheme,
  toggleTheme
} = theme

// 切换选项显示
const toggleOptions = () => {
  if (props.variant === 'simple') {
    toggleTheme()
  } else if (props.variant === 'dropdown') {
    showOptions.value = !showOptions.value
  }
}

// 选择主题
const selectTheme = (themeValue: Theme) => {
  setTheme(themeValue)
  if (props.variant === 'dropdown') {
    showOptions.value = false
  }
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  const toggleElement = document.querySelector('.theme-toggle')
  
  if (toggleElement && !toggleElement.contains(target)) {
    showOptions.value = false
  }
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    showOptions.value = false
  }
}

onMounted(() => {
  if (props.variant === 'dropdown') {
    document.addEventListener('click', handleClickOutside)
    document.addEventListener('keydown', handleKeydown)
  }
})

onUnmounted(() => {
  if (props.variant === 'dropdown') {
    document.removeEventListener('click', handleClickOutside)
    document.removeEventListener('keydown', handleKeydown)
  }
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.theme-toggle {
  position: relative;
  display: inline-block;

  // 主切换按钮
  .theme-toggle-btn {
    display: flex;
    align-items: center;
    gap: $spacing-2;
    padding: $spacing-2 $spacing-3;
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: $border-radius-lg;
    color: var(--color-text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: $font-size-sm;

    &:hover {
      background: var(--color-surface-variant);
      border-color: var(--color-primary);
    }

    &.active {
      background: var(--color-primary);
      color: var(--color-white);
      border-color: var(--color-primary);
    }

    .theme-icon {
      font-size: 1rem;
      line-height: 1;
    }

    .theme-label {
      font-weight: $font-weight-medium;
      white-space: nowrap;
    }

    .dropdown-icon {
      font-size: 0.75rem;
      transition: transform 0.3s ease;
      
      &.rotated {
        transform: rotate(180deg);
      }
    }
  }

  // 下拉选项
  .theme-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: $spacing-1;
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: $border-radius-lg;
    box-shadow: var(--color-shadow);
    z-index: $z-index-dropdown;
    overflow: hidden;

    .theme-option {
      background: none;
      border: none;
      padding: 0;
      margin: 0;
      cursor: pointer;
      font: inherit;
      color: inherit;
      display: flex;
      align-items: center;
      gap: $spacing-3;
      width: 100%;
      padding: $spacing-3 $spacing-4;
      background: transparent;
      color: var(--color-text-primary);
      cursor: pointer;
      transition: background-color 0.2s ease;
      font-size: $font-size-sm;

      &:hover {
        background: var(--color-surface-variant);
      }

      &.active {
        background: rgba(var(--color-primary), 0.1);
        color: var(--color-primary);
      }

      .option-icon {
        font-size: 1rem;
        line-height: 1;
      }

      .option-label {
        flex: 1;
        text-align: left;
        font-weight: $font-weight-medium;
      }

      .check-icon {
        font-size: 0.875rem;
        color: var(--color-primary);
      }
    }
  }

  // 按钮组模式
  .theme-buttons {
    display: flex;
    gap: $spacing-1;
    background: var(--color-surface-variant);
    padding: $spacing-1;
    border-radius: $border-radius-lg;

    .theme-button {
      background: none;
      border: none;
      padding: 0;
      margin: 0;
      cursor: pointer;
      font: inherit;
      color: inherit;
      display: flex;
      align-items: center;
      gap: $spacing-2;
      padding: $spacing-2 $spacing-3;
      background: transparent;
      border-radius: $border-radius-md;
      color: var(--color-text-secondary);
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: $font-size-sm;

      &:hover {
        background: var(--color-surface);
        color: var(--color-text-primary);
      }

      &.active {
        background: var(--color-surface);
        color: var(--color-primary);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .button-icon {
        font-size: 1rem;
        line-height: 1;
      }

      .button-label {
        font-weight: $font-weight-medium;
        white-space: nowrap;
      }
    }
  }

  // 尺寸变体
  &--sm {
    .theme-toggle-btn {
      padding: $spacing-1 $spacing-2;
      font-size: $font-size-xs;

      .theme-icon {
        font-size: 0.875rem;
      }
    }

    .theme-option,
    .theme-button {
      padding: $spacing-2 $spacing-3;
      font-size: $font-size-xs;
    }
  }

  &--lg {
    .theme-toggle-btn {
      padding: $spacing-3 $spacing-4;
      font-size: $font-size-base;

      .theme-icon {
        font-size: 1.25rem;
      }
    }

    .theme-option,
    .theme-button {
      padding: $spacing-4 $spacing-5;
      font-size: $font-size-base;
    }
  }

  // 展开状态
  &--expanded {
    .theme-toggle-btn {
      background: var(--color-primary);
      color: var(--color-white);
      border-color: var(--color-primary);
    }
  }
}

// 响应式调整
@media (max-width: 640px) {
  .theme-toggle {
    .theme-options {
      left: auto;
      right: 0;
      min-width: 150px;
    }

    .theme-buttons {
      flex-direction: column;

      .theme-button {
        justify-content: flex-start;
      }
    }
  }
}

// 深色模式特定样式
[data-theme="dark"] {
  .theme-toggle {
    .theme-options {
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    }
  }
}

// 高对比度模式特定样式
[data-theme="high-contrast"] {
  .theme-toggle {
    .theme-toggle-btn,
    .theme-option,
    .theme-button {
      border: 2px solid var(--color-border);
    }

    .theme-toggle-btn.active,
    .theme-option.active,
    .theme-button.active {
      background: var(--color-text-primary);
      color: var(--color-background);
    }
  }
}
</style>
