{"version": 3, "file": "workbox-streams.dev.js", "sources": ["../_version.js", "../concatenate.js", "../utils/createHeaders.js", "../concatenateToResponse.js", "../isSupported.js", "../strategy.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:streams:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { Deferred } from 'workbox-core/_private/Deferred.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\n/**\n * Takes either a Response, a ReadableStream, or a\n * [BodyInit](https://fetch.spec.whatwg.org/#bodyinit) and returns the\n * ReadableStreamReader object associated with it.\n *\n * @param {workbox-streams.StreamSource} source\n * @return {ReadableStreamReader}\n * @private\n */\nfunction _getReaderFromSource(source) {\n    if (source instanceof Response) {\n        // See https://github.com/GoogleChrome/workbox/issues/2998\n        if (source.body) {\n            return source.body.getReader();\n        }\n        throw new WorkboxError('opaque-streams-source', { type: source.type });\n    }\n    if (source instanceof ReadableStream) {\n        return source.getReader();\n    }\n    return new Response(source).body.getReader();\n}\n/**\n * Takes multiple source Promises, each of which could resolve to a Response, a\n * ReadableStream, or a [BodyInit](https://fetch.spec.whatwg.org/#bodyinit).\n *\n * Returns an object exposing a ReadableStream with each individual stream's\n * data returned in sequence, along with a Promise which signals when the\n * stream is finished (useful for passing to a FetchEvent's waitUntil()).\n *\n * @param {Array<Promise<workbox-streams.StreamSource>>} sourcePromises\n * @return {Object<{done: Promise, stream: ReadableStream}>}\n *\n * @memberof workbox-streams\n */\nfunction concatenate(sourcePromises) {\n    if (process.env.NODE_ENV !== 'production') {\n        assert.isArray(sourcePromises, {\n            moduleName: 'workbox-streams',\n            funcName: 'concatenate',\n            paramName: 'sourcePromises',\n        });\n    }\n    const readerPromises = sourcePromises.map((sourcePromise) => {\n        return Promise.resolve(sourcePromise).then((source) => {\n            return _getReaderFromSource(source);\n        });\n    });\n    const streamDeferred = new Deferred();\n    let i = 0;\n    const logMessages = [];\n    const stream = new ReadableStream({\n        pull(controller) {\n            return readerPromises[i]\n                .then((reader) => {\n                if (reader instanceof ReadableStreamDefaultReader) {\n                    return reader.read();\n                }\n                else {\n                    return;\n                }\n            })\n                .then((result) => {\n                if (result === null || result === void 0 ? void 0 : result.done) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        logMessages.push([\n                            'Reached the end of source:',\n                            sourcePromises[i],\n                        ]);\n                    }\n                    i++;\n                    if (i >= readerPromises.length) {\n                        // Log all the messages in the group at once in a single group.\n                        if (process.env.NODE_ENV !== 'production') {\n                            logger.groupCollapsed(`Concatenating ${readerPromises.length} sources.`);\n                            for (const message of logMessages) {\n                                if (Array.isArray(message)) {\n                                    logger.log(...message);\n                                }\n                                else {\n                                    logger.log(message);\n                                }\n                            }\n                            logger.log('Finished reading all sources.');\n                            logger.groupEnd();\n                        }\n                        controller.close();\n                        streamDeferred.resolve();\n                        return;\n                    }\n                    // The `pull` method is defined because we're inside it.\n                    return this.pull(controller);\n                }\n                else {\n                    controller.enqueue(result === null || result === void 0 ? void 0 : result.value);\n                }\n            })\n                .catch((error) => {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.error('An error occurred:', error);\n                }\n                streamDeferred.reject(error);\n                throw error;\n            });\n        },\n        cancel() {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.warn('The ReadableStream was cancelled.');\n            }\n            streamDeferred.resolve();\n        },\n    });\n    return { done: streamDeferred.promise, stream };\n}\nexport { concatenate };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * This is a utility method that determines whether the current browser supports\n * the features required to create streamed responses. Currently, it checks if\n * [`ReadableStream`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream/ReadableStream)\n * is available.\n *\n * @private\n * @param {HeadersInit} [headersInit] If there's no `Content-Type` specified,\n * `'text/html'` will be used by default.\n * @return {boolean} `true`, if the current browser meets the requirements for\n * streaming responses, and `false` otherwise.\n *\n * @memberof workbox-streams\n */\nfunction createHeaders(headersInit = {}) {\n    // See https://github.com/GoogleChrome/workbox/issues/1461\n    const headers = new Headers(headersInit);\n    if (!headers.has('content-type')) {\n        headers.set('content-type', 'text/html');\n    }\n    return headers;\n}\nexport { createHeaders };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { createHeaders } from './utils/createHeaders.js';\nimport { concatenate } from './concatenate.js';\nimport './_version.js';\n/**\n * Takes multiple source Promises, each of which could resolve to a Response, a\n * ReadableStream, or a [BodyInit](https://fetch.spec.whatwg.org/#bodyinit),\n * along with a\n * [HeadersInit](https://fetch.spec.whatwg.org/#typedefdef-headersinit).\n *\n * Returns an object exposing a Response whose body consists of each individual\n * stream's data returned in sequence, along with a Promise which signals when\n * the stream is finished (useful for passing to a FetchEvent's waitUntil()).\n *\n * @param {Array<Promise<workbox-streams.StreamSource>>} sourcePromises\n * @param {HeadersInit} [headersInit] If there's no `Content-Type` specified,\n * `'text/html'` will be used by default.\n * @return {Object<{done: Promise, response: Response}>}\n *\n * @memberof workbox-streams\n */\nfunction concatenateToResponse(sourcePromises, headersInit) {\n    const { done, stream } = concatenate(sourcePromises);\n    const headers = createHeaders(headersInit);\n    const response = new Response(stream, { headers });\n    return { done, response };\n}\nexport { concatenateToResponse };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { canConstructReadableStream } from 'workbox-core/_private/canConstructReadableStream.js';\nimport './_version.js';\n/**\n * This is a utility method that determines whether the current browser supports\n * the features required to create streamed responses. Currently, it checks if\n * [`ReadableStream`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream/ReadableStream)\n * can be created.\n *\n * @return {boolean} `true`, if the current browser meets the requirements for\n * streaming responses, and `false` otherwise.\n *\n * @memberof workbox-streams\n */\nfunction isSupported() {\n    return canConstructReadableStream();\n}\nexport { isSupported };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { createHeaders } from './utils/createHeaders.js';\nimport { concatenateToResponse } from './concatenateToResponse.js';\nimport { isSupported } from './isSupported.js';\nimport './_version.js';\n/**\n * A shortcut to create a strategy that could be dropped-in to Workbox's router.\n *\n * On browsers that do not support constructing new `ReadableStream`s, this\n * strategy will automatically wait for all the `sourceFunctions` to complete,\n * and create a final response that concatenates their values together.\n *\n * @param {Array<function({event, request, url, params})>} sourceFunctions\n * An array of functions similar to {@link workbox-routing~handlerCallback}\n * but that instead return a {@link workbox-streams.StreamSource} (or a\n * Promise which resolves to one).\n * @param {HeadersInit} [headersInit] If there's no `Content-Type` specified,\n * `'text/html'` will be used by default.\n * @return {workbox-routing~handlerCallback}\n * @memberof workbox-streams\n */\nfunction strategy(sourceFunctions, headersInit) {\n    return async ({ event, request, url, params }) => {\n        const sourcePromises = sourceFunctions.map((fn) => {\n            // Ensure the return value of the function is always a promise.\n            return Promise.resolve(fn({ event, request, url, params }));\n        });\n        if (isSupported()) {\n            const { done, response } = concatenateToResponse(sourcePromises, headersInit);\n            if (event) {\n                event.waitUntil(done);\n            }\n            return response;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`The current browser doesn't support creating response ` +\n                `streams. Falling back to non-streaming response instead.`);\n        }\n        // Fallback to waiting for everything to finish, and concatenating the\n        // responses.\n        const blobPartsPromises = sourcePromises.map(async (sourcePromise) => {\n            const source = await sourcePromise;\n            if (source instanceof Response) {\n                return source.blob();\n            }\n            else {\n                // Technically, a `StreamSource` object can include any valid\n                // `BodyInit` type, including `FormData` and `URLSearchParams`, which\n                // cannot be passed to the Blob constructor directly, so we have to\n                // convert them to actual Blobs first.\n                return new Response(source).blob();\n            }\n        });\n        const blobParts = await Promise.all(blobPartsPromises);\n        const headers = createHeaders(headersInit);\n        // Constructing a new Response from a Blob source is well-supported.\n        // So is constructing a new Blob from multiple source Blobs or strings.\n        return new Response(new Blob(blobParts), { headers });\n    };\n}\nexport { strategy };\n"], "names": ["self", "_", "e", "_getReaderFromSource", "source", "Response", "body", "<PERSON><PERSON><PERSON><PERSON>", "WorkboxError", "type", "ReadableStream", "concatenate", "sourcePromises", "assert", "isArray", "moduleName", "funcName", "paramName", "readerPromises", "map", "sourcePromise", "Promise", "resolve", "then", "streamDef<PERSON>red", "Deferred", "i", "logMessages", "stream", "pull", "controller", "reader", "ReadableStreamDefaultReader", "read", "result", "done", "push", "length", "logger", "groupCollapsed", "message", "Array", "log", "groupEnd", "close", "enqueue", "value", "catch", "error", "reject", "cancel", "warn", "promise", "createHeaders", "headersInit", "headers", "Headers", "has", "set", "concatenateToResponse", "response", "isSupported", "canConstructReadableStream", "strategy", "sourceFunctions", "event", "request", "url", "params", "fn", "waitUntil", "blobPartsPromises", "blob", "blobParts", "all", "Blob"], "mappings": ";;;;IACA;IACA,IAAI;IACAA,EAAAA,IAAI,CAAC,uBAAuB,CAAC,IAAIC,CAAC,EAAE,CAAA;IACxC,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASC,oBAAoBA,CAACC,MAAM,EAAE;MAClC,IAAIA,MAAM,YAAYC,QAAQ,EAAE;IAC5B;QACA,IAAID,MAAM,CAACE,IAAI,EAAE;IACb,MAAA,OAAOF,MAAM,CAACE,IAAI,CAACC,SAAS,EAAE,CAAA;IAClC,KAAA;IACA,IAAA,MAAM,IAAIC,4BAAY,CAAC,uBAAuB,EAAE;UAAEC,IAAI,EAAEL,MAAM,CAACK,IAAAA;IAAK,KAAC,CAAC,CAAA;IAC1E,GAAA;MACA,IAAIL,MAAM,YAAYM,cAAc,EAAE;IAClC,IAAA,OAAON,MAAM,CAACG,SAAS,EAAE,CAAA;IAC7B,GAAA;MACA,OAAO,IAAIF,QAAQ,CAACD,MAAM,CAAC,CAACE,IAAI,CAACC,SAAS,EAAE,CAAA;IAChD,CAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASI,WAAWA,CAACC,cAAc,EAAE;IACjC,EAA2C;IACvCC,IAAAA,gBAAM,CAACC,OAAO,CAACF,cAAc,EAAE;IAC3BG,MAAAA,UAAU,EAAE,iBAAiB;IAC7BC,MAAAA,QAAQ,EAAE,aAAa;IACvBC,MAAAA,SAAS,EAAE,gBAAA;IACf,KAAC,CAAC,CAAA;IACN,GAAA;IACA,EAAA,MAAMC,cAAc,GAAGN,cAAc,CAACO,GAAG,CAAEC,aAAa,IAAK;QACzD,OAAOC,OAAO,CAACC,OAAO,CAACF,aAAa,CAAC,CAACG,IAAI,CAAEnB,MAAM,IAAK;UACnD,OAAOD,oBAAoB,CAACC,MAAM,CAAC,CAAA;IACvC,KAAC,CAAC,CAAA;IACN,GAAC,CAAC,CAAA;IACF,EAAA,MAAMoB,cAAc,GAAG,IAAIC,oBAAQ,EAAE,CAAA;MACrC,IAAIC,CAAC,GAAG,CAAC,CAAA;MACT,MAAMC,WAAW,GAAG,EAAE,CAAA;IACtB,EAAA,MAAMC,MAAM,GAAG,IAAIlB,cAAc,CAAC;QAC9BmB,IAAIA,CAACC,UAAU,EAAE;UACb,OAAOZ,cAAc,CAACQ,CAAC,CAAC,CACnBH,IAAI,CAAEQ,MAAM,IAAK;YAClB,IAAIA,MAAM,YAAYC,2BAA2B,EAAE;IAC/C,UAAA,OAAOD,MAAM,CAACE,IAAI,EAAE,CAAA;IACxB,SAAC,MACI;IACD,UAAA,OAAA;IACJ,SAAA;IACJ,OAAC,CAAC,CACGV,IAAI,CAAEW,MAAM,IAAK;IAClB,QAAA,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,IAAI,EAAE;IAC7D,UAA2C;gBACvCR,WAAW,CAACS,IAAI,CAAC,CACb,4BAA4B,EAC5BxB,cAAc,CAACc,CAAC,CAAC,CACpB,CAAC,CAAA;IACN,WAAA;IACAA,UAAAA,CAAC,EAAE,CAAA;IACH,UAAA,IAAIA,CAAC,IAAIR,cAAc,CAACmB,MAAM,EAAE;IAC5B;IACA,YAA2C;kBACvCC,gBAAM,CAACC,cAAc,CAAE,CAAA,cAAA,EAAgBrB,cAAc,CAACmB,MAAO,WAAU,CAAC,CAAA;IACxE,cAAA,KAAK,MAAMG,OAAO,IAAIb,WAAW,EAAE;IAC/B,gBAAA,IAAIc,KAAK,CAAC3B,OAAO,CAAC0B,OAAO,CAAC,EAAE;IACxBF,kBAAAA,gBAAM,CAACI,GAAG,CAAC,GAAGF,OAAO,CAAC,CAAA;IAC1B,iBAAC,MACI;IACDF,kBAAAA,gBAAM,CAACI,GAAG,CAACF,OAAO,CAAC,CAAA;IACvB,iBAAA;IACJ,eAAA;IACAF,cAAAA,gBAAM,CAACI,GAAG,CAAC,+BAA+B,CAAC,CAAA;kBAC3CJ,gBAAM,CAACK,QAAQ,EAAE,CAAA;IACrB,aAAA;gBACAb,UAAU,CAACc,KAAK,EAAE,CAAA;gBAClBpB,cAAc,CAACF,OAAO,EAAE,CAAA;IACxB,YAAA,OAAA;IACJ,WAAA;IACA;IACA,UAAA,OAAO,IAAI,CAACO,IAAI,CAACC,UAAU,CAAC,CAAA;IAChC,SAAC,MACI;IACDA,UAAAA,UAAU,CAACe,OAAO,CAACX,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACY,KAAK,CAAC,CAAA;IACpF,SAAA;IACJ,OAAC,CAAC,CACGC,KAAK,CAAEC,KAAK,IAAK;IAClB,QAA2C;IACvCV,UAAAA,gBAAM,CAACU,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC,CAAA;IAC7C,SAAA;IACAxB,QAAAA,cAAc,CAACyB,MAAM,CAACD,KAAK,CAAC,CAAA;IAC5B,QAAA,MAAMA,KAAK,CAAA;IACf,OAAC,CAAC,CAAA;SACL;IACDE,IAAAA,MAAMA,GAAG;IACL,MAA2C;IACvCZ,QAAAA,gBAAM,CAACa,IAAI,CAAC,mCAAmC,CAAC,CAAA;IACpD,OAAA;UACA3B,cAAc,CAACF,OAAO,EAAE,CAAA;IAC5B,KAAA;IACJ,GAAC,CAAC,CAAA;MACF,OAAO;QAAEa,IAAI,EAAEX,cAAc,CAAC4B,OAAO;IAAExB,IAAAA,MAAAA;OAAQ,CAAA;IACnD;;IC7HA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASyB,aAAaA,CAACC,WAAW,GAAG,EAAE,EAAE;IACrC;IACA,EAAA,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAACF,WAAW,CAAC,CAAA;IACxC,EAAA,IAAI,CAACC,OAAO,CAACE,GAAG,CAAC,cAAc,CAAC,EAAE;IAC9BF,IAAAA,OAAO,CAACG,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAA;IAC5C,GAAA;IACA,EAAA,OAAOH,OAAO,CAAA;IAClB;;IC7BA;IACA;AACA;IACA;IACA;IACA;IACA;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASI,qBAAqBA,CAAC/C,cAAc,EAAE0C,WAAW,EAAE;MACxD,MAAM;QAAEnB,IAAI;IAAEP,IAAAA,MAAAA;IAAO,GAAC,GAAGjB,WAAW,CAACC,cAAc,CAAC,CAAA;IACpD,EAAA,MAAM2C,OAAO,GAAGF,aAAa,CAACC,WAAW,CAAC,CAAA;IAC1C,EAAA,MAAMM,QAAQ,GAAG,IAAIvD,QAAQ,CAACuB,MAAM,EAAE;IAAE2B,IAAAA,OAAAA;IAAQ,GAAC,CAAC,CAAA;MAClD,OAAO;QAAEpB,IAAI;IAAEyB,IAAAA,QAAAA;OAAU,CAAA;IAC7B;;IChCA;IACA;AACA;IACA;IACA;IACA;IACA;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASC,WAAWA,GAAG;MACnB,OAAOC,wDAA0B,EAAE,CAAA;IACvC;;ICtBA;IACA;AACA;IACA;IACA;IACA;IACA;IAMA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASC,QAAQA,CAACC,eAAe,EAAEV,WAAW,EAAE;IAC5C,EAAA,OAAO,OAAO;QAAEW,KAAK;QAAEC,OAAO;QAAEC,GAAG;IAAEC,IAAAA,MAAAA;IAAO,GAAC,KAAK;IAC9C,IAAA,MAAMxD,cAAc,GAAGoD,eAAe,CAAC7C,GAAG,CAAEkD,EAAE,IAAK;IAC/C;IACA,MAAA,OAAOhD,OAAO,CAACC,OAAO,CAAC+C,EAAE,CAAC;YAAEJ,KAAK;YAAEC,OAAO;YAAEC,GAAG;IAAEC,QAAAA,MAAAA;IAAO,OAAC,CAAC,CAAC,CAAA;IAC/D,KAAC,CAAC,CAAA;QACF,IAAIP,WAAW,EAAE,EAAE;UACf,MAAM;YAAE1B,IAAI;IAAEyB,QAAAA,QAAAA;IAAS,OAAC,GAAGD,qBAAqB,CAAC/C,cAAc,EAAE0C,WAAW,CAAC,CAAA;IAC7E,MAAA,IAAIW,KAAK,EAAE;IACPA,QAAAA,KAAK,CAACK,SAAS,CAACnC,IAAI,CAAC,CAAA;IACzB,OAAA;IACA,MAAA,OAAOyB,QAAQ,CAAA;IACnB,KAAA;IACA,IAA2C;IACvCtB,MAAAA,gBAAM,CAACI,GAAG,CAAE,CAAuD,sDAAA,CAAA,GAC9D,0DAAyD,CAAC,CAAA;IACnE,KAAA;IACA;IACA;QACA,MAAM6B,iBAAiB,GAAG3D,cAAc,CAACO,GAAG,CAAC,MAAOC,aAAa,IAAK;UAClE,MAAMhB,MAAM,GAAG,MAAMgB,aAAa,CAAA;UAClC,IAAIhB,MAAM,YAAYC,QAAQ,EAAE;IAC5B,QAAA,OAAOD,MAAM,CAACoE,IAAI,EAAE,CAAA;IACxB,OAAC,MACI;IACD;IACA;IACA;IACA;YACA,OAAO,IAAInE,QAAQ,CAACD,MAAM,CAAC,CAACoE,IAAI,EAAE,CAAA;IACtC,OAAA;IACJ,KAAC,CAAC,CAAA;QACF,MAAMC,SAAS,GAAG,MAAMpD,OAAO,CAACqD,GAAG,CAACH,iBAAiB,CAAC,CAAA;IACtD,IAAA,MAAMhB,OAAO,GAAGF,aAAa,CAACC,WAAW,CAAC,CAAA;IAC1C;IACA;QACA,OAAO,IAAIjD,QAAQ,CAAC,IAAIsE,IAAI,CAACF,SAAS,CAAC,EAAE;IAAElB,MAAAA,OAAAA;IAAQ,KAAC,CAAC,CAAA;OACxD,CAAA;IACL;;;;;;;;;;;;;"}