import{u as K,a as te,A as se}from"./AppLayout-DAG3tKFc.js";import{a as L,r as s,c as oe,q as W,o as Y,J as Z,i,p as R,h as e,j as M,O as ae,F as E,k as B,C as ie,m as a,G as re,t as d,_ as D,g as p,S as X,y as G,s as I,l as O,n as H,f as F,w as ee,H as ne,B as le,v as N,A as j,x as ce,P as Q,e as ue}from"./index-Dz-_Smvi.js";import{u as de,a as ve}from"./contact-sLg3zrn9.js";/* empty css                                                                            */import{v as fe}from"./validation-C5k5TbW8.js";const pe={class:"default-item"},ge=["src","alt"],me={key:1,class:"item-content"},ye={key:0,class:"item-title"},_e={key:1,class:"item-description"},he={key:2,class:"carousel-dots"},be=["onClick","aria-label"],ke=L({__name:"Carousel",props:{items:{},autoplay:{type:Boolean,default:!1},interval:{default:3e3},loop:{type:Boolean,default:!0},showArrows:{type:Boolean,default:!0},showDots:{type:Boolean,default:!0},vertical:{type:Boolean,default:!1},height:{default:"300px"},itemKey:{default:"id"}},emits:["change","click"],setup(U,{emit:T}){ie(c=>({b07ff84a:c.height}));const m=U,C=T,l=s(0),S=s(),r=s(),v=s(0),w=s(0),x=s(0),V=s(0),P=oe(()=>{const c=-l.value*100;return{transform:`${m.vertical?"translateY":"translateX"}(${c}%)`,height:m.vertical?`${m.items.length*100}%`:"100%"}}),$=(c,k)=>m.itemKey&&c[m.itemKey]?c[m.itemKey]:k,f=c=>{if(c===l.value)return;const k=l.value;l.value=c,C("change",l.value,k)},g=()=>{l.value<m.items.length-1?f(l.value+1):m.loop&&f(0)},n=()=>{l.value>0?f(l.value-1):m.loop&&f(m.items.length-1)},t=()=>{m.autoplay&&m.items.length>1&&(r.value=setInterval(()=>{g()},m.interval))},o=()=>{r.value&&(clearInterval(r.value),r.value=void 0)},_=c=>{v.value=c.touches[0].clientX,w.value=c.touches[0].clientY,o()},y=c=>{c.preventDefault()},h=c=>{x.value=c.changedTouches[0].clientX,V.value=c.changedTouches[0].clientY;const k=x.value-v.value,b=V.value-w.value,u=50;m.vertical?Math.abs(b)>u&&(b>0?n():g()):Math.abs(k)>u&&(k>0?n():g()),t()};return W(()=>m.autoplay,c=>{c?t():o()}),W(()=>m.items.length,()=>{l.value>=m.items.length&&(l.value=Math.max(0,m.items.length-1))}),Y(()=>{t()}),Z(()=>{o()}),(c,k)=>(a(),i("div",{class:R(["carousel",{"carousel--vertical":c.vertical}])},[e("div",{class:"carousel-container",ref_key:"containerRef",ref:S},[e("div",{class:"carousel-track",style:ae(P.value),onTouchstart:_,onTouchmove:y,onTouchend:h},[(a(!0),i(E,null,B(c.items,(b,u)=>(a(),i("div",{key:$(b,u),class:R(["carousel-item",{active:u===l.value}])},[re(c.$slots,"default",{item:b,index:u,active:u===l.value},()=>[e("div",pe,[b.image?(a(),i("img",{key:0,src:b.image,alt:b.title||`轮播项 ${u+1}`},null,8,ge)):M("",!0),b.title||b.description?(a(),i("div",me,[b.title?(a(),i("h3",ye,d(b.title),1)):M("",!0),b.description?(a(),i("p",_e,d(b.description),1)):M("",!0)])):M("",!0)])],!0)],2))),128))],36)],512),c.showArrows&&c.items.length>1?(a(),i("button",{key:0,class:R(["carousel-arrow carousel-arrow--prev",{disabled:l.value===0&&!c.loop}]),onClick:n,"aria-label":"上一项"},k[0]||(k[0]=[e("i",{class:"icon-arrow-left"},null,-1)]),2)):M("",!0),c.showArrows&&c.items.length>1?(a(),i("button",{key:1,class:R(["carousel-arrow carousel-arrow--next",{disabled:l.value===c.items.length-1&&!c.loop}]),onClick:g,"aria-label":"下一项"},k[1]||(k[1]=[e("i",{class:"icon-arrow-right"},null,-1)]),2)):M("",!0),c.showDots&&c.items.length>1?(a(),i("div",he,[(a(!0),i(E,null,B(c.items,(b,u)=>(a(),i("button",{key:u,class:R(["carousel-dot",{active:u===l.value}]),onClick:q=>f(u),"aria-label":`跳转到第 ${u+1} 项`},null,10,be))),128))])):M("",!0)],2))}}),we=D(ke,[["__scopeId","data-v-38787131"]]),$e="/images/hero-bg.svg",Re={class:"hero-background"},Se={class:"hero-content"},Te={class:"container"},Ce={class:"hero-title"},xe={class:"stat-label"},qe=["onClick"],Ae={class:"feature-icon"},Me={class:"feature-title"},Ve={class:"feature-description"},Ie=L({__name:"HeroSection",setup(U){p.registerPlugin(X);const{navigateToServices:T,navigateToContact:m,navigateToServices:C}=K(),l=s(),S=s(),r=s(),v=s(),w=s(),x=s(),V=s(),P=s(),$=s(),f=s({}),g=s([]),n=s([{key:"projects",label:"完成项目",value:500,suffix:"+"},{key:"clients",label:"满意客户",value:200,suffix:"+"},{key:"years",label:"行业经验",value:10,suffix:"年"},{key:"team",label:"专业团队",value:50,suffix:"人"}]),t=s([{id:"design",title:"创意设计",description:"专业的包装设计团队，为您打造独特的品牌形象",icon:"icon-design",category:"包装设计"},{id:"production",title:"生产制造",description:"先进的生产设备，严格的质量控制，确保产品品质",icon:"icon-production",category:"包装生产"},{id:"consulting",title:"专业咨询",description:"资深行业专家，为您提供专业的包装解决方案",icon:"icon-consulting",category:"包装咨询"},{id:"logistics",title:"物流配送",description:"完善的物流体系，确保产品安全快速到达",icon:"icon-logistics",category:"包装物流"}]),o=G({projects:0,clients:0,years:0,team:0}),_=(u,q)=>{u&&(f.value[q]=u)},y=(u,q)=>{u&&(g.value[q]=u)},h=()=>{const u=l.value?.querySelector(".hero-background");u&&(u.style.background="linear-gradient(135deg, #1a365d 0%, #2d3748 100%)")},c=()=>{const u=l.value?.nextElementSibling;u&&u.scrollIntoView({behavior:"smooth"})},k=()=>{n.value.forEach(u=>{p.to(o,{[u.key]:u.value,duration:2,ease:"power2.out",scrollTrigger:{trigger:x.value,start:"top 80%",once:!0}})})},b=async()=>{await H();const u=p.timeline();u.from([r.value,v.value],{y:100,opacity:0,duration:1,stagger:.2,ease:"power3.out"}),u.from(w.value,{y:50,opacity:0,duration:.8,ease:"power2.out"},"-=0.5"),u.from(V.value?.children||[],{y:30,opacity:0,duration:.6,stagger:.1,ease:"power2.out"},"-=0.3"),u.from(g.value,{y:60,opacity:0,duration:.8,stagger:.15,ease:"power2.out"},"-=0.4"),u.from($.value,{opacity:0,duration:.5,ease:"power2.out"},"-=0.2"),p.to($.value?.querySelector(".scroll-arrow"),{y:10,duration:1.5,repeat:-1,yoyo:!0,ease:"power2.inOut"}),p.to(".background-image",{yPercent:-50,ease:"none",scrollTrigger:{trigger:l.value,start:"top bottom",end:"bottom top",scrub:!0}}),k()};return Y(()=>{b()}),Z(()=>{X.getAll().forEach(u=>u.kill())}),(u,q)=>(a(),i("section",{class:"hero-section",ref_key:"heroRef",ref:l},[e("div",Re,[q[2]||(q[2]=e("div",{class:"background-overlay"},null,-1)),e("img",{src:$e,alt:"包装解决方案背景",class:"background-image",onError:h},null,32)]),e("div",Se,[e("div",Te,[e("div",{class:"hero-text",ref_key:"heroTextRef",ref:S},[e("h1",Ce,[e("span",{class:"title-line",ref_key:"titleLine1",ref:r},"专业包装",512),e("span",{class:"title-line",ref_key:"titleLine2",ref:v},"一站式解决方案",512)]),e("p",{class:"hero-subtitle",ref_key:"subtitleRef",ref:w}," 从创意设计到生产制造，为您提供全方位的包装服务 ",512),e("div",{class:"hero-stats",ref_key:"statsRef",ref:x},[(a(!0),i(E,null,B(n.value,A=>(a(),i("div",{class:"stat-item",key:A.label},[e("div",{class:"stat-number",ref_for:!0,ref:z=>_(z,A.key)},d(o[A.key])+d(A.suffix),513),e("div",xe,d(A.label),1)]))),128))],512),e("div",{class:"hero-actions",ref_key:"actionsRef",ref:V},[e("button",{class:"btn btn--primary btn--lg hero-cta",onClick:q[0]||(q[0]=(...A)=>I(T)&&I(T)(...A))},q[3]||(q[3]=[O(" 了解我们的服务 ",-1),e("i",{class:"icon-arrow-right"},null,-1)])),e("button",{class:"btn btn--secondary btn--lg",onClick:q[1]||(q[1]=(...A)=>I(m)&&I(m)(...A))}," 立即咨询 ")],512)],512),e("div",{class:"hero-features",ref_key:"featuresRef",ref:P},[(a(!0),i(E,null,B(t.value,(A,z)=>(a(),i("div",{class:"feature-card",key:A.id,ref_for:!0,ref:J=>y(J,z),onClick:J=>I(C)(A.category)},[e("div",Ae,[e("i",{class:R(A.icon)},null,2)]),e("h3",Me,d(A.title),1),e("p",Ve,d(A.description),1)],8,qe))),128))],512)])]),e("div",{class:"scroll-indicator",ref_key:"scrollIndicatorRef",ref:$,onClick:c},q[4]||(q[4]=[e("div",{class:"scroll-text"},"向下滚动",-1),e("div",{class:"scroll-arrow"},[e("i",{class:"icon-arrow-down"})],-1)]),512)],512))}}),Ee=D(Ie,[["__scopeId","data-v-cf1913ab"]]),Be={class:"container"},Pe=["onClick","onMouseenter","onMouseleave"],Oe={class:"service-icon"},Ne={class:"service-content"},Ue={class:"service-title"},Le={class:"service-description"},De={class:"service-features"},Fe={class:"service-footer"},Xe={class:"service-category"},Ye=L({__name:"ServicesOverview",setup(U){p.registerPlugin(X),de();const{navigateToServices:T,navigateToServices:m}=K(),C=s(),l=s(),S=s(),r=s(),v=s([]),w=s([{id:"1",title:"包装设计",description:"专业的包装设计团队，为您的产品打造独特的视觉形象，提升品牌价值。",category:"设计服务",icon:"icon-design",features:["创意设计","品牌一致性","市场导向","用户体验"]},{id:"2",title:"包装生产",description:"先进的生产设备和严格的质量控制，确保每一个包装产品的品质。",category:"生产服务",icon:"icon-production",features:["质量保证","快速交付","成本优化","环保材料"]},{id:"3",title:"包装咨询",description:"资深行业专家为您提供专业的包装解决方案和技术支持。",category:"咨询服务",icon:"icon-consulting",features:["专业建议","技术支持","成本分析","市场调研"]},{id:"4",title:"包装测试",description:"全面的包装测试服务，确保产品在运输和储存过程中的安全性。",category:"测试服务",icon:"icon-testing",features:["安全测试","耐久性测试","环境测试","质量认证"]},{id:"5",title:"包装物流",description:"完善的物流配送体系，确保包装产品安全快速地到达目的地。",category:"物流服务",icon:"icon-logistics",features:["快速配送","安全包装","跟踪服务","全国覆盖"]},{id:"6",title:"环保包装",description:"绿色环保的包装解决方案，为可持续发展贡献力量。",category:"环保服务",icon:"icon-eco",features:["可回收材料","生物降解","节能减排","绿色认证"]}]),x=($,f)=>{$&&(v.value[f]=$)},V=($,f)=>{const g=v.value[$];if(!g)return;const n=p.timeline();f?n.to(g.querySelector(".service-icon"),{scale:1.1,rotation:5,duration:.3,ease:"power2.out"}).to(g.querySelector(".card-decoration"),{scale:1.1,opacity:.8,duration:.3,ease:"power2.out"},0).to(g.querySelector(".hover-overlay"),{opacity:1,duration:.3,ease:"power2.out"},0):n.to(g.querySelector(".service-icon"),{scale:1,rotation:0,duration:.3,ease:"power2.out"}).to(g.querySelector(".card-decoration"),{scale:1,opacity:.6,duration:.3,ease:"power2.out"},0).to(g.querySelector(".hover-overlay"),{opacity:0,duration:.3,ease:"power2.out"},0)},P=async()=>{await H(),p.from(l.value?.children||[],{y:50,opacity:0,duration:.8,stagger:.2,ease:"power2.out",scrollTrigger:{trigger:l.value,start:"top 80%",once:!0}}),p.from(v.value,{y:80,opacity:0,duration:.8,stagger:.15,ease:"power2.out",scrollTrigger:{trigger:S.value,start:"top 80%",once:!0}}),p.from(r.value,{y:30,opacity:0,duration:.6,ease:"power2.out",scrollTrigger:{trigger:r.value,start:"top 90%",once:!0}})};return Y(()=>{P()}),($,f)=>(a(),i("section",{class:"services-overview",ref_key:"sectionRef",ref:C},[e("div",Be,[e("div",{class:"section-header",ref_key:"headerRef",ref:l},f[1]||(f[1]=[e("h2",{class:"section-title"},"我们的服务",-1),e("p",{class:"section-subtitle"}," 专业的包装解决方案，满足您的各种需求 ",-1)]),512),e("div",{class:"services-grid",ref_key:"gridRef",ref:S},[(a(!0),i(E,null,B(w.value,(g,n)=>(a(),i("div",{key:g.id,class:"service-card",ref_for:!0,ref:t=>x(t,n),onClick:t=>I(m)(g.category),onMouseenter:t=>V(n,!0),onMouseleave:t=>V(n,!1)},[f[4]||(f[4]=e("div",{class:"card-decoration"},[e("div",{class:"decoration-circle"}),e("div",{class:"decoration-triangle"})],-1)),e("div",Oe,[e("i",{class:R(g.icon)},null,2)]),e("div",Ne,[e("h3",Ue,d(g.title),1),e("p",Le,d(g.description),1),e("ul",De,[(a(!0),i(E,null,B(g.features.slice(0,3),t=>(a(),i("li",{key:t},[f[2]||(f[2]=e("i",{class:"icon-check"},null,-1)),O(" "+d(t),1)]))),128))])]),e("div",Fe,[e("span",Xe,d(g.category),1),f[3]||(f[3]=e("button",{class:"service-link"},[O(" 了解更多 "),e("i",{class:"icon-arrow-right"})],-1))]),f[5]||(f[5]=e("div",{class:"hover-overlay"},null,-1))],40,Pe))),128))],512),e("div",{class:"section-footer",ref_key:"footerRef",ref:r},[e("button",{class:"btn btn--primary btn--lg",onClick:f[0]||(f[0]=(...g)=>I(T)&&I(T)(...g))},f[6]||(f[6]=[O(" 查看全部服务 ",-1),e("i",{class:"icon-arrow-right"},null,-1)]))],512)])],512))}}),je=D(Ye,[["__scopeId","data-v-98ffaba7"]]),He="/images/company-advantages.svg",Ke={class:"container"},ze={class:"advantages-layout"},Ge={class:"advantages-list"},Je=["onMouseenter","onMouseleave"],We={class:"advantage-icon"},Qe={class:"advantage-text"},Ze={class:"advantage-title"},et={class:"advantage-description"},tt={class:"advantage-number"},st={class:"visual-container"},ot={class:"main-image"},at={class:"floating-cards"},it={class:"card-icon"},rt={class:"card-content"},nt={class:"card-number"},lt={class:"card-label"},ct=L({__name:"CompanyAdvantages",setup(U){p.registerPlugin(X);const{navigateToAbout:T}=K(),m=s(),C=s(),l=s(),S=s(),r=s([]),v=s([]),w=s([{id:"1",title:"专业团队",description:"拥有10年以上行业经验的专业设计师和工程师团队，为您提供最专业的服务。",icon:"icon-team"},{id:"2",title:"先进设备",description:"引进国际先进的生产设备和检测仪器，确保产品质量和生产效率。",icon:"icon-equipment"},{id:"3",title:"质量保证",description:"严格的质量管理体系，通过ISO9001认证，每个环节都有严格的质量控制。",icon:"icon-quality"},{id:"4",title:"快速响应",description:"24小时客服支持，快速响应客户需求，提供及时的技术支持和解决方案。",icon:"icon-support"}]),x=s([{id:"1",number:"500+",label:"成功项目",icon:"icon-project"},{id:"2",number:"98%",label:"客户满意度",icon:"icon-satisfaction"},{id:"3",number:"24h",label:"响应时间",icon:"icon-time"}]),V=(n,t)=>{n&&(r.value[t]=n)},P=(n,t)=>{n&&(v.value[t]=n)},$=(n,t)=>{const o=r.value[n];o&&(p.to(o.querySelector(".advantage-icon"),{scale:t?1.1:1,rotation:t?5:0,duration:.3,ease:"power2.out"}),p.to(o.querySelector(".advantage-number"),{scale:t?1.2:1,opacity:t?1:.6,duration:.3,ease:"power2.out"}))},f=()=>{const n=l.value?.querySelector(".main-image");n&&(n.style.background="linear-gradient(135deg, #1a365d 0%, #2d3748 100%)",n.innerHTML='<div style="color: white; text-align: center; padding: 2rem;">公司优势展示</div>')},g=async()=>{await H(),p.from(C.value?.querySelector(".section-header")?.children||[],{y:50,opacity:0,duration:.8,stagger:.2,ease:"power2.out",scrollTrigger:{trigger:C.value,start:"top 80%",once:!0}}),p.from(r.value,{x:-50,opacity:0,duration:.8,stagger:.2,ease:"power2.out",scrollTrigger:{trigger:r.value[0],start:"top 80%",once:!0}}),p.from(S.value,{y:30,opacity:0,duration:.6,ease:"power2.out",scrollTrigger:{trigger:S.value,start:"top 90%",once:!0}}),p.from(l.value?.querySelector(".main-image"),{scale:.8,opacity:0,duration:1,ease:"power2.out",scrollTrigger:{trigger:l.value,start:"top 80%",once:!0}}),v.value.forEach((n,t)=>{p.from(n,{y:100,opacity:0,duration:.8,delay:t*.2,ease:"power2.out",scrollTrigger:{trigger:n,start:"top 90%",once:!0}}),p.to(n,{y:-10,duration:2+t*.5,repeat:-1,yoyo:!0,ease:"power2.inOut",delay:t*.3})}),p.to(".decoration--1",{rotation:360,duration:20,repeat:-1,ease:"none"}),p.to(".decoration--2",{rotation:-360,duration:15,repeat:-1,ease:"none"}),p.to(".decoration--3",{scale:1.2,duration:3,repeat:-1,yoyo:!0,ease:"power2.inOut"})};return Y(()=>{g()}),(n,t)=>(a(),i("section",{class:"company-advantages",ref_key:"sectionRef",ref:m},[e("div",Ke,[e("div",ze,[e("div",{class:"advantages-content",ref_key:"contentRef",ref:C},[t[2]||(t[2]=e("div",{class:"section-header"},[e("h2",{class:"section-title"},"为什么选择我们"),e("p",{class:"section-subtitle"}," 多年行业经验，专业团队，为您提供最优质的包装解决方案 ")],-1)),e("div",Ge,[(a(!0),i(E,null,B(w.value,(o,_)=>(a(),i("div",{key:o.id,class:"advantage-item",ref_for:!0,ref:y=>V(y,_),onMouseenter:y=>$(_,!0),onMouseleave:y=>$(_,!1)},[e("div",We,[e("i",{class:R(o.icon)},null,2)]),e("div",Qe,[e("h3",Ze,d(o.title),1),e("p",et,d(o.description),1)]),e("div",tt,d(String(_+1).padStart(2,"0")),1)],40,Je))),128))]),e("div",{class:"advantages-cta",ref_key:"ctaRef",ref:S},[e("button",{class:"btn btn--primary btn--lg",onClick:t[0]||(t[0]=(...o)=>I(T)&&I(T)(...o))},t[1]||(t[1]=[O(" 了解更多关于我们 ",-1),e("i",{class:"icon-arrow-right"},null,-1)]))],512)],512),e("div",{class:"advantages-visual",ref_key:"visualRef",ref:l},[e("div",st,[e("div",ot,[e("img",{src:He,alt:"公司优势展示",onError:f},null,32)]),e("div",at,[(a(!0),i(E,null,B(x.value,(o,_)=>(a(),i("div",{key:o.id,class:R(["floating-card",`floating-card--${_+1}`]),ref_for:!0,ref:y=>P(y,_)},[e("div",it,[e("i",{class:R(o.icon)},null,2)]),e("div",rt,[e("div",nt,d(o.number),1),e("div",lt,d(o.label),1)])],2))),128))]),t[3]||(t[3]=e("div",{class:"decorative-elements"},[e("div",{class:"decoration decoration--1"}),e("div",{class:"decoration decoration--2"}),e("div",{class:"decoration decoration--3"})],-1))])],512)])])],512))}}),ut=D(ct,[["__scopeId","data-v-d771447d"]]),dt={class:"container"},vt={class:"case-layout"},ft={class:"case-image"},pt=["src","alt"],gt={class:"image-overlay"},mt=["onClick"],yt={class:"case-content"},_t={class:"case-meta"},ht={class:"case-industry"},bt={class:"case-date"},kt={class:"case-title"},wt={class:"case-description"},$t={class:"case-results"},Rt={class:"results-list"},St={class:"case-client"},Tt={class:"client-info"},Ct={key:0,class:"client-testimonial"},xt={class:"stats-grid"},qt={class:"stat-icon"},At={class:"stat-content"},Mt={class:"stat-label"},Vt=L({__name:"CaseShowcase",setup(U){p.registerPlugin(X);const{navigateToCases:T,navigateToCases:m}=K(),C=s(),l=s(),S=s(),r=s(),v=s(),w=s([]),x=s({}),V=s([{id:"1",title:"电子产品包装升级项目",description:"为知名电子品牌设计了全新的产品包装系列，提升了品牌形象和用户体验。",client:"科技公司A",industry:"电子科技",date:"2024-01-15",image:"/images/case1.svg",results:["包装成本降低30%","品牌认知度提升50%","客户满意度达到95%","环保评分提升40%"],testimonial:{content:"新的包装设计不仅降低了成本，还大大提升了我们的品牌形象。",author:"张总",position:"科技公司A CEO"}},{id:"2",title:"食品包装安全升级",description:"为食品企业提供了符合最新安全标准的包装解决方案，确保产品质量和安全。",client:"食品公司B",industry:"食品饮料",date:"2024-02-20",image:"/images/case2.svg",results:["保鲜期延长20%","安全性提升100%","客户投诉减少80%","通过食品安全认证"],testimonial:{content:"专业的包装解决方案让我们的产品更安全，客户更放心。",author:"李经理",position:"食品公司B 质量总监"}},{id:"3",title:"奢侈品包装定制",description:"为高端奢侈品牌打造了独特的包装设计，完美诠释了品牌的奢华理念。",client:"奢侈品牌C",industry:"奢侈品",date:"2024-03-10",image:"/images/case3.svg",results:["品牌价值提升60%","包装识别度提升80%","客户复购率提升45%","获得设计大奖"],testimonial:{content:"精美的包装设计完美体现了我们品牌的高端定位。",author:"王总",position:"奢侈品牌C 品牌总监"}}]),P=s(V.value.map(_=>({id:_.id,title:_.title,description:_.description,image:_.image,..._}))),$=s([{key:"totalCases",label:"成功案例",value:150,suffix:"+",icon:"icon-cases"},{key:"industries",label:"服务行业",value:20,suffix:"+",icon:"icon-industries"},{key:"satisfaction",label:"客户满意度",value:98,suffix:"%",icon:"icon-satisfaction"},{key:"awards",label:"获得奖项",value:15,suffix:"+",icon:"icon-awards"}]),f=G({totalCases:0,industries:0,satisfaction:0,awards:0}),g=(_,y)=>{_&&(w.value[y]=_)},n=(_,y)=>{_&&(x.value[y]=_)},t=()=>{$.value.forEach(_=>{p.to(f,{[_.key]:_.value,duration:2,ease:"power2.out",scrollTrigger:{trigger:r.value,start:"top 80%",once:!0}})})},o=async()=>{await H(),p.from(l.value?.children||[],{y:50,opacity:0,duration:.8,stagger:.2,ease:"power2.out",scrollTrigger:{trigger:l.value,start:"top 80%",once:!0}}),p.from(S.value,{y:80,opacity:0,duration:1,ease:"power2.out",scrollTrigger:{trigger:S.value,start:"top 80%",once:!0}}),p.from(w.value,{y:60,opacity:0,duration:.8,stagger:.15,ease:"power2.out",scrollTrigger:{trigger:r.value,start:"top 80%",once:!0}}),p.from(v.value,{y:30,opacity:0,duration:.6,ease:"power2.out",scrollTrigger:{trigger:v.value,start:"top 90%",once:!0}}),t()};return Y(()=>{o()}),(_,y)=>(a(),i("section",{class:"case-showcase",ref_key:"sectionRef",ref:C},[e("div",dt,[e("div",{class:"section-header",ref_key:"headerRef",ref:l},y[1]||(y[1]=[e("h2",{class:"section-title"},"成功案例",-1),e("p",{class:"section-subtitle"}," 看看我们为客户创造的价值和成果 ",-1)]),512),e("div",{class:"cases-carousel",ref_key:"carouselRef",ref:S},[F(we,{items:P.value,autoplay:!0,interval:5e3,"show-dots":!0,"show-arrows":!0,height:"500px"},{default:ee(({item:h,index:c,active:k})=>[e("div",{class:R(["case-slide",{active:k}])},[e("div",vt,[e("div",ft,[e("img",{src:h.image,alt:h.title},null,8,pt),e("div",gt,[e("button",{class:"view-case-btn",onClick:b=>I(m)(h.id)},y[2]||(y[2]=[O(" 查看详情 ",-1),e("i",{class:"icon-arrow-right"},null,-1)]),8,mt)])]),e("div",yt,[e("div",_t,[e("span",ht,d(h.industry),1),e("span",bt,d(I(te)(h.date)),1)]),e("h3",kt,d(h.title),1),e("p",wt,d(h.description),1),e("div",$t,[y[4]||(y[4]=e("h4",{class:"results-title"},"项目成果",-1)),e("ul",Rt,[(a(!0),i(E,null,B(h.results.slice(0,3),b=>(a(),i("li",{key:b},[y[3]||(y[3]=e("i",{class:"icon-check"},null,-1)),O(" "+d(b),1)]))),128))])]),e("div",St,[e("div",Tt,[y[5]||(y[5]=e("strong",null,"客户：",-1)),O(d(h.client),1)]),h.testimonial?(a(),i("div",Ct,[e("blockquote",null,' "'+d(h.testimonial.content)+'" ',1),e("cite",null,d(h.testimonial.author)+" - "+d(h.testimonial.position),1)])):M("",!0)])])])],2)]),_:1},8,["items"])],512),e("div",{class:"case-stats",ref_key:"statsRef",ref:r},[e("div",xt,[(a(!0),i(E,null,B($.value,(h,c)=>(a(),i("div",{key:h.label,class:"stat-item",ref_for:!0,ref:k=>g(k,c)},[e("div",qt,[e("i",{class:R(h.icon)},null,2)]),e("div",At,[e("div",{class:"stat-number",ref_for:!0,ref:k=>n(k,h.key)},d(f[h.key])+d(h.suffix),513),e("div",Mt,d(h.label),1)])]))),128))])],512),e("div",{class:"section-footer",ref_key:"footerRef",ref:v},[e("button",{class:"btn btn--primary btn--lg",onClick:y[0]||(y[0]=(...h)=>I(T)&&I(T)(...h))},y[6]||(y[6]=[O(" 查看更多案例 ",-1),e("i",{class:"icon-arrow-right"},null,-1)]))],512)])],512))}}),It=D(Vt,[["__scopeId","data-v-c164c835"]]),Et={class:"container"},Bt={class:"contact-layout"},Pt={class:"contact-methods"},Ot={class:"method-icon"},Nt={class:"method-content"},Ut={class:"method-title"},Lt={class:"method-description"},Dt=["href","target"],Ft={class:"form-row"},Xt={class:"form-group"},Yt={key:0,class:"error-message"},jt={class:"form-group"},Ht={key:0,class:"error-message"},Kt={class:"form-row"},zt={class:"form-group"},Gt={key:0,class:"error-message"},Jt={class:"form-group"},Wt={class:"form-group"},Qt=["value"],Zt={key:0,class:"error-message"},es={class:"form-group"},ts={key:0,class:"error-message"},ss={class:"form-group"},os={class:"radio-group"},as={class:"radio-option"},is={class:"radio-option"},rs=["disabled"],ns={key:0},ls={key:1},cs={key:2,class:"icon-send"},us={key:0,class:"success-message"},ds=L({__name:"ContactSection",setup(U){p.registerPlugin(X);const T=ve(),m=s(),C=s(),l=s(),S=s([]),r=G({name:"",email:"",phone:"",company:"",service_interest:"",message:"",preferred_contact:"email"}),v=s({}),w=s(!1),x=s(!1),V=s([{type:"phone",title:"电话咨询",description:"立即与我们的专家通话",icon:"icon-phone",link:"tel:+86-************",linkText:"+86 ************",external:!1},{type:"email",title:"邮件联系",description:"发送邮件获取详细信息",icon:"icon-email",link:"mailto:<EMAIL>",linkText:"<EMAIL>",external:!1},{type:"location",title:"公司地址",description:"欢迎到访我们的办公室",icon:"icon-location",link:"https://maps.google.com",linkText:"北京市朝阳区某某大厦1001室",external:!0}]),P=s(["包装设计","包装生产","包装咨询","包装测试","包装物流","环保包装","智能包装","定制包装"]),$=(n,t)=>{n&&(S.value[t]=n)},f=async()=>{v.value={},x.value=!1;const n=fe(r);if(!n.isValid){v.value=n.errors;return}w.value=!0;try{await T.submitContactForm(r),x.value=!0,Object.assign(r,{name:"",email:"",phone:"",company:"",service_interest:"",message:"",preferred_contact:"email"})}catch(t){console.error("Form submission error:",t)}finally{w.value=!1}},g=async()=>{await H(),p.from(C.value?.querySelector(".info-header")?.children||[],{y:50,opacity:0,duration:.8,stagger:.2,ease:"power2.out",scrollTrigger:{trigger:C.value,start:"top 80%",once:!0}}),p.from(S.value,{x:-50,opacity:0,duration:.8,stagger:.2,ease:"power2.out",scrollTrigger:{trigger:S.value[0],start:"top 80%",once:!0}}),p.from(l.value,{x:50,opacity:0,duration:1,ease:"power2.out",scrollTrigger:{trigger:l.value,start:"top 80%",once:!0}})};return Y(()=>{g()}),(n,t)=>(a(),i("section",{class:"contact-section",ref_key:"sectionRef",ref:m},[e("div",Et,[e("div",Bt,[e("div",{class:"contact-info",ref_key:"infoRef",ref:C},[t[9]||(t[9]=e("div",{class:"info-header"},[e("h2",{class:"section-title"},"联系我们"),e("p",{class:"section-subtitle"}," 准备开始您的包装项目？我们随时为您提供专业服务 ")],-1)),e("div",Pt,[(a(!0),i(E,null,B(V.value,(o,_)=>(a(),i("div",{key:o.type,class:"contact-method",ref_for:!0,ref:y=>$(y,_)},[e("div",Ot,[e("i",{class:R(o.icon)},null,2)]),e("div",Nt,[e("h3",Ut,d(o.title),1),e("p",Lt,d(o.description),1),e("a",{href:o.link,class:"method-link",target:o.external?"_blank":"_self"},[O(d(o.linkText)+" ",1),t[8]||(t[8]=e("i",{class:"icon-arrow-right"},null,-1))],8,Dt)])]))),128))]),t[10]||(t[10]=ne('<div class="business-hours" data-v-80e9f02b><h3 class="hours-title" data-v-80e9f02b>营业时间</h3><div class="hours-list" data-v-80e9f02b><div class="hours-item" data-v-80e9f02b><span class="hours-day" data-v-80e9f02b>周一至周五</span><span class="hours-time" data-v-80e9f02b>9:00 - 18:00</span></div><div class="hours-item" data-v-80e9f02b><span class="hours-day" data-v-80e9f02b>周六</span><span class="hours-time" data-v-80e9f02b>9:00 - 17:00</span></div><div class="hours-item" data-v-80e9f02b><span class="hours-day" data-v-80e9f02b>周日</span><span class="hours-time" data-v-80e9f02b>休息</span></div></div></div>',1))],512),e("div",{class:"contact-form-container",ref_key:"formRef",ref:l},[t[22]||(t[22]=e("div",{class:"form-header"},[e("h3",{class:"form-title"},"发送消息"),e("p",{class:"form-subtitle"},"填写表单，我们将在24小时内回复您")],-1)),e("form",{class:"contact-form",onSubmit:le(f,["prevent"])},[e("div",Ft,[e("div",Xt,[t[11]||(t[11]=e("label",{for:"name",class:"form-label"},"姓名 *",-1)),N(e("input",{id:"name","onUpdate:modelValue":t[0]||(t[0]=o=>r.name=o),type:"text",class:R(["form-input",{error:v.value.name}]),placeholder:"请输入您的姓名",required:""},null,2),[[j,r.name]]),v.value.name?(a(),i("span",Yt,d(v.value.name),1)):M("",!0)]),e("div",jt,[t[12]||(t[12]=e("label",{for:"email",class:"form-label"},"邮箱 *",-1)),N(e("input",{id:"email","onUpdate:modelValue":t[1]||(t[1]=o=>r.email=o),type:"email",class:R(["form-input",{error:v.value.email}]),placeholder:"请输入您的邮箱",required:""},null,2),[[j,r.email]]),v.value.email?(a(),i("span",Ht,d(v.value.email),1)):M("",!0)])]),e("div",Kt,[e("div",zt,[t[13]||(t[13]=e("label",{for:"phone",class:"form-label"},"电话",-1)),N(e("input",{id:"phone","onUpdate:modelValue":t[2]||(t[2]=o=>r.phone=o),type:"tel",class:R(["form-input",{error:v.value.phone}]),placeholder:"请输入您的电话号码"},null,2),[[j,r.phone]]),v.value.phone?(a(),i("span",Gt,d(v.value.phone),1)):M("",!0)]),e("div",Jt,[t[14]||(t[14]=e("label",{for:"company",class:"form-label"},"公司",-1)),N(e("input",{id:"company","onUpdate:modelValue":t[3]||(t[3]=o=>r.company=o),type:"text",class:"form-input",placeholder:"请输入您的公司名称"},null,512),[[j,r.company]])])]),e("div",Wt,[t[16]||(t[16]=e("label",{for:"service",class:"form-label"},"感兴趣的服务 *",-1)),N(e("select",{id:"service","onUpdate:modelValue":t[4]||(t[4]=o=>r.service_interest=o),class:R(["form-select",{error:v.value.service_interest}]),required:""},[t[15]||(t[15]=e("option",{value:""},"请选择服务类型",-1)),(a(!0),i(E,null,B(P.value,o=>(a(),i("option",{key:o,value:o},d(o),9,Qt))),128))],2),[[ce,r.service_interest]]),v.value.service_interest?(a(),i("span",Zt,d(v.value.service_interest),1)):M("",!0)]),e("div",es,[t[17]||(t[17]=e("label",{for:"message",class:"form-label"},"留言 *",-1)),N(e("textarea",{id:"message","onUpdate:modelValue":t[5]||(t[5]=o=>r.message=o),class:R(["form-textarea",{error:v.value.message}]),placeholder:"请详细描述您的需求...",rows:"5",required:""},null,2),[[j,r.message]]),v.value.message?(a(),i("span",ts,d(v.value.message),1)):M("",!0)]),e("div",ss,[t[20]||(t[20]=e("label",{class:"form-label"},"联系方式偏好",-1)),e("div",os,[e("label",as,[N(e("input",{"onUpdate:modelValue":t[6]||(t[6]=o=>r.preferred_contact=o),type:"radio",value:"email"},null,512),[[Q,r.preferred_contact]]),t[18]||(t[18]=e("span",{class:"radio-label"},"邮箱",-1))]),e("label",is,[N(e("input",{"onUpdate:modelValue":t[7]||(t[7]=o=>r.preferred_contact=o),type:"radio",value:"phone"},null,512),[[Q,r.preferred_contact]]),t[19]||(t[19]=e("span",{class:"radio-label"},"电话",-1))])])]),e("button",{type:"submit",class:R(["submit-btn",{loading:w.value}]),disabled:w.value},[w.value?(a(),i("span",ls,"发送中...")):(a(),i("span",ns,"发送消息")),w.value?M("",!0):(a(),i("i",cs))],10,rs)],32),x.value?(a(),i("div",us,t[21]||(t[21]=[e("i",{class:"icon-check"},null,-1),e("span",null,"消息发送成功！我们将尽快回复您。",-1)]))):M("",!0)],512)])])],512))}}),vs=D(ds,[["__scopeId","data-v-80e9f02b"]]),fs=L({__name:"HomePage",setup(U){return(T,m)=>(a(),ue(se,null,{default:ee(()=>[F(Ee),F(je),F(ut),F(It),F(vs)]),_:1}))}}),hs=D(fs,[["__scopeId","data-v-676cd7ec"]]);export{hs as default};
