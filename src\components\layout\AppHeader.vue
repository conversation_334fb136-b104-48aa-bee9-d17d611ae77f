<template>
  <header class="app-header" :class="{ 'scrolled': isScrolled }">
    <div class="container">
      <div class="header-content">
        <!-- Logo -->
        <router-link to="/" class="logo">
          <img src="/logo.svg" alt="包装解决方案" class="logo-image" />
          <span class="logo-text">包装解决方案</span>
        </router-link>

        <!-- 桌面端导航菜单 -->
        <nav class="desktop-nav" aria-label="主导航">
          <ul class="nav-list">
            <li v-for="item in mainMenuItems" :key="item.id" class="nav-item">
              <router-link
                :to="item.path"
                class="nav-link"
                :class="{ 'active': isCurrentRoute(item.path) }"
                @click="closeMobileMenu"
              >
                {{ item.label }}
              </router-link>
            </li>
          </ul>
        </nav>

        <!-- 右侧操作区 -->
        <div class="header-actions">
          <!-- 主题切换 -->
          <ThemeToggle variant="simple" size="sm" />
        </div>

        <!-- 移动端菜单按钮 -->
        <button
          class="mobile-menu-button"
          :class="{ 'active': isMobileMenuOpen }"
          @click="toggleMobileMenu"
          aria-label="切换菜单"
        >
          <span class="hamburger-line"></span>
          <span class="hamburger-line"></span>
          <span class="hamburger-line"></span>
        </button>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <Transition name="mobile-menu">
      <div v-if="isMobileMenuOpen" class="mobile-menu">
        <nav class="mobile-nav" aria-label="移动端导航">
          <ul class="mobile-nav-list">
            <li v-for="item in mainMenuItems" :key="item.id" class="mobile-nav-item">
              <router-link
                :to="item.path"
                class="mobile-nav-link"
                :class="{ 'active': isCurrentRoute(item.path) }"
                @click="closeMobileMenu"
              >
                {{ item.label }}
              </router-link>
            </li>
          </ul>
        </nav>
      </div>
    </Transition>

    <!-- 移动端菜单遮罩 -->
    <Transition name="overlay">
      <div
        v-if="isMobileMenuOpen"
        class="mobile-menu-overlay"
        @click="closeMobileMenu"
      ></div>
    </Transition>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores'
import { useNavigation } from '@/composables'
import { ThemeToggle } from '@/components/common'

const appStore = useAppStore()
const { mainMenuItems, isCurrentRoute } = useNavigation()

// 滚动状态
const isScrolled = ref(false)

// 移动端菜单状态
const isMobileMenuOpen = computed(() => appStore.isMobileMenuOpen)

// 方法
const toggleMobileMenu = () => {
  appStore.toggleMobileMenu()
}

const closeMobileMenu = () => {
  appStore.closeMobileMenu()
}

// 滚动监听
const handleScroll = () => {
  isScrolled.value = window.scrollY > 50
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease-in-out;

  &.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
}

.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
  max-width: 1280px;

  @media (min-width: 768px) {
    padding: 0 1.5rem;
  }

  @media (min-width: 1024px) {
    padding: 0 2rem;
  }
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;

  @media (min-width: 1024px) {
    height: 5rem;
  }
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: #1a365d;
  font-weight: 700;
  font-size: 1.25rem;
  transition: color 0.3s ease-in-out;

  &:hover {
    color: #ff6b35;
  }
}

.logo-image {
  width: 2rem;
  height: 2rem;

  @media (min-width: 1024px) {
    width: 2.5rem;
    height: 2.5rem;
  }
}

.logo-text {
  @media (max-width: 640px) {
    display: none;
  }
}

.desktop-nav {
  display: none;

  @media (min-width: 1024px) {
    display: block;
  }
}

.nav-list {
  display: flex;
  align-items: center;
  gap: 2rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-link {
  position: relative;
  color: #4a5568;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0;
  transition: color 0.3s ease-in-out;

  &:hover {
    color: #1a365d;
  }

  &.active {
    color: #1a365d;

    &::after {
      content: '';
      position: absolute;
      bottom: -0.5rem;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(135deg, #1a365d 0%, #ff6b35 100%);
      border-radius: 1px;
    }
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: $spacing-4;

  @media (max-width: 768px) {
    display: none;
  }

  .btn {
    white-space: nowrap;
  }
}

.mobile-menu-button {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 2.5rem;
  height: 2.5rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  gap: 0.25rem;

  @media (min-width: 1024px) {
    display: none;
  }

  &:focus {
    outline: 2px solid #4299e1;
    outline-offset: 2px;
  }
}

.hamburger-line {
  width: 1.5rem;
  height: 2px;
  background: #4a5568;
  border-radius: 1px;
  transition: all 0.3s ease-in-out;

  .mobile-menu-button.active & {
    &:nth-child(1) {
      transform: rotate(45deg) translate(0.375rem, 0.375rem);
    }

    &:nth-child(2) {
      opacity: 0;
    }

    &:nth-child(3) {
      transform: rotate(-45deg) translate(0.375rem, -0.375rem);
    }
  }
}

.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.mobile-nav-list {
  list-style: none;
  margin: 0;
  padding: 1rem 0;
}

.mobile-nav-item {
  border-bottom: 1px solid #f7fafc;

  &:last-child {
    border-bottom: none;
  }
}

.mobile-nav-link {
  display: block;
  padding: 1rem 1.5rem;
  color: #4a5568;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease-in-out;

  &:hover {
    background: #f7fafc;
    color: #1a365d;
  }

  &.active {
    background: linear-gradient(135deg, #1a365d 0%, #ff6b35 100%);
    color: white;
  }
}

.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: -1;
}

// 动画
.mobile-menu-enter-active,
.mobile-menu-leave-active {
  transition: all 0.3s ease-in-out;
}

.mobile-menu-enter-from,
.mobile-menu-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.overlay-enter-active,
.overlay-leave-active {
  transition: opacity 0.3s ease-in-out;
}

.overlay-enter-from,
.overlay-leave-to {
  opacity: 0;
}
</style>
