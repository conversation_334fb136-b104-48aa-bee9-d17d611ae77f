// 导入变量和混合器
@use 'variables' as *;
@use 'mixins' as *;

// 导入动画和组件样式
@use 'animations' as *;
@use 'themes' as *;
@use 'components/buttons' as *;
@use 'components/cards' as *;
@use 'components/forms' as *;

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

// 基础样式
html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: $font-family-primary;
  font-size: $font-size-base;
  line-height: $line-height-normal;
  color: $gray-800;
  background-color: $gray-50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 链接样式
a {
  color: $primary-color;
  text-decoration: none;
  transition: color $transition-base;
  
  &:hover {
    color: $secondary-color;
  }
}

// 标题样式
h1, h2, h3, h4, h5, h6 {
  font-weight: $font-weight-bold;
  line-height: $line-height-tight;
  margin-bottom: $spacing-4;
  color: $gray-900;
}

h1 {
  font-size: $font-size-5xl;
  
  @include mobile-first($breakpoint-md) {
    font-size: $font-size-4xl;
  }
}

h2 {
  font-size: $font-size-4xl;
  
  @include mobile-first($breakpoint-md) {
    font-size: $font-size-3xl;
  }
}

h3 {
  font-size: $font-size-3xl;
  
  @include mobile-first($breakpoint-md) {
    font-size: $font-size-2xl;
  }
}

h4 {
  font-size: $font-size-2xl;
  
  @include mobile-first($breakpoint-md) {
    font-size: $font-size-xl;
  }
}

h5 {
  font-size: $font-size-xl;
  
  @include mobile-first($breakpoint-md) {
    font-size: $font-size-lg;
  }
}

h6 {
  font-size: $font-size-lg;
  
  @include mobile-first($breakpoint-md) {
    font-size: $font-size-base;
  }
}

// 段落样式
p {
  margin-bottom: $spacing-4;
  line-height: $line-height-relaxed;
}

// 列表样式
ul, ol {
  margin-bottom: $spacing-4;
  padding-left: $spacing-6;
}

li {
  margin-bottom: $spacing-2;
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
  display: block;
}

// 按钮基础样式
.btn {
  @include button-base;
  padding: $spacing-3 $spacing-6;
  font-size: $font-size-base;
  
  &--primary {
    @include button-primary;
  }
  
  &--secondary {
    @include button-secondary;
  }
  
  &--sm {
    padding: $spacing-2 $spacing-4;
    font-size: $font-size-sm;
  }
  
  &--lg {
    padding: $spacing-4 $spacing-8;
    font-size: $font-size-lg;
  }
}

// 卡片样式
.card {
  @include card;
  padding: $spacing-6;
  
  &--hover {
    cursor: pointer;
  }
}

// 容器样式
.container {
  @include container;
}

// 网格系统
.grid {
  @include grid;
  
  &--2 {
    @include grid(2);
    
    @include mobile-first($breakpoint-md) {
      @include grid(1);
    }
  }
  
  &--3 {
    @include grid(3);
    
    @include mobile-first($breakpoint-lg) {
      @include grid(2);
    }
    
    @include mobile-first($breakpoint-md) {
      @include grid(1);
    }
  }
  
  &--4 {
    @include grid(4);
    
    @include mobile-first($breakpoint-xl) {
      @include grid(3);
    }
    
    @include mobile-first($breakpoint-lg) {
      @include grid(2);
    }
    
    @include mobile-first($breakpoint-md) {
      @include grid(1);
    }
  }
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: $primary-color;
}

.text-secondary {
  color: $secondary-color;
}

.text-gradient {
  @include gradient-text;
}

.bg-primary {
  background-color: $primary-color;
}

.bg-secondary {
  background-color: $secondary-color;
}

.bg-gradient {
  background: $primary-gradient;
}

// 间距工具类
@each $size in (0, 1, 2, 3, 4, 5, 6, 8, 10, 12, 16, 20, 24) {
  .m-#{$size} {
    margin: #{$size * 0.25}rem;
  }
  
  .mt-#{$size} {
    margin-top: #{$size * 0.25}rem;
  }
  
  .mb-#{$size} {
    margin-bottom: #{$size * 0.25}rem;
  }
  
  .ml-#{$size} {
    margin-left: #{$size * 0.25}rem;
  }
  
  .mr-#{$size} {
    margin-right: #{$size * 0.25}rem;
  }
  
  .mx-#{$size} {
    margin-left: #{$size * 0.25}rem;
    margin-right: #{$size * 0.25}rem;
  }
  
  .my-#{$size} {
    margin-top: #{$size * 0.25}rem;
    margin-bottom: #{$size * 0.25}rem;
  }
  
  .p-#{$size} {
    padding: #{$size * 0.25}rem;
  }
  
  .pt-#{$size} {
    padding-top: #{$size * 0.25}rem;
  }
  
  .pb-#{$size} {
    padding-bottom: #{$size * 0.25}rem;
  }
  
  .pl-#{$size} {
    padding-left: #{$size * 0.25}rem;
  }
  
  .pr-#{$size} {
    padding-right: #{$size * 0.25}rem;
  }
  
  .px-#{$size} {
    padding-left: #{$size * 0.25}rem;
    padding-right: #{$size * 0.25}rem;
  }
  
  .py-#{$size} {
    padding-top: #{$size * 0.25}rem;
    padding-bottom: #{$size * 0.25}rem;
  }
}

// 动画类
.fade-in {
  @include fade-in;
}

.slide-in-up {
  @include slide-in-up;
}

.pulse {
  @include pulse-animation;
}

// 隐藏/显示工具类
.hidden {
  display: none;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
