import{a as W,r as c,o as H,g as p,i as n,m as o,h as e,j as k,p as w,t as d,F as R,k as M,B as D,l as I,_ as J,S as le,y as ce,c as P,q as ue,e as j,w as de,f as F,s as E,v as ve,x as ge,L as pe,n as G}from"./index-Dz-_Smvi.js";import{u as K,f as fe,A as ye}from"./AppLayout-DAG3tKFc.js";import{B as me}from"./Breadcrumb-DX9d_gU2.js";/* empty css                                                                            */import{S as ke,F as he}from"./FilterPanel-C3hsGuJ4.js";import{u as be}from"./contact-sLg3zrn9.js";const _e={class:"service-content"},we={class:"service-header"},Se={class:"service-title"},Ce={key:0,class:"service-category"},$e={class:"service-description"},Pe={key:0,class:"service-features"},Re={class:"features-list"},Me={key:1,class:"service-pricing"},Le={class:"price-value"},Ve={class:"amount"},Be={class:"unit"},qe={key:2,class:"service-tags"},Te={class:"service-footer"},Fe={class:"service-meta"},Ne={key:0,class:"meta-item"},xe={key:1,class:"meta-item"},Qe={class:"rating"},Ae={class:"rating-value"},Ee={class:"service-actions"},Ie={key:0,class:"featured-badge"},Ue=W({__name:"ServiceCard",props:{service:{},variant:{default:"default"},featured:{type:Boolean,default:!1},maxFeatures:{default:4}},emits:["click","quote","learn-more"],setup(U,{emit:N}){const f=U,S=N,{navigateToServices:C}=K(),L=c(),g=c(),_=c();let r;const $=a=>fe(a),h=()=>{S("click",f.service),C(f.service.category)},u=()=>{S("quote",f.service)},V=()=>{S("learn-more",f.service),C(f.service.category)},v=()=>{r&&r.kill(),r=p.timeline(),r.to(L.value,{y:-8,scale:1.02,duration:.3,ease:"power2.out"}),r.to(g.value,{scale:1.1,rotation:5,duration:.3,ease:"power2.out"},0),r.to(_.value,{opacity:1,duration:.3,ease:"power2.out"},0),r.to(g.value?.querySelector(".icon-glow"),{scale:1.5,opacity:.6,duration:.3,ease:"power2.out"},0)},B=()=>{r&&r.kill(),r=p.timeline(),r.to(L.value,{y:0,scale:1,duration:.3,ease:"power2.out"}),r.to(g.value,{scale:1,rotation:0,duration:.3,ease:"power2.out"},0),r.to(_.value,{opacity:0,duration:.3,ease:"power2.out"},0),r.to(g.value?.querySelector(".icon-glow"),{scale:1,opacity:0,duration:.3,ease:"power2.out"},0)};return H(()=>{p.set(_.value,{opacity:0}),p.set(g.value?.querySelector(".icon-glow"),{opacity:0})}),(a,l)=>(o(),n("div",{class:w(["service-card",[`service-card--${a.variant}`,{"service-card--featured":a.featured}]]),ref_key:"cardRef",ref:L,onMouseenter:v,onMouseleave:B,onClick:h},[l[8]||(l[8]=e("div",{class:"card-background"},[e("div",{class:"background-pattern"}),e("div",{class:"background-gradient"})],-1)),e("div",{class:"service-icon",ref_key:"iconRef",ref:g},[e("i",{class:w(a.service.icon)},null,2),l[0]||(l[0]=e("div",{class:"icon-glow"},null,-1))],512),e("div",_e,[e("div",we,[e("h3",Se,d(a.service.title),1),a.service.category?(o(),n("span",Ce,d(a.service.category),1)):k("",!0)]),e("p",$e,d(a.service.description),1),a.service.features&&a.service.features.length>0?(o(),n("div",Pe,[l[2]||(l[2]=e("h4",{class:"features-title"},"核心特点",-1)),e("ul",Re,[(o(!0),n(R,null,M(a.service.features.slice(0,a.maxFeatures),(y,x)=>(o(),n("li",{key:x,class:"feature-item"},[l[1]||(l[1]=e("i",{class:"icon-check"},null,-1)),e("span",null,d(y),1)]))),128))])])):k("",!0),a.service.pricing?(o(),n("div",Me,[l[4]||(l[4]=e("div",{class:"price-label"},"起始价格",-1)),e("div",Le,[l[3]||(l[3]=e("span",{class:"currency"},"¥",-1)),e("span",Ve,d($(a.service.pricing.startingPrice)),1),e("span",Be,d(a.service.pricing.unit),1)])])):k("",!0),a.service.tags&&a.service.tags.length>0?(o(),n("div",qe,[(o(!0),n(R,null,M(a.service.tags.slice(0,3),y=>(o(),n("span",{key:y,class:"service-tag"},d(y),1))),128))])):k("",!0)]),e("div",Te,[e("div",Fe,[a.service.duration?(o(),n("div",Ne,[l[5]||(l[5]=e("i",{class:"icon-clock"},null,-1)),e("span",null,d(a.service.duration),1)])):k("",!0),a.service.rating?(o(),n("div",xe,[e("div",Qe,[(o(),n(R,null,M(5,y=>e("i",{key:y,class:w(["star",{filled:y<=Math.floor(a.service.rating)}])},null,2)),64)),e("span",Ae,"("+d(a.service.rating)+")",1)])])):k("",!0)]),e("div",Ee,[e("button",{class:"btn btn--secondary btn--sm",onClick:D(u,["stop"])}," 获取报价 "),e("button",{class:"btn btn--primary btn--sm",onClick:D(V,["stop"])},l[6]||(l[6]=[I(" 了解详情 ",-1),e("i",{class:"icon-arrow-right"},null,-1)]))])]),e("div",{class:"hover-overlay",ref_key:"overlayRef",ref:_},null,512),a.featured?(o(),n("div",Ie,l[7]||(l[7]=[e("i",{class:"icon-star"},null,-1),e("span",null,"推荐",-1)]))):k("",!0)],34))}}),ze=J(Ue,[["__scopeId","data-v-c332fb8e"]]),De={class:"container"},je={class:"hero-content"},Ge={class:"services-stats"},We={class:"stat-number"},He={class:"stat-label"},Je={class:"container"},Ke={class:"filters-layout"},Oe={class:"search-section"},Xe={class:"filter-section"},Ye={class:"container"},Ze={class:"content-header"},et={class:"results-info"},tt={class:"results-count"},st={class:"content-controls"},it={class:"sort-controls"},at={class:"view-controls"},ot={key:0,class:"loading-container"},rt={key:1,class:"empty-state"},nt={key:2,class:"pagination-container"},lt={class:"pagination"},ct=["disabled"],ut={class:"pagination-numbers"},dt=["onClick"],vt=["disabled"],gt=W({__name:"ServicesPage",setup(U){p.registerPlugin(le),be();const{navigateToContact:N}=K(),f=c(),S=c(),C=c(),L=c([]),g=c([]),_=c(!1),r=c(""),$=c("default"),h=c("grid"),u=c(1),V=c(12),v=c({}),B=c([{key:"totalServices",label:"服务项目",value:50,suffix:"+"},{key:"industries",label:"服务行业",value:20,suffix:"+"},{key:"experience",label:"行业经验",value:10,suffix:"年"},{key:"satisfaction",label:"客户满意度",value:98,suffix:"%"}]),a=ce({totalServices:0,industries:0,experience:0,satisfaction:0}),l=c([{id:"1",title:"包装设计服务",description:"专业的包装设计团队，为您的产品打造独特的视觉形象，提升品牌价值和市场竞争力。",category:"设计服务",icon:"icon-design",features:["创意设计","品牌一致性","市场导向","用户体验"],pricing:{startingPrice:5e3,unit:"起"},tags:["创意","品牌","视觉"],duration:"7-14天",rating:4.8,featured:!0},{id:"2",title:"包装生产制造",description:"先进的生产设备和严格的质量控制，确保每一个包装产品的品质和交付时间。",category:"生产服务",icon:"icon-production",features:["质量保证","快速交付","成本优化","环保材料"],pricing:{startingPrice:1e4,unit:"起"},tags:["生产","质量","环保"],duration:"10-20天",rating:4.9},{id:"3",title:"包装咨询顾问",description:"资深行业专家为您提供专业的包装解决方案和技术支持，助力业务发展。",category:"咨询服务",icon:"icon-consulting",features:["专业建议","技术支持","成本分析","市场调研"],pricing:{startingPrice:3e3,unit:"起"},tags:["咨询","专业","支持"],duration:"3-7天",rating:4.7},{id:"4",title:"包装测试认证",description:"全面的包装测试服务，确保产品在运输和储存过程中的安全性和可靠性。",category:"测试服务",icon:"icon-testing",features:["安全测试","耐久性测试","环境测试","质量认证"],pricing:{startingPrice:2e3,unit:"起"},tags:["测试","认证","安全"],duration:"5-10天",rating:4.6},{id:"5",title:"包装物流配送",description:"完善的物流配送体系，确保包装产品安全快速地到达目的地。",category:"物流服务",icon:"icon-logistics",features:["快速配送","安全包装","跟踪服务","全国覆盖"],pricing:{startingPrice:500,unit:"起"},tags:["物流","配送","跟踪"],duration:"1-3天",rating:4.5},{id:"6",title:"环保包装解决方案",description:"绿色环保的包装解决方案，为可持续发展贡献力量，符合环保要求。",category:"环保服务",icon:"icon-eco",features:["可回收材料","生物降解","节能减排","绿色认证"],pricing:{startingPrice:8e3,unit:"起"},tags:["环保","可持续","绿色"],duration:"10-15天",rating:4.8,featured:!0}]),y=P(()=>[{text:"包装设计",category:"设计"},{text:"包装生产",category:"生产"},{text:"包装咨询",category:"咨询"},{text:"包装测试",category:"测试"},{text:"环保包装",category:"环保"}]),x=P(()=>[{key:"category",title:"服务类别",type:"checkbox",options:[{value:"设计服务",label:"设计服务",count:1},{value:"生产服务",label:"生产服务",count:1},{value:"咨询服务",label:"咨询服务",count:1},{value:"测试服务",label:"测试服务",count:1},{value:"物流服务",label:"物流服务",count:1},{value:"环保服务",label:"环保服务",count:1}]},{key:"priceRange",title:"价格范围",type:"range",minPlaceholder:"最低价格",maxPlaceholder:"最高价格"},{key:"duration",title:"服务周期",type:"radio",options:[{value:"short",label:"1-7天",count:2},{value:"medium",label:"7-15天",count:3},{value:"long",label:"15天以上",count:1}]}]),q=P(()=>{let i=[...l.value];if(r.value){const t=r.value.toLowerCase();i=i.filter(s=>s.title.toLowerCase().includes(t)||s.description.toLowerCase().includes(t)||s.category.toLowerCase().includes(t))}if(v.value.category&&v.value.category.length>0&&(i=i.filter(t=>v.value.category.includes(t.category))),v.value.priceRange){const{min:t,max:s}=v.value.priceRange;i=i.filter(m=>{if(!m.pricing)return!0;const b=m.pricing.startingPrice;return!(t!==void 0&&b<t||s!==void 0&&b>s)})}return $.value!=="default"&&i.sort((t,s)=>{switch($.value){case"name-asc":return t.title.localeCompare(s.title);case"name-desc":return s.title.localeCompare(t.title);case"price-asc":return(t.pricing?.startingPrice||0)-(s.pricing?.startingPrice||0);case"price-desc":return(s.pricing?.startingPrice||0)-(t.pricing?.startingPrice||0);default:return 0}}),i}),T=P(()=>Math.ceil(q.value.length/V.value)),z=P(()=>{const i=(u.value-1)*V.value,t=i+V.value;return q.value.slice(i,t)}),O=P(()=>{const i=[],t=T.value,s=u.value,m=Math.max(1,s-2),b=Math.min(t,s+2);for(let A=m;A<=b;A++)i.push(A);return i}),X=(i,t)=>{i&&(L.value[t]=i)},Y=(i,t)=>{i&&(g.value[t]=i)},Z=i=>{r.value=i,u.value=1},ee=i=>{v.value=i,u.value=1},te=()=>{u.value=1},se=i=>{console.log("Service clicked:",i)},ie=i=>{N()},ae=i=>{console.log("Learn more:",i)},oe=()=>{r.value="",v.value={},u.value=1},Q=i=>{i>=1&&i<=T.value&&(u.value=i,C.value?.scrollIntoView({behavior:"smooth"}))},re=()=>{B.value.forEach(i=>{p.to(a,{[i.key]:i.value,duration:2,ease:"power2.out",scrollTrigger:{trigger:f.value,start:"top 80%",once:!0}})})},ne=async()=>{await G(),p.from(f.value?.querySelector(".hero-content")?.children||[],{y:50,opacity:0,duration:.8,stagger:.2,ease:"power2.out"}),p.from(S.value,{y:30,opacity:0,duration:.6,ease:"power2.out",delay:.3}),p.from(g.value,{y:60,opacity:0,duration:.8,stagger:.1,ease:"power2.out",scrollTrigger:{trigger:C.value,start:"top 80%",once:!0}}),re()};return ue(()=>z.value,()=>{G(()=>{p.from(g.value,{y:30,opacity:0,duration:.5,stagger:.05,ease:"power2.out"})})}),H(()=>{ne()}),(i,t)=>(o(),j(ye,null,{default:de(()=>[F(me),e("section",{class:"services-hero",ref_key:"heroRef",ref:f},[e("div",De,[e("div",je,[t[7]||(t[7]=e("h1",{class:"page-title"},"我们的服务",-1)),t[8]||(t[8]=e("p",{class:"page-subtitle"}," 专业的包装解决方案，满足您的各种需求 ",-1)),e("div",Ge,[(o(!0),n(R,null,M(B.value,(s,m)=>(o(),n("div",{key:s.label,class:"stat-item",ref_for:!0,ref:b=>X(b,m)},[e("div",We,d(a[s.key])+d(s.suffix),1),e("div",He,d(s.label),1)]))),128))])])])],512),e("section",{class:"services-filters",ref_key:"filtersRef",ref:S},[e("div",Je,[e("div",Ke,[e("div",Oe,[F(E(ke),{modelValue:r.value,"onUpdate:modelValue":t[0]||(t[0]=s=>r.value=s),placeholder:"搜索服务...",suggestions:y.value,onSearch:Z},null,8,["modelValue","suggestions"])]),e("div",Xe,[F(E(he),{modelValue:v.value,"onUpdate:modelValue":t[1]||(t[1]=s=>v.value=s),"filter-groups":x.value,onChange:ee},null,8,["modelValue","filter-groups"])])])])],512),e("section",{class:"services-content",ref_key:"contentRef",ref:C},[e("div",Ye,[e("div",Ze,[e("div",et,[e("span",tt,"找到 "+d(q.value.length)+" 个服务",1)]),e("div",st,[e("div",it,[t[10]||(t[10]=e("label",{for:"sort-select",class:"sort-label"},"排序：",-1)),ve(e("select",{id:"sort-select","onUpdate:modelValue":t[2]||(t[2]=s=>$.value=s),class:"sort-select",onChange:te},t[9]||(t[9]=[e("option",{value:"default"},"默认排序",-1),e("option",{value:"name-asc"},"名称升序",-1),e("option",{value:"name-desc"},"名称降序",-1),e("option",{value:"price-asc"},"价格升序",-1),e("option",{value:"price-desc"},"价格降序",-1)]),544),[[ge,$.value]])]),e("div",at,[e("button",{class:w(["view-btn",{active:h.value==="grid"}]),onClick:t[3]||(t[3]=s=>h.value="grid"),"aria-label":"网格视图"},t[11]||(t[11]=[e("i",{class:"icon-grid"},null,-1)]),2),e("button",{class:w(["view-btn",{active:h.value==="list"}]),onClick:t[4]||(t[4]=s=>h.value="list"),"aria-label":"列表视图"},t[12]||(t[12]=[e("i",{class:"icon-list"},null,-1)]),2)])])]),e("div",{class:w(["services-grid",[`services-grid--${h.value}`,{loading:_.value}]])},[(o(!0),n(R,null,M(z.value,(s,m)=>(o(),j(ze,{key:s.id,service:s,variant:h.value==="list"?"compact":"default",featured:s.featured,ref_for:!0,ref:b=>Y(b,m),onClick:se,onQuote:ie,onLearnMore:ae},null,8,["service","variant","featured"]))),128))],2),_.value?(o(),n("div",ot,[F(E(pe))])):q.value.length===0?(o(),n("div",rt,[t[13]||(t[13]=e("div",{class:"empty-icon"},[e("i",{class:"icon-search"})],-1)),t[14]||(t[14]=e("h3",{class:"empty-title"},"未找到相关服务",-1)),t[15]||(t[15]=e("p",{class:"empty-description"}," 请尝试调整搜索条件或筛选器 ",-1)),e("button",{class:"btn btn--primary",onClick:oe}," 清除筛选条件 ")])):k("",!0),T.value>1?(o(),n("div",nt,[e("div",lt,[e("button",{class:"pagination-btn",disabled:u.value===1,onClick:t[5]||(t[5]=s=>Q(u.value-1))},t[16]||(t[16]=[e("i",{class:"icon-arrow-left"},null,-1),I(" 上一页 ",-1)]),8,ct),e("div",ut,[(o(!0),n(R,null,M(O.value,s=>(o(),n("button",{key:s,class:w(["pagination-number",{active:s===u.value}]),onClick:m=>Q(s)},d(s),11,dt))),128))]),e("button",{class:"pagination-btn",disabled:u.value===T.value,onClick:t[6]||(t[6]=s=>Q(u.value+1))},t[17]||(t[17]=[I(" 下一页 ",-1),e("i",{class:"icon-arrow-right"},null,-1)]),8,vt)])])):k("",!0)])],512)]),_:1}))}}),bt=J(gt,[["__scopeId","data-v-3e4d5915"]]);export{bt as default};
