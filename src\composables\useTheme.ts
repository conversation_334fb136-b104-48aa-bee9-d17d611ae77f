// 主题管理 Composable

import { ref, computed, watch, onMounted, readonly } from 'vue'

export type Theme = 'light' | 'dark' | 'auto' | 'high-contrast'

// 全局主题状态
const currentTheme = ref<Theme>('auto')
const systemPrefersDark = ref(false)

// 检测系统主题偏好
const detectSystemTheme = () => {
  if (typeof window !== 'undefined') {
    systemPrefersDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
  }
}

// 监听系统主题变化
const watchSystemTheme = () => {
  if (typeof window !== 'undefined') {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

    const handleChange = (e: MediaQueryListEvent) => {
      systemPrefersDark.value = e.matches
    }

    mediaQuery.addEventListener('change', handleChange)

    return () => {
      mediaQuery.removeEventListener('change', handleChange)
    }
  }
}

// 应用主题到 DOM
const applyTheme = (theme: Theme) => {
  if (typeof document === 'undefined') return

  const root = document.documentElement

  // 移除所有主题类
  root.removeAttribute('data-theme')
  root.classList.remove('theme-light', 'theme-dark', 'theme-auto')

  switch (theme) {
    case 'light':
      root.setAttribute('data-theme', 'light')
      root.classList.add('theme-light')
      break
    case 'dark':
      root.setAttribute('data-theme', 'dark')
      root.classList.add('theme-dark')
      break
    case 'high-contrast':
      root.setAttribute('data-theme', 'high-contrast')
      break
    case 'auto':
    default:
      root.classList.add('theme-auto')
      // auto 模式下根据系统偏好设置
      if (systemPrefersDark.value) {
        root.setAttribute('data-theme', 'dark')
      } else {
        root.setAttribute('data-theme', 'light')
      }
      break
  }
}

// 保存主题到本地存储
const saveTheme = (theme: Theme) => {
  if (typeof localStorage !== 'undefined') {
    localStorage.setItem('preferred-theme', theme)
  }
}

// 从本地存储加载主题
const loadTheme = (): Theme => {
  if (typeof localStorage !== 'undefined') {
    const saved = localStorage.getItem('preferred-theme') as Theme
    if (saved && ['light', 'dark', 'auto', 'high-contrast'].includes(saved)) {
      return saved
    }
  }
  return 'auto'
}

export const useTheme = () => {
  // 计算当前实际应用的主题
  const resolvedTheme = computed(() => {
    if (currentTheme.value === 'auto') {
      return systemPrefersDark.value ? 'dark' : 'light'
    }
    return currentTheme.value
  })

  // 主题显示名称
  const themeDisplayName = computed(() => {
    const names: Record<Theme, string> = {
      light: '浅色模式',
      dark: '深色模式',
      auto: '跟随系统',
      'high-contrast': '高对比度'
    }
    return names[currentTheme.value]
  })

  // 主题图标
  const themeIcon = computed(() => {
    const icons: Record<Theme, string> = {
      light: '☀️',
      dark: '🌙',
      auto: '🔄',
      'high-contrast': '🔍'
    }
    return icons[currentTheme.value]
  })

  // 是否为深色主题
  const isDark = computed(() => {
    return resolvedTheme.value === 'dark'
  })

  // 是否为浅色主题
  const isLight = computed(() => {
    return resolvedTheme.value === 'light'
  })

  // 是否为高对比度主题
  const isHighContrast = computed(() => {
    return currentTheme.value === 'high-contrast'
  })

  // 设置主题
  const setTheme = (theme: Theme) => {
    currentTheme.value = theme
    applyTheme(theme)
    saveTheme(theme)
  }

  // 切换主题（在 light 和 dark 之间）
  const toggleTheme = () => {
    if (currentTheme.value === 'light') {
      setTheme('dark')
    } else if (currentTheme.value === 'dark') {
      setTheme('light')
    } else {
      // 如果是 auto 或其他，切换到相反的主题
      setTheme(systemPrefersDark.value ? 'light' : 'dark')
    }
  }

  // 循环切换主题
  const cycleTheme = () => {
    const themes: Theme[] = ['light', 'dark', 'auto']
    const currentIndex = themes.indexOf(currentTheme.value)
    const nextIndex = (currentIndex + 1) % themes.length
    setTheme(themes[nextIndex])
  }

  // 重置为系统主题
  const resetToSystem = () => {
    setTheme('auto')
  }

  // 获取所有可用主题
  const availableThemes = computed(() => [
    { value: 'light', label: '浅色模式', icon: '☀️' },
    { value: 'dark', label: '深色模式', icon: '🌙' },
    { value: 'auto', label: '跟随系统', icon: '🔄' },
    { value: 'high-contrast', label: '高对比度', icon: '🔍' }
  ])

  // 初始化主题
  const initTheme = () => {
    detectSystemTheme()
    const savedTheme = loadTheme()
    currentTheme.value = savedTheme
    applyTheme(savedTheme)

    // 监听系统主题变化
    const cleanup = watchSystemTheme()

    return cleanup
  }

  // 监听主题变化
  watch(currentTheme, (newTheme) => {
    applyTheme(newTheme)
  })

  // 监听系统主题变化（仅在 auto 模式下）
  watch(systemPrefersDark, () => {
    if (currentTheme.value === 'auto') {
      applyTheme('auto')
    }
  })

  // 获取主题相关的 CSS 变量值
  const getThemeVariable = (variable: string): string => {
    if (typeof document === 'undefined') return ''
    return getComputedStyle(document.documentElement)
      .getPropertyValue(variable)
      .trim()
  }

  // 设置主题相关的 CSS 变量
  const setThemeVariable = (variable: string, value: string) => {
    if (typeof document === 'undefined') return
    document.documentElement.style.setProperty(variable, value)
  }

  // 主题过渡效果
  const enableThemeTransition = () => {
    if (typeof document === 'undefined') return

    document.documentElement.classList.remove('no-theme-transition')
  }

  const disableThemeTransition = () => {
    if (typeof document === 'undefined') return

    document.documentElement.classList.add('no-theme-transition')

    // 短暂禁用后重新启用
    setTimeout(() => {
      enableThemeTransition()
    }, 100)
  }

  // 平滑主题切换
  const smoothSetTheme = (theme: Theme) => {
    disableThemeTransition()
    setTheme(theme)
  }

  return {
    // 状态
    currentTheme: readonly(currentTheme),
    resolvedTheme,
    systemPrefersDark: readonly(systemPrefersDark),

    // 计算属性
    themeDisplayName,
    themeIcon,
    isDark,
    isLight,
    isHighContrast,
    availableThemes,

    // 方法
    setTheme,
    toggleTheme,
    cycleTheme,
    resetToSystem,
    smoothSetTheme,
    initTheme,

    // 工具方法
    getThemeVariable,
    setThemeVariable,
    enableThemeTransition,
    disableThemeTransition
  }
}

// 全局主题实例（单例模式）
let globalThemeInstance: ReturnType<typeof useTheme> | null = null

export const useGlobalTheme = () => {
  if (!globalThemeInstance) {
    globalThemeInstance = useTheme()
  }
  return globalThemeInstance
}

// 主题初始化函数（在应用启动时调用）
export const initializeTheme = () => {
  const theme = useGlobalTheme()

  // 在客户端初始化
  if (typeof window !== 'undefined') {
    onMounted(() => {
      theme.initTheme()
    })
  }

  return theme
}
