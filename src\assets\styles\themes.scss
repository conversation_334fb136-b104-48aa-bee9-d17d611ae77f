// 主题系统 - 支持亮色和深色模式

@use 'sass:color';
@use 'variables' as *;

// ===== CSS 自定义属性定义 =====
:root {
  // 主色调
  --color-primary: #{$primary-color};
  --color-secondary: #{$secondary-color};
  --color-accent: #{$accent-color};
  
  // 功能色彩
  --color-success: #{$success-color};
  --color-warning: #{$warning-color};
  --color-error: #{$error-color};
  --color-info: #{$info-color};
  
  // 中性色 - 亮色模式
  --color-white: #{$white};
  --color-black: #{$black};
  --color-gray-50: #{$gray-50};
  --color-gray-100: #{$gray-100};
  --color-gray-200: #{$gray-200};
  --color-gray-300: #{$gray-300};
  --color-gray-400: #{$gray-400};
  --color-gray-500: #{$gray-500};
  --color-gray-600: #{$gray-600};
  --color-gray-700: #{$gray-700};
  --color-gray-800: #{$gray-800};
  --color-gray-900: #{$gray-900};
  
  // 语义化颜色 - 亮色模式
  --color-background: #{$white};
  --color-surface: #{$white};
  --color-surface-variant: #{$gray-50};
  --color-text-primary: #{$gray-900};
  --color-text-secondary: #{$gray-600};
  --color-text-muted: #{$gray-500};
  --color-border: #{$gray-200};
  --color-border-light: #{$gray-100};
  --color-shadow: rgba(0, 0, 0, 0.1);
  
  // 导航和布局
  --color-nav-bg: rgba(255, 255, 255, 0.95);
  --color-nav-text: #{$gray-800};
  --color-nav-hover: #{$primary-color};
  --color-footer-bg: #{$gray-900};
  --color-footer-text: #{$gray-300};
  
  // 卡片和组件
  --color-card-bg: #{$white};
  --color-card-border: #{$gray-200};
  --color-card-shadow: #{$shadow-md};
  --color-input-bg: #{$white};
  --color-input-border: #{$gray-300};
  --color-input-focus: #{$accent-color};
  
  // 渐变
  --gradient-primary: linear-gradient(135deg, #{$primary-color} 0%, #{$secondary-color} 100%);
  --gradient-secondary: linear-gradient(135deg, #{$secondary-color} 0%, #{$warning-color} 100%);
  --gradient-accent: linear-gradient(135deg, #{$accent-color} 0%, #{$info-color} 100%);
}

// ===== 深色模式主题 =====
[data-theme="dark"] {
  // 中性色 - 深色模式
  --color-gray-50: #0f172a;
  --color-gray-100: #1e293b;
  --color-gray-200: #334155;
  --color-gray-300: #475569;
  --color-gray-400: #64748b;
  --color-gray-500: #94a3b8;
  --color-gray-600: #cbd5e1;
  --color-gray-700: #e2e8f0;
  --color-gray-800: #f1f5f9;
  --color-gray-900: #f8fafc;
  
  // 语义化颜色 - 深色模式
  --color-background: #0f172a;
  --color-surface: #1e293b;
  --color-surface-variant: #334155;
  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-muted: #94a3b8;
  --color-border: #334155;
  --color-border-light: #475569;
  --color-shadow: rgba(0, 0, 0, 0.3);
  
  // 导航和布局 - 深色模式
  --color-nav-bg: rgba(15, 23, 42, 0.95);
  --color-nav-text: #f8fafc;
  --color-nav-hover: #{$secondary-color};
  --color-footer-bg: #0f172a;
  --color-footer-text: #cbd5e1;
  
  // 卡片和组件 - 深色模式
  --color-card-bg: #1e293b;
  --color-card-border: #334155;
  --color-card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  --color-input-bg: #1e293b;
  --color-input-border: #475569;
  --color-input-focus: #{$secondary-color};
  
  // 调整主色调在深色模式下的表现
  --color-primary: #{color.adjust($primary-color, $lightness: 10%)};
  --color-secondary: #{$secondary-color};
  --color-accent: #{color.adjust($accent-color, $lightness: 15%)};
  
  // 深色模式渐变
  --gradient-primary: linear-gradient(135deg, #{$secondary-color} 0%, #{$primary-color} 100%);
  --gradient-secondary: linear-gradient(135deg, #{$warning-color} 0%, #{$secondary-color} 100%);
  --gradient-accent: linear-gradient(135deg, #{$info-color} 0%, #{$accent-color} 100%);
}

// ===== 高对比度模式 =====
[data-theme="high-contrast"] {
  --color-primary: #000000;
  --color-secondary: #ffffff;
  --color-accent: #0066cc;
  
  --color-background: #ffffff;
  --color-surface: #ffffff;
  --color-surface-variant: #f5f5f5;
  --color-text-primary: #000000;
  --color-text-secondary: #333333;
  --color-text-muted: #666666;
  --color-border: #000000;
  --color-border-light: #333333;
  --color-shadow: rgba(0, 0, 0, 0.5);
  
  --color-success: #008000;
  --color-warning: #ff8c00;
  --color-error: #dc143c;
  --color-info: #0066cc;
  
  --color-card-bg: #ffffff;
  --color-card-border: #000000;
  --color-input-bg: #ffffff;
  --color-input-border: #000000;
  --color-input-focus: #0066cc;
}

// ===== 主题工具类 =====
.theme {
  &-light {
    color-scheme: light;
  }
  
  &-dark {
    color-scheme: dark;
  }
  
  &-auto {
    color-scheme: light dark;
  }
}

// ===== 主题感知组件样式 =====

// 背景色工具类
.bg {
  &-background {
    background-color: var(--color-background);
  }
  
  &-surface {
    background-color: var(--color-surface);
  }
  
  &-surface-variant {
    background-color: var(--color-surface-variant);
  }
  
  &-primary {
    background-color: var(--color-primary);
  }
  
  &-secondary {
    background-color: var(--color-secondary);
  }
  
  &-gradient {
    &-primary {
      background: var(--gradient-primary);
    }
    
    &-secondary {
      background: var(--gradient-secondary);
    }
    
    &-accent {
      background: var(--gradient-accent);
    }
  }
}

// 文字颜色工具类
.text {
  &-primary {
    color: var(--color-text-primary);
  }
  
  &-secondary {
    color: var(--color-text-secondary);
  }
  
  &-muted {
    color: var(--color-text-muted);
  }
  
  &-brand {
    &-primary {
      color: var(--color-primary);
    }
    
    &-secondary {
      color: var(--color-secondary);
    }
    
    &-accent {
      color: var(--color-accent);
    }
  }
  
  &-success {
    color: var(--color-success);
  }
  
  &-warning {
    color: var(--color-warning);
  }
  
  &-error {
    color: var(--color-error);
  }
  
  &-info {
    color: var(--color-info);
  }
}

// 边框颜色工具类
.border {
  &-default {
    border-color: var(--color-border);
  }
  
  &-light {
    border-color: var(--color-border-light);
  }
  
  &-primary {
    border-color: var(--color-primary);
  }
  
  &-secondary {
    border-color: var(--color-secondary);
  }
}

// ===== 主题切换动画 =====
* {
  transition: 
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease,
    box-shadow 0.3s ease;
}

// 禁用动画的元素
.no-theme-transition,
.no-theme-transition * {
  transition: none !important;
}

// ===== 系统偏好检测 =====
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    // 如果用户没有手动设置主题，且系统偏好深色模式
    // 自动应用深色主题变量
    --color-background: #0f172a;
    --color-surface: #1e293b;
    --color-surface-variant: #334155;
    --color-text-primary: #f8fafc;
    --color-text-secondary: #cbd5e1;
    --color-text-muted: #94a3b8;
    --color-border: #334155;
    --color-border-light: #475569;
    --color-shadow: rgba(0, 0, 0, 0.3);
    
    --color-nav-bg: rgba(15, 23, 42, 0.95);
    --color-nav-text: #f8fafc;
    --color-nav-hover: #{$secondary-color};
    --color-footer-bg: #0f172a;
    --color-footer-text: #cbd5e1;
    
    --color-card-bg: #1e293b;
    --color-card-border: #334155;
    --color-input-bg: #1e293b;
    --color-input-border: #475569;
    --color-input-focus: #{$secondary-color};
  }
}

// ===== 高对比度偏好检测 =====
@media (prefers-contrast: high) {
  :root:not([data-theme]) {
    --color-border: #000000;
    --color-text-primary: #000000;
    --color-background: #ffffff;
  }
  
  [data-theme="dark"] {
    --color-border: #ffffff;
    --color-text-primary: #ffffff;
    --color-background: #000000;
  }
}

// ===== 减少动画偏好检测 =====
@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}

// ===== 主题特定的组件覆盖 =====

// 按钮在不同主题下的表现
.btn {
  background-color: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
  
  &:hover {
    background-color: var(--color-secondary);
    border-color: var(--color-secondary);
  }
  
  &--secondary {
    background-color: var(--color-surface);
    color: var(--color-text-primary);
    border-color: var(--color-border);
    
    &:hover {
      background-color: var(--color-surface-variant);
    }
  }
  
  &--outline {
    background-color: transparent;
    color: var(--color-primary);
    border-color: var(--color-primary);
    
    &:hover {
      background-color: var(--color-primary);
      color: var(--color-white);
    }
  }
}

// 卡片在不同主题下的表现
.card {
  background-color: var(--color-card-bg);
  border-color: var(--color-card-border);
  box-shadow: var(--color-card-shadow);
}

// 输入框在不同主题下的表现
.form-input,
.form-select,
.form-textarea {
  background-color: var(--color-input-bg);
  border-color: var(--color-input-border);
  color: var(--color-text-primary);
  
  &:focus {
    border-color: var(--color-input-focus);
  }
  
  &::placeholder {
    color: var(--color-text-muted);
  }
}
