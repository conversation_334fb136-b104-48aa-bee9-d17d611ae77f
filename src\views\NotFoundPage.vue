<template>
  <div class="not-found-page">
    <div class="container">
      <div class="content">
        <h1>404</h1>
        <h2>页面未找到</h2>
        <p>抱歉，您访问的页面不存在或已被移除。</p>
        <router-link to="/" class="btn btn--primary">
          返回首页
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 404 页面组件
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;
@use '@/assets/styles/mixins' as *;

.not-found-page {
  min-height: 100vh;
  @include flex-center;
  background: $gray-50;
  
  .container {
    text-align: center;
  }
  
  .content {
    max-width: 500px;
    margin: 0 auto;
  }
  
  h1 {
    font-size: 8rem;
    font-weight: $font-weight-bold;
    @include gradient-text;
    margin-bottom: $spacing-4;
    
    @include mobile-first($breakpoint-md) {
      font-size: 6rem;
    }
  }
  
  h2 {
    color: $gray-800;
    margin-bottom: $spacing-4;
  }
  
  p {
    color: $gray-600;
    margin-bottom: $spacing-8;
    font-size: $font-size-lg;
  }
}
</style>
