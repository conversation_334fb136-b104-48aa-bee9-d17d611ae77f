// 响应式断点混合器

@use 'variables' as *;

@mixin respond-to($breakpoint) {
  @if $breakpoint == sm {
    @media (min-width: $breakpoint-sm) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (min-width: $breakpoint-md) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (min-width: $breakpoint-lg) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (min-width: $breakpoint-xl) {
      @content;
    }
  }
  @if $breakpoint == 2xl {
    @media (min-width: $breakpoint-2xl) {
      @content;
    }
  }
}

// 移动端优先的响应式混合器
@mixin mobile-first($breakpoint) {
  @media (max-width: $breakpoint - 1px) {
    @content;
  }
}

// Flexbox 居中
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Flexbox 垂直居中
@mixin flex-center-vertical {
  display: flex;
  align-items: center;
}

// Flexbox 水平居中
@mixin flex-center-horizontal {
  display: flex;
  justify-content: center;
}

// 文本省略号
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本省略号
@mixin text-ellipsis-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 按钮基础样式
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: $border-radius-md;
  font-weight: $font-weight-medium;
  text-decoration: none;
  cursor: pointer;
  transition: all $transition-base;
  user-select: none;
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 主要按钮样式
@mixin button-primary {
  @include button-base;
  background: $primary-gradient;
  color: $white;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 次要按钮样式
@mixin button-secondary {
  @include button-base;
  background: $white;
  color: $primary-color;
  border: 2px solid $primary-color;
  
  &:hover:not(:disabled) {
    background: $primary-color;
    color: $white;
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
}

// 卡片样式
@mixin card {
  background: $white;
  border-radius: $border-radius-xl;
  box-shadow: $shadow-base;
  transition: all $transition-base;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-xl;
  }
}

// 容器样式
@mixin container {
  width: 100%;
  margin: 0 auto;
  padding: 0 $spacing-4;
  
  @include respond-to(sm) {
    max-width: $container-sm;
  }
  
  @include respond-to(md) {
    max-width: $container-md;
    padding: 0 $spacing-6;
  }
  
  @include respond-to(lg) {
    max-width: $container-lg;
    padding: 0 $spacing-8;
  }
  
  @include respond-to(xl) {
    max-width: $container-xl;
  }
  
  @include respond-to(2xl) {
    max-width: $container-2xl;
  }
}

// 网格系统
@mixin grid($columns: 12, $gap: $spacing-6) {
  display: grid;
  grid-template-columns: repeat($columns, 1fr);
  gap: $gap;
}

// 渐变文字
@mixin gradient-text($gradient: $primary-gradient) {
  background: $gradient;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// 加载动画
@mixin loading-spinner($size: 40px, $color: $primary-color) {
  width: $size;
  height: $size;
  border: 3px solid rgba($color, 0.3);
  border-radius: 50%;
  border-top-color: $color;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 脉冲动画
@mixin pulse-animation($color: $primary-color) {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
}

// 淡入动画
@mixin fade-in($duration: $transition-base) {
  animation: fadeIn $duration ease-in-out;
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}

// 滑入动画
@mixin slide-in-up($duration: $transition-base, $distance: 30px) {
  animation: slideInUp $duration ease-out;

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY($distance);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

// ===== 扩展设计系统混合器 =====

// 玻璃态效果
@mixin glass-effect($opacity: 0.1, $blur: 10px) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur($blur);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// 几何装饰元素
@mixin decoration-circle($size: 4rem, $color: $secondary-color, $position: 'top-right') {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    width: $size;
    height: $size;
    background: $color;
    border-radius: 50%;
    opacity: 0.1;
    z-index: -1;

    @if $position == 'top-right' {
      top: -#{$size / 4};
      right: -#{$size / 4};
    } @else if $position == 'top-left' {
      top: -#{$size / 4};
      left: -#{$size / 4};
    } @else if $position == 'bottom-right' {
      bottom: -#{$size / 4};
      right: -#{$size / 4};
    } @else if $position == 'bottom-left' {
      bottom: -#{$size / 4};
      left: -#{$size / 4};
    }
  }
}

// 三角形装饰
@mixin decoration-triangle($size: 3rem, $color: $accent-color, $position: 'top-left') {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0.1;
    z-index: -1;

    @if $position == 'top-left' {
      top: 0;
      left: 0;
      border-left: $size solid $color;
      border-bottom: $size solid transparent;
    } @else if $position == 'top-right' {
      top: 0;
      right: 0;
      border-right: $size solid $color;
      border-bottom: $size solid transparent;
    } @else if $position == 'bottom-left' {
      bottom: 0;
      left: 0;
      border-left: $size solid $color;
      border-top: $size solid transparent;
    } @else if $position == 'bottom-right' {
      bottom: 0;
      right: 0;
      border-right: $size solid $color;
      border-top: $size solid transparent;
    }
  }
}

// 输入框样式
@mixin input-field {
  width: 100%;
  padding: $spacing-3 $spacing-4;
  background: $white;
  border: 2px solid $gray-300;
  border-radius: $border-radius-md;
  font-family: $font-family-primary;
  font-size: $font-size-base;
  transition: $transition-base;

  &:focus {
    outline: none;
    border-color: $accent-color;
    box-shadow: 0 0 0 3px rgba($accent-color, 0.1);
  }

  &::placeholder {
    color: $gray-400;
  }

  &:disabled {
    background: $gray-100;
    color: $gray-500;
    cursor: not-allowed;
  }

  &.error {
    border-color: $error-color;
    box-shadow: 0 0 0 3px rgba($error-color, 0.1);
  }
}

// 标签样式
@mixin tag($bg: $gray-100, $color: $gray-700, $size: 'sm') {
  display: inline-flex;
  align-items: center;
  gap: $spacing-1;
  background: $bg;
  color: $color;
  border-radius: $border-radius-full;
  font-weight: $font-weight-medium;

  @if $size == 'xs' {
    padding: $spacing-1 $spacing-2;
    font-size: $font-size-xs;
  } @else if $size == 'sm' {
    padding: $spacing-1 $spacing-3;
    font-size: $font-size-sm;
  } @else if $size == 'md' {
    padding: $spacing-2 $spacing-4;
    font-size: $font-size-base;
  }
}

// 徽章样式
@mixin badge($bg: $error-color, $color: $white) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 1.25rem;
  height: 1.25rem;
  padding: 0 $spacing-1;
  background: $bg;
  color: $color;
  border-radius: $border-radius-full;
  font-size: $font-size-xs;
  font-weight: $font-weight-bold;
  line-height: 1;
}

// 分割线样式
@mixin divider($color: $gray-200, $thickness: 1px, $margin: $spacing-4) {
  border: none;
  height: $thickness;
  background: $color;
  margin: $margin 0;
}

// 工具提示样式
@mixin tooltip($bg: $gray-900, $color: $white) {
  position: relative;

  &::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: $spacing-2 $spacing-3;
    background: $bg;
    color: $color;
    border-radius: $border-radius-md;
    font-size: $font-size-sm;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: $transition-base;
    z-index: $z-index-tooltip;
  }

  &::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: $bg;
    opacity: 0;
    visibility: hidden;
    transition: $transition-base;
    z-index: $z-index-tooltip;
  }

  &:hover::after,
  &:hover::before {
    opacity: 1;
    visibility: visible;
  }
}

// 自定义滚动条
@mixin custom-scrollbar($width: 8px, $track: $gray-100, $thumb: $gray-400) {
  &::-webkit-scrollbar {
    width: $width;
  }

  &::-webkit-scrollbar-track {
    background: $track;
    border-radius: $width / 2;
  }

  &::-webkit-scrollbar-thumb {
    background: $thumb;
    border-radius: $width / 2;

    &:hover {
      background: darken($thumb, 10%);
    }
  }
}

// 图片样式
@mixin image-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

@mixin image-contain {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
}

// 隐藏元素（保持可访问性）
@mixin visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

// 重置列表样式
@mixin reset-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

// 重置按钮样式
@mixin reset-button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  font: inherit;
  color: inherit;
}

// ===== 响应式设计混合器 =====

// 响应式字体大小
@mixin responsive-font-size($mobile-size, $tablet-size: null, $desktop-size: null) {
  font-size: $mobile-size;

  @if $tablet-size {
    @include respond-to(md) {
      font-size: $tablet-size;
    }
  }

  @if $desktop-size {
    @include respond-to(lg) {
      font-size: $desktop-size;
    }
  } @else if $tablet-size {
    @include respond-to(lg) {
      font-size: $tablet-size;
    }
  }
}

// 流体字体大小
@mixin fluid-font-size($min-size, $max-size, $min-width: 320px, $max-width: 1200px) {
  font-size: $min-size;

  @media (min-width: $min-width) {
    font-size: calc(#{$min-size} + #{strip-unit($max-size - $min-size)} * ((100vw - #{$min-width}) / #{strip-unit($max-width - $min-width)}));
  }

  @media (min-width: $max-width) {
    font-size: $max-size;
  }
}

// 响应式间距
@mixin responsive-spacing($property, $mobile-value, $tablet-value: null, $desktop-value: null) {
  #{$property}: $mobile-value;

  @if $tablet-value {
    @include respond-to(md) {
      #{$property}: $tablet-value;
    }
  }

  @if $desktop-value {
    @include respond-to(lg) {
      #{$property}: $desktop-value;
    }
  } @else if $tablet-value {
    @include respond-to(lg) {
      #{$property}: $tablet-value;
    }
  }
}

// 响应式网格
@mixin responsive-grid($mobile-cols: 1, $tablet-cols: 2, $desktop-cols: 3, $gap: $spacing-4) {
  display: grid;
  gap: $gap;
  grid-template-columns: repeat($mobile-cols, 1fr);

  @include respond-to(md) {
    grid-template-columns: repeat($tablet-cols, 1fr);
  }

  @include respond-to(lg) {
    grid-template-columns: repeat($desktop-cols, 1fr);
  }
}

// 容器查询支持
@mixin container-query($condition) {
  @supports (container-type: inline-size) {
    @container #{$condition} {
      @content;
    }
  }
}

// 响应式显示/隐藏
@mixin hide-on($breakpoint) {
  @include respond-to($breakpoint) {
    display: none !important;
  }
}

@mixin show-on($breakpoint) {
  display: none !important;

  @include respond-to($breakpoint) {
    display: block !important;
  }
}

// 响应式文本对齐
@mixin responsive-text-align($mobile: left, $tablet: null, $desktop: null) {
  text-align: $mobile;

  @if $tablet {
    @include respond-to(md) {
      text-align: $tablet;
    }
  }

  @if $desktop {
    @include respond-to(lg) {
      text-align: $desktop;
    }
  }
}

// 移动端断点 mixin
@mixin mobile {
  @media (max-width: $breakpoint-md - 1px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: $breakpoint-md) and (max-width: $breakpoint-lg - 1px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: $breakpoint-lg) {
    @content;
  }
}

// 卡片基础样式 mixin
@mixin card-base {
  background: var(--color-background);
  border-radius: $border-radius-lg;
  box-shadow: $shadow-md;
  padding: $spacing-6;
  border: 1px solid var(--color-border);
  transition: all $transition-base;
}

// 卡片内边距 mixin
@mixin card-padding($size: 'md') {
  @if $size == 'sm' {
    padding: $spacing-4;
  } @else if $size == 'lg' {
    padding: $spacing-8;
  } @else {
    padding: $spacing-6;
  }
}

// 卡片悬停效果 mixin
@mixin card-hover {
  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
}

// 按钮变体 mixin
@mixin btn-variant($bg-color, $text-color, $hover-bg-color: null) {
  background-color: $bg-color;
  color: $text-color;
  border-color: $bg-color;

  &:hover {
    background-color: $hover-bg-color or darken($bg-color, 10%);
    border-color: $hover-bg-color or darken($bg-color, 10%);
    color: $text-color;
  }

  &:focus {
    box-shadow: 0 0 0 3px rgba($bg-color, 0.25);
  }

  &:active {
    background-color: darken($bg-color, 15%);
    border-color: darken($bg-color, 15%);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 工具函数：移除单位
@function strip-unit($number) {
  @if type-of($number) == 'number' and not unitless($number) {
    @return $number / ($number * 0 + 1);
  }
  @return $number;
}
