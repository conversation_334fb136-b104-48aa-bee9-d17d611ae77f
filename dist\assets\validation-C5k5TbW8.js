const r=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,c=/^1[3-9]\d{9}$/,l=e=>r.test(e),o=e=>c.test(e),n=e=>typeof e=="string"?e.trim().length>0:e!=null,i=(e,t,s)=>{const a=e.trim().length;return s!==void 0?a>=t&&a<=s:a>=t},d=e=>{const t={};return n(e.name)?i(e.name,2,50)||(t.name="姓名长度应在2-50个字符之间"):t.name="请输入您的姓名",n(e.email)?l(e.email)||(t.email="请输入有效的邮箱地址"):t.email="请输入您的邮箱",e.phone&&!o(e.phone)&&(t.phone="请输入有效的手机号码"),e.company&&!i(e.company,2,100)&&(t.company="公司名称长度应在2-100个字符之间"),n(e.service_interest)||(t.service_interest="请选择您感兴趣的服务"),n(e.message)?i(e.message,10,1e3)||(t.message="留言长度应在10-1000个字符之间"):t.message="请输入您的留言",["email","phone"].includes(e.preferred_contact)||(t.preferred_contact="请选择联系方式偏好"),{isValid:Object.keys(t).length===0,errors:t}};export{d as v};
