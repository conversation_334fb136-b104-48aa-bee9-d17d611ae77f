<template>
  <Transition
    :name="transitionName"
    mode="out-in"
    @before-enter="onBeforeEnter"
    @enter="onEnter"
    @after-enter="onAfterEnter"
    @before-leave="onBeforeLeave"
    @leave="onLeave"
    @after-leave="onAfterLeave"
  >
    <slot />
  </Transition>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { gsap } from 'gsap'

interface Props {
  type?: 'fade' | 'slide' | 'scale' | 'rotate' | 'curtain' | 'wave' | 'flip'
  duration?: number
  direction?: 'left' | 'right' | 'up' | 'down'
  easing?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'fade',
  duration: 0.6,
  direction: 'right',
  easing: 'power2.out'
})

const route = useRoute()
const isAnimating = ref(false)

// 根据路由或 props 确定过渡动画
const transitionName = computed(() => {
  return route.meta?.transition || props.type
})

// 动画钩子函数
const onBeforeEnter = (el: Element) => {
  isAnimating.value = true
  const element = el as HTMLElement
  
  // 设置初始状态
  switch (props.type) {
    case 'fade':
      gsap.set(element, { opacity: 0 })
      break
    case 'slide':
      const slideDistance = 100
      const slideX = props.direction === 'left' ? -slideDistance : props.direction === 'right' ? slideDistance : 0
      const slideY = props.direction === 'up' ? -slideDistance : props.direction === 'down' ? slideDistance : 0
      gsap.set(element, { x: slideX, y: slideY, opacity: 0 })
      break
    case 'scale':
      gsap.set(element, { scale: 0.8, opacity: 0 })
      break
    case 'rotate':
      gsap.set(element, { rotation: -180, opacity: 0, transformOrigin: 'center center' })
      break
    case 'curtain':
      gsap.set(element, { scaleY: 0, transformOrigin: 'top center' })
      break
    case 'wave':
      gsap.set(element, { skewX: 15, x: 100, opacity: 0 })
      break
    case 'flip':
      gsap.set(element, { rotationY: -90, opacity: 0, transformOrigin: 'center center' })
      break
  }
}

const onEnter = (el: Element, done: () => void) => {
  const element = el as HTMLElement
  
  nextTick(() => {
    // 执行进入动画
    switch (props.type) {
      case 'fade':
        gsap.to(element, {
          opacity: 1,
          duration: props.duration,
          ease: props.easing,
          onComplete: done
        })
        break
      case 'slide':
        gsap.to(element, {
          x: 0,
          y: 0,
          opacity: 1,
          duration: props.duration,
          ease: props.easing,
          onComplete: done
        })
        break
      case 'scale':
        gsap.to(element, {
          scale: 1,
          opacity: 1,
          duration: props.duration,
          ease: 'back.out(1.7)',
          onComplete: done
        })
        break
      case 'rotate':
        gsap.to(element, {
          rotation: 0,
          opacity: 1,
          duration: props.duration,
          ease: props.easing,
          onComplete: done
        })
        break
      case 'curtain':
        gsap.to(element, {
          scaleY: 1,
          duration: props.duration,
          ease: props.easing,
          onComplete: done
        })
        break
      case 'wave':
        gsap.to(element, {
          skewX: 0,
          x: 0,
          opacity: 1,
          duration: props.duration,
          ease: 'elastic.out(1, 0.3)',
          onComplete: done
        })
        break
      case 'flip':
        gsap.to(element, {
          rotationY: 0,
          opacity: 1,
          duration: props.duration,
          ease: props.easing,
          onComplete: done
        })
        break
      default:
        done()
    }
  })
}

const onAfterEnter = (el: Element) => {
  isAnimating.value = false
  // 清理样式
  gsap.set(el, { clearProps: 'all' })
}

const onBeforeLeave = (el: Element) => {
  isAnimating.value = true
  // 确保元素在正确的位置开始离开动画
  const element = el as HTMLElement
  gsap.set(element, { position: 'absolute', top: 0, left: 0, width: '100%' })
}

const onLeave = (el: Element, done: () => void) => {
  const element = el as HTMLElement
  const leaveDuration = props.duration * 0.7
  
  // 执行离开动画
  switch (props.type) {
    case 'fade':
      gsap.to(element, {
        opacity: 0,
        duration: leaveDuration,
        ease: 'power2.in',
        onComplete: done
      })
      break
    case 'slide':
      const slideDistance = -100
      const slideX = props.direction === 'left' ? slideDistance : props.direction === 'right' ? -slideDistance : 0
      const slideY = props.direction === 'up' ? slideDistance : props.direction === 'down' ? -slideDistance : 0
      gsap.to(element, {
        x: slideX,
        y: slideY,
        opacity: 0,
        duration: leaveDuration,
        ease: 'power2.in',
        onComplete: done
      })
      break
    case 'scale':
      gsap.to(element, {
        scale: 1.2,
        opacity: 0,
        duration: leaveDuration,
        ease: 'power2.in',
        onComplete: done
      })
      break
    case 'rotate':
      gsap.to(element, {
        rotation: 180,
        opacity: 0,
        duration: leaveDuration,
        ease: 'power2.in',
        onComplete: done
      })
      break
    case 'curtain':
      gsap.to(element, {
        scaleY: 0,
        duration: leaveDuration,
        ease: 'power2.in',
        transformOrigin: 'bottom center',
        onComplete: done
      })
      break
    case 'wave':
      gsap.to(element, {
        skewX: -15,
        x: -100,
        opacity: 0,
        duration: leaveDuration,
        ease: 'power2.in',
        onComplete: done
      })
      break
    case 'flip':
      gsap.to(element, {
        rotationY: 90,
        opacity: 0,
        duration: leaveDuration,
        ease: 'power2.in',
        onComplete: done
      })
      break
    default:
      done()
  }
}

const onAfterLeave = (el: Element) => {
  isAnimating.value = false
  // 清理样式
  gsap.set(el, { clearProps: 'all' })
}

// 暴露动画状态
defineExpose({
  isAnimating
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

// 基础过渡容器样式
:deep(.v-enter-active),
:deep(.v-leave-active) {
  transition: none; // 禁用 CSS 过渡，使用 GSAP
}

// 确保过渡期间的布局稳定
:deep(.v-leave-active) {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1;
}

:deep(.v-enter-active) {
  z-index: 2;
}

// 为不同的过渡类型提供基础样式
.fade-enter-active,
.fade-leave-active,
.slide-enter-active,
.slide-leave-active,
.scale-enter-active,
.scale-leave-active,
.rotate-enter-active,
.rotate-leave-active,
.curtain-enter-active,
.curtain-leave-active,
.wave-enter-active,
.wave-leave-active,
.flip-enter-active,
.flip-leave-active {
  transition: none !important;
}

// 3D 变换支持
.rotate-enter-active,
.rotate-leave-active,
.flip-enter-active,
.flip-leave-active {
  transform-style: preserve-3d;
  perspective: 1000px;
}

// 响应式调整
@media (max-width: 768px) {
  // 在移动设备上使用更简单的动画
  :deep(.v-enter-active),
  :deep(.v-leave-active) {
    transition: opacity 0.3s ease !important;
  }
}

// 减少动画偏好
@media (prefers-reduced-motion: reduce) {
  :deep(.v-enter-active),
  :deep(.v-leave-active) {
    transition: none !important;
    animation: none !important;
  }
  
  // 直接显示内容，不使用动画
  :deep(.v-enter-from),
  :deep(.v-leave-to) {
    opacity: 1 !important;
    transform: none !important;
  }
}
</style>
