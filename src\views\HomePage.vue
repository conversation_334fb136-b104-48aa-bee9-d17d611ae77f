<template>
  <AppLayout>
    <!-- 主视觉区域 -->
    <HeroSection />

    <!-- 服务概览 -->
    <ServicesOverview />

    <!-- 公司优势 -->
    <CompanyAdvantages />

    <!-- 客户案例 -->
    <CaseShowcase />

    <!-- 联系我们 -->
    <ContactSection />
  </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/components/layout/AppLayout.vue'
import HeroSection from '@/components/business/HeroSection.vue'
import ServicesOverview from '@/components/business/ServicesOverview.vue'
import CompanyAdvantages from '@/components/business/CompanyAdvantages.vue'
import CaseShowcase from '@/components/business/CaseShowcase.vue'
import ContactSection from '@/components/business/ContactSection.vue'
</script>

<style lang="scss" scoped>
/* 首页样式由各个组件自己处理，这里不需要额外样式 */
</style>
