<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="case6-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#d69e2e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f6ad55;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="#f7fafc"/>
  <rect x="50" y="50" width="300" height="200" rx="15" fill="url(#case6-gradient)"/>
  <circle cx="150" cy="120" r="25" fill="rgba(255,255,255,0.3)"/>
  <circle cx="250" cy="180" r="35" fill="rgba(255,255,255,0.2)"/>
  <text x="200" y="155" text-anchor="middle" fill="white" font-size="14" font-family="Arial, sans-serif">化妆品包装</text>
</svg>
