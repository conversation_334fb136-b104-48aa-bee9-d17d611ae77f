<template>
  <div class="search-box" :class="{ 'search-box--focused': isFocused }">
    <div class="search-input-container">
      <!-- 搜索图标 -->
      <i class="search-icon icon-search"></i>
      
      <!-- 搜索输入框 -->
      <input
        ref="inputRef"
        v-model="searchValue"
        type="text"
        class="search-input"
        :placeholder="placeholder"
        :disabled="disabled"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
        @input="handleInput"
      />
      
      <!-- 清除按钮 -->
      <button
        v-if="searchValue && clearable"
        class="clear-button"
        @click="handleClear"
        aria-label="清除搜索"
      >
        <i class="icon-close"></i>
      </button>
      
      <!-- 搜索按钮 -->
      <button
        v-if="showSearchButton"
        class="search-button"
        :disabled="disabled || (!searchValue && !allowEmpty)"
        @click="handleSearch"
        aria-label="搜索"
      >
        <i class="icon-search"></i>
        <span v-if="searchButtonText">{{ searchButtonText }}</span>
      </button>
    </div>
    
    <!-- 搜索建议下拉列表 -->
    <Transition name="suggestions">
      <div
        v-if="showSuggestions && filteredSuggestions.length > 0"
        class="suggestions-dropdown"
      >
        <ul class="suggestions-list">
          <li
            v-for="(suggestion, index) in filteredSuggestions"
            :key="getSuggestionKey(suggestion, index)"
            class="suggestion-item"
            :class="{ 'highlighted': index === highlightedIndex }"
            @click="handleSuggestionClick(suggestion)"
            @mouseenter="highlightedIndex = index"
          >
            <i v-if="suggestion.icon" :class="suggestion.icon" class="suggestion-icon"></i>
            <span class="suggestion-text" v-html="highlightMatch(suggestion.text)"></span>
            <span v-if="suggestion.category" class="suggestion-category">{{ suggestion.category }}</span>
          </li>
        </ul>
      </div>
    </Transition>
    
    <!-- 最近搜索 -->
    <Transition name="recent">
      <div
        v-if="showRecent && recentSearches.length > 0 && !searchValue && isFocused"
        class="recent-searches"
      >
        <div class="recent-header">
          <span class="recent-title">最近搜索</span>
          <button class="clear-recent-button" @click="clearRecentSearches">
            清除
          </button>
        </div>
        <ul class="recent-list">
          <li
            v-for="(recent, index) in recentSearches"
            :key="index"
            class="recent-item"
            @click="handleRecentClick(recent)"
          >
            <i class="icon-history"></i>
            <span class="recent-text">{{ recent }}</span>
            <button
              class="remove-recent-button"
              @click.stop="removeRecentSearch(recent)"
              aria-label="删除"
            >
              <i class="icon-close"></i>
            </button>
          </li>
        </ul>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { debounce } from '@/utils'

interface SearchSuggestion {
  text: string
  value?: string
  icon?: string
  category?: string
}

interface Props {
  modelValue: string
  placeholder?: string
  disabled?: boolean
  clearable?: boolean
  showSearchButton?: boolean
  searchButtonText?: string
  suggestions?: SearchSuggestion[]
  showSuggestions?: boolean
  showRecent?: boolean
  maxRecentItems?: number
  allowEmpty?: boolean
  debounceDelay?: number
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'search', value: string): void
  (e: 'clear'): void
  (e: 'focus', event: FocusEvent): void
  (e: 'blur', event: FocusEvent): void
  (e: 'suggestion-click', suggestion: SearchSuggestion): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入搜索关键词...',
  disabled: false,
  clearable: true,
  showSearchButton: false,
  suggestions: () => [],
  showSuggestions: true,
  showRecent: true,
  maxRecentItems: 5,
  allowEmpty: false,
  debounceDelay: 300
})

const emit = defineEmits<Emits>()

// 响应式数据
const inputRef = ref<HTMLInputElement>()
const isFocused = ref(false)
const highlightedIndex = ref(-1)
const recentSearches = ref<string[]>([])

// 计算属性
const searchValue = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value)
})

const filteredSuggestions = computed(() => {
  if (!searchValue.value || !props.suggestions.length) return []
  
  const query = searchValue.value.toLowerCase()
  return props.suggestions.filter(suggestion =>
    suggestion.text.toLowerCase().includes(query)
  ).slice(0, 10) // 限制显示数量
})

// 方法
const getSuggestionKey = (suggestion: SearchSuggestion, index: number): string => {
  return suggestion.value || suggestion.text || index.toString()
}

const highlightMatch = (text: string): string => {
  if (!searchValue.value) return text
  
  const query = searchValue.value.toLowerCase()
  const regex = new RegExp(`(${query})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

const handleFocus = (event: FocusEvent) => {
  isFocused.value = true
  highlightedIndex.value = -1
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  // 延迟隐藏，以便处理点击建议项
  setTimeout(() => {
    isFocused.value = false
    highlightedIndex.value = -1
  }, 200)
  emit('blur', event)
}

const handleInput = debounce(() => {
  highlightedIndex.value = -1
}, props.debounceDelay)

const handleKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'Enter':
      event.preventDefault()
      if (highlightedIndex.value >= 0 && filteredSuggestions.value.length > 0) {
        handleSuggestionClick(filteredSuggestions.value[highlightedIndex.value])
      } else {
        handleSearch()
      }
      break
      
    case 'ArrowDown':
      event.preventDefault()
      if (filteredSuggestions.value.length > 0) {
        highlightedIndex.value = Math.min(
          highlightedIndex.value + 1,
          filteredSuggestions.value.length - 1
        )
      }
      break
      
    case 'ArrowUp':
      event.preventDefault()
      if (filteredSuggestions.value.length > 0) {
        highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1)
      }
      break
      
    case 'Escape':
      inputRef.value?.blur()
      break
  }
}

const handleSearch = () => {
  const value = searchValue.value.trim()
  if (value || props.allowEmpty) {
    emit('search', value)
    addToRecentSearches(value)
    inputRef.value?.blur()
  }
}

const handleClear = () => {
  searchValue.value = ''
  emit('clear')
  inputRef.value?.focus()
}

const handleSuggestionClick = (suggestion: SearchSuggestion) => {
  const value = suggestion.value || suggestion.text
  searchValue.value = value
  emit('suggestion-click', suggestion)
  emit('search', value)
  addToRecentSearches(value)
  inputRef.value?.blur()
}

const handleRecentClick = (recent: string) => {
  searchValue.value = recent
  emit('search', recent)
  inputRef.value?.blur()
}

const addToRecentSearches = (value: string) => {
  if (!value || !props.showRecent) return
  
  // 移除已存在的项
  const index = recentSearches.value.indexOf(value)
  if (index > -1) {
    recentSearches.value.splice(index, 1)
  }
  
  // 添加到开头
  recentSearches.value.unshift(value)
  
  // 限制数量
  if (recentSearches.value.length > props.maxRecentItems) {
    recentSearches.value = recentSearches.value.slice(0, props.maxRecentItems)
  }
  
  // 保存到本地存储
  localStorage.setItem('search-recent', JSON.stringify(recentSearches.value))
}

const removeRecentSearch = (value: string) => {
  const index = recentSearches.value.indexOf(value)
  if (index > -1) {
    recentSearches.value.splice(index, 1)
    localStorage.setItem('search-recent', JSON.stringify(recentSearches.value))
  }
}

const clearRecentSearches = () => {
  recentSearches.value = []
  localStorage.removeItem('search-recent')
}

// 初始化最近搜索
const initRecentSearches = () => {
  try {
    const stored = localStorage.getItem('search-recent')
    if (stored) {
      recentSearches.value = JSON.parse(stored)
    }
  } catch (error) {
    console.warn('Failed to load recent searches:', error)
  }
}

// 生命周期
initRecentSearches()
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.search-box {
  position: relative;
  width: 100%;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  transition: all 0.3s ease-in-out;
  
  .search-box--focused & {
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }
}

.search-icon {
  position: absolute;
  left: 1rem;
  color: #a0aec0;
  font-size: 1rem;
  z-index: 1;
}

.search-input {
  flex: 1;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: none;
  background: transparent;
  font-size: 1rem;
  color: #2d3748;
  
  &::placeholder {
    color: #a0aec0;
  }
  
  &:focus {
    outline: none;
  }
  
  &:disabled {
    background: #f7fafc;
    color: #a0aec0;
    cursor: not-allowed;
  }
}

.clear-button {
  position: absolute;
  right: 0.5rem;
  width: 1.5rem;
  height: 1.5rem;
  background: none;
  border: none;
  border-radius: 50%;
  color: #a0aec0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background: #f7fafc;
    color: #4a5568;
  }
}

.search-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #1a365d;
  color: white;
  border: none;
  border-radius: 0 0.375rem 0.375rem 0;
  cursor: pointer;
  transition: background 0.3s ease-in-out;
  
  &:hover:not(:disabled) {
    background: #2c5282;
  }
  
  &:disabled {
    background: #a0aec0;
    cursor: not-allowed;
  }
}

.suggestions-dropdown,
.recent-searches {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 0.25rem;
  max-height: 300px;
  overflow-y: auto;
}

.suggestions-list,
.recent-list {
  list-style: none;
  margin: 0;
  padding: 0.5rem 0;
}

.suggestion-item,
.recent-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background 0.2s ease-in-out;
  
  &:hover,
  &.highlighted {
    background: #f7fafc;
  }
}

.suggestion-icon {
  color: #a0aec0;
  font-size: 0.875rem;
}

.suggestion-text {
  flex: 1;
  
  :deep(mark) {
    background: #fed7d7;
    color: #c53030;
    padding: 0;
  }
}

.suggestion-category {
  font-size: 0.75rem;
  color: #a0aec0;
  background: #f7fafc;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.recent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f7fafc;
}

.recent-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4a5568;
}

.clear-recent-button {
  font-size: 0.75rem;
  color: #a0aec0;
  background: none;
  border: none;
  cursor: pointer;
  
  &:hover {
    color: #4a5568;
  }
}

.recent-text {
  flex: 1;
  color: #4a5568;
}

.remove-recent-button {
  width: 1.25rem;
  height: 1.25rem;
  background: none;
  border: none;
  border-radius: 50%;
  color: #a0aec0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: #f7fafc;
    color: #4a5568;
  }
}

// 动画
.suggestions-enter-active,
.suggestions-leave-active,
.recent-enter-active,
.recent-leave-active {
  transition: all 0.2s ease-in-out;
}

.suggestions-enter-from,
.suggestions-leave-to,
.recent-enter-from,
.recent-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

// 图标
.icon-search::before { content: '🔍'; }
.icon-close::before { content: '✕'; }
.icon-history::before { content: '🕒'; }

// 响应式
@media (max-width: 640px) {
  .search-input {
    font-size: 0.875rem;
    padding: 0.625rem 1rem 0.625rem 2.25rem;
  }
  
  .search-icon {
    left: 0.75rem;
    font-size: 0.875rem;
  }
  
  .search-button {
    padding: 0.625rem 0.75rem;
    font-size: 0.875rem;
  }
}
</style>
