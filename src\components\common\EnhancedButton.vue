<template>
  <MicroInteractions 
    :type="interactionType" 
    :intensity="intensity"
    :color="color"
  >
    <button
      :class="buttonClasses"
      :disabled="disabled || loading"
      @click="handleClick"
      ref="buttonRef"
    >
      <!-- 加载状态 -->
      <div v-if="loading" class="btn-loading">
        <div class="loading-spinner"></div>
      </div>
      
      <!-- 按钮内容 -->
      <div class="btn-content" :class="{ 'btn-content--loading': loading }">
        <!-- 图标 -->
        <i v-if="icon && iconPosition === 'left'" :class="icon" class="btn-icon btn-icon--left"></i>
        
        <!-- 文本内容 -->
        <span class="btn-text">
          <slot>{{ text }}</slot>
        </span>
        
        <!-- 右侧图标 -->
        <i v-if="icon && iconPosition === 'right'" :class="icon" class="btn-icon btn-icon--right"></i>
      </div>
      
      <!-- 波纹效果背景 -->
      <div class="btn-ripple-bg" ref="rippleBg"></div>
      
      <!-- 悬停效果背景 -->
      <div class="btn-hover-bg"></div>
    </button>
  </MicroInteractions>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { gsap } from 'gsap'
import MicroInteractions from './MicroInteractions.vue'

interface Props {
  // 基础属性
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  text?: string
  disabled?: boolean
  loading?: boolean
  
  // 图标
  icon?: string
  iconPosition?: 'left' | 'right'
  
  // 交互效果
  interactionType?: 'magnetic' | 'particles' | 'ripple' | 'glow' | 'tilt'
  intensity?: number
  color?: string
  
  // 动画
  animateOnMount?: boolean
  hoverAnimation?: boolean
  
  // 样式
  rounded?: boolean
  fullWidth?: boolean
  gradient?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  iconPosition: 'left',
  interactionType: 'ripple',
  intensity: 1,
  color: '#ff6b35',
  animateOnMount: false,
  hoverAnimation: true,
  rounded: false,
  fullWidth: false,
  gradient: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

// 模板引用
const buttonRef = ref<HTMLElement>()
const rippleBg = ref<HTMLElement>()

// 计算属性
const buttonClasses = computed(() => [
  'enhanced-btn',
  `enhanced-btn--${props.variant}`,
  `enhanced-btn--${props.size}`,
  {
    'enhanced-btn--disabled': props.disabled,
    'enhanced-btn--loading': props.loading,
    'enhanced-btn--rounded': props.rounded,
    'enhanced-btn--full-width': props.fullWidth,
    'enhanced-btn--gradient': props.gradient,
    'enhanced-btn--with-icon': props.icon,
    'enhanced-btn--icon-right': props.icon && props.iconPosition === 'right'
  }
])

// 方法
const handleClick = (event: MouseEvent) => {
  if (props.disabled || props.loading) return
  
  // 创建点击波纹效果
  createClickRipple(event)
  
  // 发射点击事件
  emit('click', event)
}

const createClickRipple = (event: MouseEvent) => {
  if (!buttonRef.value || !rippleBg.value) return
  
  const rect = buttonRef.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  // 创建波纹元素
  const ripple = document.createElement('div')
  ripple.className = 'btn-ripple'
  ripple.style.left = `${x}px`
  ripple.style.top = `${y}px`
  
  rippleBg.value.appendChild(ripple)
  
  // 动画
  gsap.fromTo(ripple, 
    {
      scale: 0,
      opacity: 0.6
    },
    {
      scale: 4,
      opacity: 0,
      duration: 0.6,
      ease: 'power2.out',
      onComplete: () => {
        ripple.remove()
      }
    }
  )
}

const animateMount = () => {
  if (!props.animateOnMount || !buttonRef.value) return
  
  gsap.fromTo(buttonRef.value,
    {
      scale: 0.8,
      opacity: 0
    },
    {
      scale: 1,
      opacity: 1,
      duration: 0.5,
      ease: 'back.out(1.7)'
    }
  )
}

const setupHoverAnimation = () => {
  if (!props.hoverAnimation || !buttonRef.value) return
  
  const button = buttonRef.value
  
  button.addEventListener('mouseenter', () => {
    gsap.to(button, {
      scale: 1.05,
      duration: 0.3,
      ease: 'power2.out'
    })
  })
  
  button.addEventListener('mouseleave', () => {
    gsap.to(button, {
      scale: 1,
      duration: 0.3,
      ease: 'power2.out'
    })
  })
}

// 生命周期
onMounted(() => {
  animateMount()
  setupHoverAnimation()
})
</script>

<style lang="scss" scoped>
@use 'sass:color';
@use '@/assets/styles/variables' as *;

.enhanced-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-family: inherit;
  font-weight: $font-weight-medium;
  text-decoration: none;
  transition: all 0.3s ease;
  overflow: hidden;
  user-select: none;
  outline: none;
  
  // 基础尺寸
  &--xs {
    padding: $spacing-1 $spacing-2;
    font-size: $font-size-xs;
    border-radius: $border-radius-sm;
  }
  
  &--sm {
    padding: $spacing-2 $spacing-3;
    font-size: $font-size-sm;
    border-radius: $border-radius-md;
  }
  
  &--md {
    padding: $spacing-3 $spacing-4;
    font-size: $font-size-base;
    border-radius: $border-radius-md;
  }
  
  &--lg {
    padding: $spacing-4 $spacing-6;
    font-size: $font-size-lg;
    border-radius: $border-radius-lg;
  }
  
  &--xl {
    padding: $spacing-5 $spacing-8;
    font-size: $font-size-xl;
    border-radius: $border-radius-lg;
  }
  
  // 变体样式
  &--primary {
    background: var(--color-primary);
    color: var(--color-white);
    
    &:hover:not(:disabled) {
      background: var(--color-secondary);
    }
  }
  
  &--secondary {
    background: var(--color-surface);
    color: var(--color-text-primary);
    border: 2px solid var(--color-border);
    
    &:hover:not(:disabled) {
      background: var(--color-surface-variant);
      border-color: var(--color-primary);
    }
  }
  
  &--outline {
    background: transparent;
    color: var(--color-primary);
    border: 2px solid var(--color-primary);
    
    &:hover:not(:disabled) {
      background: var(--color-primary);
      color: var(--color-white);
    }
  }
  
  &--ghost {
    background: transparent;
    color: var(--color-text-primary);
    
    &:hover:not(:disabled) {
      background: var(--color-surface-variant);
    }
  }
  
  &--danger {
    background: var(--color-error);
    color: var(--color-white);
    
    &:hover:not(:disabled) {
      background: #{color.adjust($error-color, $lightness: -10%)};
    }
  }
  
  &--success {
    background: var(--color-success);
    color: var(--color-white);
    
    &:hover:not(:disabled) {
      background: #{color.adjust($success-color, $lightness: -10%)};
    }
  }
  
  // 修饰符
  &--rounded {
    border-radius: $border-radius-full;
  }
  
  &--full-width {
    width: 100%;
  }
  
  &--gradient {
    background: var(--gradient-primary);
    
    &:hover:not(:disabled) {
      background: var(--gradient-secondary);
    }
  }
  
  // 状态
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }
  
  &--loading {
    cursor: wait;
  }
  
  // 图标
  .btn-icon {
    &--left {
      margin-right: $spacing-2;
    }
    
    &--right {
      margin-left: $spacing-2;
    }
  }
  
  // 内容
  .btn-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    transition: opacity 0.3s ease;
    
    &--loading {
      opacity: 0;
    }
  }
  
  // 加载状态
  .btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
  }
  
  .loading-spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  // 波纹效果
  .btn-ripple-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    z-index: 1;
  }
  
  .btn-ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    pointer-events: none;
  }
  
  // 悬停背景
  .btn-hover-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }
  
  &:hover:not(:disabled) .btn-hover-bg {
    opacity: 1;
  }
}

// 动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式调整
@media (max-width: 640px) {
  .enhanced-btn {
    &--lg {
      padding: $spacing-3 $spacing-5;
      font-size: $font-size-base;
    }
    
    &--xl {
      padding: $spacing-4 $spacing-6;
      font-size: $font-size-lg;
    }
  }
}

// 减少动画偏好
@media (prefers-reduced-motion: reduce) {
  .enhanced-btn {
    transition: none !important;
    
    * {
      animation: none !important;
      transition: none !important;
    }
  }
}
</style>
