import{d as g,r as o,c as v}from"./index-Dz-_Smvi.js";const y=g("services",()=>{const s=o([]),t=o(null),e=o(!1),c=o(null),n=v(()=>{const r=new Set(s.value.map(a=>a.category));return Array.from(r)}),l=v(()=>r=>s.value.filter(a=>a.category===r));return{services:s,currentService:t,loading:e,error:c,serviceCategories:n,getServicesByCategory:l,fetchServices:async()=>{e.value=!0,c.value=null;try{s.value=[{id:"1",title:"包装设计服务",description:"专业的包装设计，提升产品形象",category:"设计服务",features:["创意设计","品牌一致性","市场导向"],images:["/images/service1.jpg"],icon:"design"},{id:"2",title:"包装生产服务",description:"高质量的包装生产制造",category:"生产服务",features:["质量保证","快速交付","成本优化"],images:["/images/service2.jpg"],icon:"production"}]}catch(r){c.value="获取服务数据失败",console.error("Error fetching services:",r)}finally{e.value=!1}},getServiceById:async r=>{const a=s.value.find(u=>u.id===r);return a?(t.value=a,a):(e.value=!0,e.value=!1,null)},clearError:()=>{c.value=null}}}),p=g("contact",()=>{const s=o(!1),t=o(null),e=o(!1),c=o({name:"包装解决方案有限公司",description:"专业的包装一站式解决方案提供商",address:"北京市朝阳区某某大厦1001室",phone:"+86 ************",email:"<EMAIL>",website:"www.packaging-solutions.com",socialMedia:{wechat:"packaging_solutions",weibo:"@包装解决方案",linkedin:"packaging-solutions"},businessHours:{weekdays:"周一至周五 9:00-18:00",weekends:"周六 9:00-17:00"}});return{loading:s,error:t,submitSuccess:e,companyInfo:c,submitContactForm:async i=>{s.value=!0,t.value=null,e.value=!1;try{await new Promise(r=>setTimeout(r,1e3)),e.value=!0,console.log("Contact form submitted:",i)}catch(r){t.value="提交失败，请稍后重试",console.error("Error submitting contact form:",r)}finally{s.value=!1}},clearError:()=>{t.value=null},clearSuccess:()=>{e.value=!1},resetForm:()=>{t.value=null,e.value=!1,s.value=!1}}});export{p as a,y as u};
