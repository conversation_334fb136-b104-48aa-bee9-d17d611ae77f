<template>
  <Teleport to="body">
    <Transition name="modal" appear>
      <div v-if="modelValue" class="modal-overlay" @click="handleOverlayClick">
        <div
          class="modal-container"
          :class="[
            `modal--${size}`,
            { 'modal--fullscreen': fullscreen }
          ]"
          @click.stop
        >
          <!-- 模态框头部 -->
          <header v-if="showHeaderComputed" class="modal-header">
            <h2 v-if="title" class="modal-title">{{ title }}</h2>
            <slot name="header" />
            <button
              v-if="closable"
              class="modal-close-button"
              @click="handleClose"
              aria-label="关闭"
            >
              <i class="icon-close"></i>
            </button>
          </header>

          <!-- 模态框内容 -->
          <main class="modal-body" :class="{ 'modal-body--no-padding': noPadding }">
            <slot />
          </main>

          <!-- 模态框底部 -->
          <footer v-if="showFooterComputed" class="modal-footer">
            <slot name="footer">
              <div class="modal-actions">
                <button
                  v-if="showCancel"
                  class="btn btn--secondary"
                  @click="handleCancel"
                >
                  {{ cancelText }}
                </button>
                <button
                  v-if="showConfirm"
                  class="btn btn--primary"
                  :disabled="confirmDisabled"
                  @click="handleConfirm"
                >
                  {{ confirmText }}
                </button>
              </div>
            </slot>
          </footer>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, watch, nextTick } from 'vue'

interface Props {
  modelValue: boolean
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  fullscreen?: boolean
  closable?: boolean
  closeOnOverlay?: boolean
  showHeader?: boolean
  showFooter?: boolean
  showCancel?: boolean
  showConfirm?: boolean
  cancelText?: string
  confirmText?: string
  confirmDisabled?: boolean
  noPadding?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'close'): void
  (e: 'cancel'): void
  (e: 'confirm'): void
  (e: 'opened'): void
  (e: 'closed'): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  fullscreen: false,
  closable: true,
  closeOnOverlay: true,
  showHeader: true,
  showFooter: false,
  showCancel: false,
  showConfirm: false,
  cancelText: '取消',
  confirmText: '确定',
  confirmDisabled: false,
  noPadding: false
})

const emit = defineEmits<Emits>()

// 计算属性
const showHeaderComputed = computed(() => {
  return props.showHeader && (props.title || props.closable)
})

const showFooterComputed = computed(() => {
  return props.showFooter || props.showCancel || props.showConfirm
})

// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
  emit('close')
}

const handleCancel = () => {
  emit('cancel')
  handleClose()
}

const handleConfirm = () => {
  emit('confirm')
}

const handleOverlayClick = () => {
  if (props.closeOnOverlay) {
    handleClose()
  }
}

// 监听模态框状态变化
watch(() => props.modelValue, async (newValue) => {
  if (newValue) {
    await nextTick()
    emit('opened')
    
    // 阻止背景滚动
    document.body.style.overflow = 'hidden'
  } else {
    emit('closed')
    
    // 恢复背景滚动
    document.body.style.overflow = ''
  }
})

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.closable) {
    handleClose()
  }
}

// 监听键盘事件
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    document.addEventListener('keydown', handleKeydown)
  } else {
    document.removeEventListener('keydown', handleKeydown)
  }
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
  padding: 1rem;
}

.modal-container {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  &.modal--sm {
    width: 100%;
    max-width: 400px;
  }
  
  &.modal--md {
    width: 100%;
    max-width: 500px;
  }
  
  &.modal--lg {
    width: 100%;
    max-width: 700px;
  }
  
  &.modal--xl {
    width: 100%;
    max-width: 900px;
  }
  
  &.modal--fullscreen {
    width: 100vw;
    height: 100vh;
    max-width: none;
    max-height: none;
    border-radius: 0;
    margin: 0;
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 0;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 1.5rem;
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.modal-close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: none;
  border: none;
  border-radius: 0.375rem;
  color: #718096;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background: #f7fafc;
    color: #2d3748;
  }
  
  &:focus {
    outline: 2px solid #4299e1;
    outline-offset: 2px;
  }
}

.modal-body {
  flex: 1;
  padding: 0 1.5rem;
  overflow-y: auto;
  
  &.modal-body--no-padding {
    padding: 0;
  }
}

.modal-footer {
  padding: 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid #e2e8f0;
  margin-top: 1.5rem;
}

.modal-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

// 动画
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease-in-out;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-from .modal-container,
.modal-leave-to .modal-container {
  transform: scale(0.9) translateY(-20px);
}

// 图标
.icon-close::before {
  content: '✕';
  font-size: 1rem;
}

// 响应式
@media (max-width: 640px) {
  .modal-overlay {
    padding: 0;
  }
  
  .modal-container {
    width: 100vw;
    height: 100vh;
    max-width: none;
    max-height: none;
    border-radius: 0;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .modal-actions {
    flex-direction: column-reverse;
    
    .btn {
      width: 100%;
    }
  }
}
</style>
