import{a as q,r as S,c as V,i as o,h as n,f as O,v as K,j as m,A as H,s as G,t as _,T as L,w as j,F as x,k as $,p as B,m as c,B as U,_ as J,q as z,l as P}from"./index-Dz-_Smvi.js";import{d as Q}from"./AppLayout-DAG3tKFc.js";/* empty css                                                                            */const W={class:"search-input-container"},X=["placeholder","disabled"],Y=["disabled"],Z={key:0},ee={key:0,class:"suggestions-dropdown"},te={class:"suggestions-list"},le=["onClick","onMouseenter"],se=["innerHTML"],ae={key:1,class:"suggestion-category"},ne={key:0,class:"recent-searches"},oe={class:"recent-list"},ce=["onClick"],ie={class:"recent-text"},re=["onClick"],ue=q({__name:"SearchBox",props:{modelValue:{},placeholder:{default:"请输入搜索关键词..."},disabled:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},showSearchButton:{type:Boolean,default:!1},searchButtonText:{},suggestions:{default:()=>[]},showSuggestions:{type:Boolean,default:!0},showRecent:{type:Boolean,default:!0},maxRecentItems:{default:5},allowEmpty:{type:Boolean,default:!1},debounceDelay:{default:300}},emits:["update:modelValue","search","clear","focus","blur","suggestion-click"],setup(T,{emit:A}){const p=T,b=A,y=S(),u=S(!1),k=S(-1),d=S([]),f=V({get:()=>p.modelValue,set:t=>b("update:modelValue",t)}),v=V(()=>{if(!f.value||!p.suggestions.length)return[];const t=f.value.toLowerCase();return p.suggestions.filter(i=>i.text.toLowerCase().includes(t)).slice(0,10)}),E=(t,i)=>t.value||t.text||i.toString(),R=t=>{if(!f.value)return t;const i=f.value.toLowerCase(),h=new RegExp(`(${i})`,"gi");return t.replace(h,"<mark>$1</mark>")},M=t=>{u.value=!0,k.value=-1,b("focus",t)},I=t=>{setTimeout(()=>{u.value=!1,k.value=-1},200),b("blur",t)},F=Q(()=>{k.value=-1},p.debounceDelay),D=t=>{switch(t.key){case"Enter":t.preventDefault(),k.value>=0&&v.value.length>0?e(v.value[k.value]):a();break;case"ArrowDown":t.preventDefault(),v.value.length>0&&(k.value=Math.min(k.value+1,v.value.length-1));break;case"ArrowUp":t.preventDefault(),v.value.length>0&&(k.value=Math.max(k.value-1,-1));break;case"Escape":y.value?.blur();break}},a=()=>{const t=f.value.trim();(t||p.allowEmpty)&&(b("search",t),r(t),y.value?.blur())},s=()=>{f.value="",b("clear"),y.value?.focus()},e=t=>{const i=t.value||t.text;f.value=i,b("suggestion-click",t),b("search",i),r(i),y.value?.blur()},l=t=>{f.value=t,b("search",t),y.value?.blur()},r=t=>{if(!t||!p.showRecent)return;const i=d.value.indexOf(t);i>-1&&d.value.splice(i,1),d.value.unshift(t),d.value.length>p.maxRecentItems&&(d.value=d.value.slice(0,p.maxRecentItems)),localStorage.setItem("search-recent",JSON.stringify(d.value))},g=t=>{const i=d.value.indexOf(t);i>-1&&(d.value.splice(i,1),localStorage.setItem("search-recent",JSON.stringify(d.value)))},C=()=>{d.value=[],localStorage.removeItem("search-recent")};return(()=>{try{const t=localStorage.getItem("search-recent");t&&(d.value=JSON.parse(t))}catch(t){console.warn("Failed to load recent searches:",t)}})(),(t,i)=>(c(),o("div",{class:B(["search-box",{"search-box--focused":u.value}])},[n("div",W,[i[4]||(i[4]=n("i",{class:"search-icon icon-search"},null,-1)),K(n("input",{ref_key:"inputRef",ref:y,"onUpdate:modelValue":i[0]||(i[0]=h=>f.value=h),type:"text",class:"search-input",placeholder:t.placeholder,disabled:t.disabled,onFocus:M,onBlur:I,onKeydown:D,onInput:i[1]||(i[1]=(...h)=>G(F)&&G(F)(...h))},null,40,X),[[H,f.value]]),f.value&&t.clearable?(c(),o("button",{key:0,class:"clear-button",onClick:s,"aria-label":"清除搜索"},i[2]||(i[2]=[n("i",{class:"icon-close"},null,-1)]))):m("",!0),t.showSearchButton?(c(),o("button",{key:1,class:"search-button",disabled:t.disabled||!f.value&&!t.allowEmpty,onClick:a,"aria-label":"搜索"},[i[3]||(i[3]=n("i",{class:"icon-search"},null,-1)),t.searchButtonText?(c(),o("span",Z,_(t.searchButtonText),1)):m("",!0)],8,Y)):m("",!0)]),O(L,{name:"suggestions"},{default:j(()=>[t.showSuggestions&&v.value.length>0?(c(),o("div",ee,[n("ul",te,[(c(!0),o(x,null,$(v.value,(h,w)=>(c(),o("li",{key:E(h,w),class:B(["suggestion-item",{highlighted:w===k.value}]),onClick:N=>e(h),onMouseenter:N=>k.value=w},[h.icon?(c(),o("i",{key:0,class:B([h.icon,"suggestion-icon"])},null,2)):m("",!0),n("span",{class:"suggestion-text",innerHTML:R(h.text)},null,8,se),h.category?(c(),o("span",ae,_(h.category),1)):m("",!0)],42,le))),128))])])):m("",!0)]),_:1}),O(L,{name:"recent"},{default:j(()=>[t.showRecent&&d.value.length>0&&!f.value&&u.value?(c(),o("div",ne,[n("div",{class:"recent-header"},[i[5]||(i[5]=n("span",{class:"recent-title"},"最近搜索",-1)),n("button",{class:"clear-recent-button",onClick:C}," 清除 ")]),n("ul",oe,[(c(!0),o(x,null,$(d.value,(h,w)=>(c(),o("li",{key:w,class:"recent-item",onClick:N=>l(h)},[i[7]||(i[7]=n("i",{class:"icon-history"},null,-1)),n("span",ie,_(h),1),n("button",{class:"remove-recent-button",onClick:U(N=>g(h),["stop"]),"aria-label":"删除"},i[6]||(i[6]=[n("i",{class:"icon-close"},null,-1)]),8,re)],8,ce))),128))])])):m("",!0)]),_:1})],2))}}),Ge=J(ue,[["__scopeId","data-v-5e1d7d81"]]),de={class:"filter-header"},he={class:"filter-actions"},ve=["aria-label"],fe={key:0,class:"filter-content"},pe={class:"filter-group-title"},ye={key:0,class:"filter-options"},me=["name","value","checked","onChange"],ke={class:"option-label"},be={key:0,class:"option-count"},_e={key:1,class:"filter-options"},ge=["value","checked","onChange"],Ce={class:"option-label"},xe={key:0,class:"option-count"},$e={key:2,class:"filter-range"},we={class:"range-inputs"},Se=["placeholder","value","onInput"],Ve=["placeholder","value","onInput"],Be={key:3,class:"filter-select"},Re=["value","onChange"],Ie={value:""},Fe=["value"],Te={key:0},Ae={key:0,class:"active-filters"},Ee={class:"filter-tags"},Me=["onClick","aria-label"],De=q({__name:"FilterPanel",props:{filterGroups:{},modelValue:{},collapsed:{type:Boolean,default:!1}},emits:["update:modelValue","change","clear"],setup(T,{emit:A}){const p=T,b=A,y=S(p.collapsed),u=V({get:()=>p.modelValue,set:a=>{b("update:modelValue",a),b("change",a)}}),k=V(()=>Object.values(u.value).some(a=>Array.isArray(a)?a.length>0:typeof a=="object"&&a!==null?Object.values(a).some(s=>s!=null&&s!==""):a!=null&&a!=="")),d=V(()=>{const a=[];return p.filterGroups.forEach(s=>{const e=u.value[s.key];if(s.type==="radio"||s.type==="select"){if(e){const l=s.options?.find(r=>r.value===e);l&&a.push({key:`${s.key}-${e}`,groupKey:s.key,label:l.label,value:e})}}else if(s.type==="checkbox")Array.isArray(e)&&e.length>0&&e.forEach(l=>{const r=s.options?.find(g=>g.value===l);r&&a.push({key:`${s.key}-${l}`,groupKey:s.key,label:r.label,value:l})});else if(s.type==="range"&&e&&(e.min!==void 0||e.max!==void 0)){const l=[];e.min!==void 0&&e.min!==""&&l.push(`≥${e.min}`),e.max!==void 0&&e.max!==""&&l.push(`≤${e.max}`),l.length>0&&a.push({key:`${s.key}-range`,groupKey:s.key,label:`${s.title}: ${l.join(", ")}`,value:e})}}),a}),f=()=>{y.value=!y.value},v=(a,s)=>{const e={...u.value};s===""||s===null?delete e[a]:e[a]=s,u.value=e},E=(a,s,e)=>{const l=e.target,r=u.value[a]||[];let g;l.checked?g=[...r,s]:g=r.filter(C=>C!==s),v(a,g.length>0?g:null)},R=(a,s,e)=>{const l=e.target,r=l.value===""?void 0:Number(l.value),C={...u.value[a]||{},[s]:r};C.min===void 0&&C.max===void 0?v(a,null):v(a,C)},M=(a,s)=>{const e=u.value[a];return Array.isArray(e)?e.includes(s):!1},I=(a,s)=>{const e=u.value[a];return e&&e[s]!==void 0?e[s].toString():""},F=(a,s)=>{const e=p.filterGroups.find(l=>l.key===a);if(e)if(e.type==="checkbox"){const r=(u.value[a]||[]).filter(g=>g!==s);v(a,r.length>0?r:null)}else e.type,v(a,null)},D=()=>{u.value={},b("clear")};return z(()=>p.collapsed,a=>{y.value=a}),(a,s)=>(c(),o("div",{class:B(["filter-panel",{"filter-panel--collapsed":y.value}])},[n("div",de,[s[0]||(s[0]=n("h3",{class:"filter-title"},"筛选条件",-1)),n("div",he,[k.value?(c(),o("button",{key:0,class:"clear-filters-button",onClick:D}," 清除全部 ")):m("",!0),n("button",{class:"collapse-button",onClick:f,"aria-label":y.value?"展开筛选":"收起筛选"},[n("i",{class:B(y.value?"icon-expand":"icon-collapse")},null,2)],8,ve)])]),O(L,{name:"filter-content"},{default:j(()=>[y.value?m("",!0):(c(),o("div",fe,[(c(!0),o(x,null,$(a.filterGroups,e=>(c(),o("div",{key:e.key,class:"filter-group"},[n("h4",pe,_(e.title),1),e.type==="radio"?(c(),o("div",ye,[(c(!0),o(x,null,$(e.options,l=>(c(),o("label",{key:l.value,class:"filter-option filter-option--radio"},[n("input",{type:"radio",name:e.key,value:l.value,checked:u.value[e.key]===l.value,onChange:r=>v(e.key,l.value)},null,40,me),n("span",ke,_(l.label),1),l.count!==void 0?(c(),o("span",be," ("+_(l.count)+") ",1)):m("",!0)]))),128))])):e.type==="checkbox"?(c(),o("div",_e,[(c(!0),o(x,null,$(e.options,l=>(c(),o("label",{key:l.value,class:"filter-option filter-option--checkbox"},[n("input",{type:"checkbox",value:l.value,checked:M(e.key,l.value),onChange:r=>E(e.key,l.value,r)},null,40,ge),n("span",Ce,_(l.label),1),l.count!==void 0?(c(),o("span",xe," ("+_(l.count)+") ",1)):m("",!0)]))),128))])):e.type==="range"?(c(),o("div",$e,[n("div",we,[n("input",{type:"number",placeholder:e.minPlaceholder||"最小值",value:I(e.key,"min"),onInput:l=>R(e.key,"min",l),class:"range-input"},null,40,Se),s[1]||(s[1]=n("span",{class:"range-separator"},"-",-1)),n("input",{type:"number",placeholder:e.maxPlaceholder||"最大值",value:I(e.key,"max"),onInput:l=>R(e.key,"max",l),class:"range-input"},null,40,Ve)])])):e.type==="select"?(c(),o("div",Be,[n("select",{value:u.value[e.key]||"",onChange:l=>v(e.key,l.target.value),class:"select-input"},[n("option",Ie,_(e.placeholder||"请选择"),1),(c(!0),o(x,null,$(e.options,l=>(c(),o("option",{key:l.value,value:l.value},[P(_(l.label)+" ",1),l.count!==void 0?(c(),o("span",Te,"("+_(l.count)+")",1)):m("",!0)],8,Fe))),128))],40,Re)])):m("",!0)]))),128))]))]),_:1}),d.value.length>0?(c(),o("div",Ae,[s[3]||(s[3]=n("div",{class:"active-filters-title"},"已选筛选：",-1)),n("div",Ee,[(c(!0),o(x,null,$(d.value,e=>(c(),o("span",{key:e.key,class:"filter-tag"},[P(_(e.label)+" ",1),n("button",{class:"remove-tag-button",onClick:l=>F(e.groupKey,e.value),"aria-label":`移除 ${e.label} 筛选`},s[2]||(s[2]=[n("i",{class:"icon-close"},null,-1)]),8,Me)]))),128))])])):m("",!0)],2))}}),Pe=J(De,[["__scopeId","data-v-7e54e402"]]);export{Pe as F,Ge as S};
