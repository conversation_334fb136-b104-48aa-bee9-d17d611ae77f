import{a as N,r as d,y as W,o as A,n as j,J,i as l,G as R,h as u,C as K,g as m,m as s,_ as V,c as D,e as Q,w as Y,p as P,j as E,l as z,t as $,b as Z,v as X,f as S,s as x,H as ee,K as te,M as ne}from"./index-Dz-_Smvi.js";/* empty css                                                                            */const oe={class:"micro-interactions"},ae=["width","height"],ie={key:5},se=N({__name:"MicroInteractions",props:{type:{},intensity:{default:1},color:{default:"#ff6b35"},duration:{default:.3}},setup(I){K(e=>({"04d5ff2e":e.color}));const a=I,o=d(),c=d(),i=d(),p=d(),g=d(),k=d(),b=d(),w=W({width:200,height:200}),M=d([]),n=d(),f=e=>{if(!o.value)return;const t=o.value.getBoundingClientRect(),_=t.left+t.width/2,C=t.top+t.height/2,v=(e.clientX-_)*a.intensity*.3,B=(e.clientY-C)*a.intensity*.3;m.to(o.value,{x:v,y:B,duration:a.duration,ease:"power2.out"})},r=()=>{o.value&&m.to(o.value,{x:0,y:0,duration:a.duration*1.5,ease:"elastic.out(1, 0.3)"})},h=e=>{if(!i.value||!c.value)return;const t=c.value.getBoundingClientRect(),_=e.clientX-t.left,C=e.clientY-t.top,v=15*a.intensity,B=[];for(let q=0;q<v;q++)B.push({x:_,y:C,vx:(Math.random()-.5)*10*a.intensity,vy:(Math.random()-.5)*10*a.intensity,life:1,decay:.02,size:Math.random()*4+2});M.value.push(...B),y()},y=()=>{if(!i.value)return;const e=i.value.getContext("2d");e&&(e.clearRect(0,0,w.width,w.height),M.value=M.value.filter(t=>(t.x+=t.vx,t.y+=t.vy,t.life-=t.decay,t.vx*=.98,t.vy*=.98,t.life>0?(e.globalAlpha=t.life,e.fillStyle=a.color,e.beginPath(),e.arc(t.x,t.y,t.size,0,Math.PI*2),e.fill(),!0):!1)),M.value.length>0&&(n.value=requestAnimationFrame(y)))},O=e=>{if(!p.value)return;const t=p.value.getBoundingClientRect(),_=e.clientX-t.left,C=e.clientY-t.top,v=document.createElement("div");v.className="ripple",v.style.left=`${_}px`,v.style.top=`${C}px`,v.style.backgroundColor=a.color,p.value.appendChild(v),m.fromTo(v,{scale:0,opacity:.6},{scale:4*a.intensity,opacity:0,duration:.6,ease:"power2.out",onComplete:()=>{v.remove()}})},F=e=>{if(!g.value||!k.value)return;const t=g.value.getBoundingClientRect(),_=e.clientX-t.left,C=e.clientY-t.top;m.to(k.value,{"--glow-x":`${_}px`,"--glow-y":`${C}px`,opacity:.3*a.intensity,duration:.1,ease:"none"})},G=()=>{k.value&&m.to(k.value,{opacity:0,duration:a.duration,ease:"power2.out"})},H=e=>{if(!b.value)return;const t=b.value.getBoundingClientRect(),_=t.left+t.width/2,C=t.top+t.height/2,v=(e.clientY-C)/t.height*-20*a.intensity,B=(e.clientX-_)/t.width*20*a.intensity;m.to(b.value,{rotationX:v,rotationY:B,duration:a.duration,ease:"power2.out",transformPerspective:1e3})},U=()=>{b.value&&m.to(b.value,{rotationX:0,rotationY:0,duration:a.duration*1.5,ease:"elastic.out(1, 0.3)"})},T=()=>{if(!c.value||!i.value)return;const e=c.value.getBoundingClientRect();w.width=e.width,w.height=e.height};return A(async()=>{await j(),a.type==="particles"&&(T(),window.addEventListener("resize",T))}),J(()=>{n.value&&cancelAnimationFrame(n.value),a.type==="particles"&&window.removeEventListener("resize",T)}),(e,t)=>(s(),l("div",oe,[e.type==="magnetic"?(s(),l("div",{key:0,ref_key:"magneticRef",ref:o,class:"magnetic-element",onMousemove:f,onMouseleave:r},[R(e.$slots,"default",{},void 0,!0)],544)):e.type==="particles"?(s(),l("div",{key:1,ref_key:"particlesRef",ref:c,class:"particles-element",onClick:h},[R(e.$slots,"default",{},void 0,!0),u("canvas",{ref_key:"particlesCanvas",ref:i,class:"particles-canvas",width:w.width,height:w.height},null,8,ae)],512)):e.type==="ripple"?(s(),l("div",{key:2,ref_key:"rippleRef",ref:p,class:"ripple-element",onClick:O},[R(e.$slots,"default",{},void 0,!0)],512)):e.type==="glow"?(s(),l("div",{key:3,ref_key:"glowRef",ref:g,class:"glow-element",onMousemove:F,onMouseleave:G},[R(e.$slots,"default",{},void 0,!0),u("div",{class:"glow-overlay",ref_key:"glowOverlay",ref:k},null,512)],544)):e.type==="tilt"?(s(),l("div",{key:4,ref_key:"tiltRef",ref:b,class:"tilt-element",onMousemove:H,onMouseleave:U},[R(e.$slots,"default",{},void 0,!0)],544)):(s(),l("div",ie,[R(e.$slots,"default",{},void 0,!0)]))]))}}),le=V(se,[["__scopeId","data-v-57fc0484"]]),re=["disabled"],ce={key:0,class:"btn-loading"},de={class:"btn-text"},ue=N({__name:"EnhancedButton",props:{variant:{default:"primary"},size:{default:"md"},text:{},disabled:{type:Boolean},loading:{type:Boolean},icon:{},iconPosition:{default:"left"},interactionType:{default:"ripple"},intensity:{default:1},color:{default:"#ff6b35"},animateOnMount:{type:Boolean,default:!1},hoverAnimation:{type:Boolean,default:!0},rounded:{type:Boolean,default:!1},fullWidth:{type:Boolean,default:!1},gradient:{type:Boolean,default:!1}},emits:["click"],setup(I,{emit:a}){const o=I,c=a,i=d(),p=d(),g=D(()=>["enhanced-btn",`enhanced-btn--${o.variant}`,`enhanced-btn--${o.size}`,{"enhanced-btn--disabled":o.disabled,"enhanced-btn--loading":o.loading,"enhanced-btn--rounded":o.rounded,"enhanced-btn--full-width":o.fullWidth,"enhanced-btn--gradient":o.gradient,"enhanced-btn--with-icon":o.icon,"enhanced-btn--icon-right":o.icon&&o.iconPosition==="right"}]),k=n=>{o.disabled||o.loading||(b(n),c("click",n))},b=n=>{if(!i.value||!p.value)return;const f=i.value.getBoundingClientRect(),r=n.clientX-f.left,h=n.clientY-f.top,y=document.createElement("div");y.className="btn-ripple",y.style.left=`${r}px`,y.style.top=`${h}px`,p.value.appendChild(y),m.fromTo(y,{scale:0,opacity:.6},{scale:4,opacity:0,duration:.6,ease:"power2.out",onComplete:()=>{y.remove()}})},w=()=>{!o.animateOnMount||!i.value||m.fromTo(i.value,{scale:.8,opacity:0},{scale:1,opacity:1,duration:.5,ease:"back.out(1.7)"})},M=()=>{if(!o.hoverAnimation||!i.value)return;const n=i.value;n.addEventListener("mouseenter",()=>{m.to(n,{scale:1.05,duration:.3,ease:"power2.out"})}),n.addEventListener("mouseleave",()=>{m.to(n,{scale:1,duration:.3,ease:"power2.out"})})};return A(()=>{w(),M()}),(n,f)=>(s(),Q(le,{type:n.interactionType,intensity:n.intensity,color:n.color},{default:Y(()=>[u("button",{class:P(g.value),disabled:n.disabled||n.loading,onClick:k,ref_key:"buttonRef",ref:i},[n.loading?(s(),l("div",ce,f[0]||(f[0]=[u("div",{class:"loading-spinner"},null,-1)]))):E("",!0),u("div",{class:P(["btn-content",{"btn-content--loading":n.loading}])},[n.icon&&n.iconPosition==="left"?(s(),l("i",{key:0,class:P([n.icon,"btn-icon btn-icon--left"])},null,2)):E("",!0),u("span",de,[R(n.$slots,"default",{},()=>[z($(n.text),1)],!0)]),n.icon&&n.iconPosition==="right"?(s(),l("i",{key:1,class:P([n.icon,"btn-icon btn-icon--right"])},null,2)):E("",!0)],2),u("div",{class:"btn-ripple-bg",ref_key:"rippleBg",ref:p},null,512),f[1]||(f[1]=u("div",{class:"btn-hover-bg"},null,-1))],10,re)]),_:3},8,["type","intensity","color"]))}}),L=V(ue,[["__scopeId","data-v-f9913b1c"]]),ve={class:"error-page"},pe={class:"container"},fe={class:"error-content"},ye={class:"error-icon"},he={class:"error-info"},me={class:"error-title"},ge={class:"error-message"},be={key:0,class:"error-details"},we={class:"error-actions"},ke={class:"error-contact"},_e={key:0,class:"error-id"},Ce=N({__name:"ErrorPage",setup(I){const a=te(),o=Z(),c=d("系统出现错误"),i=d("抱歉，系统遇到了一个意外错误。我们正在努力修复这个问题。"),p=d(""),g=d(""),k=D(()=>o.query.type||"unknown"),b=()=>{const f=k.value,r=o.query.message,h=o.query.details,y=o.query.id;switch(f){case"network":c.value="网络连接错误",i.value="无法连接到服务器，请检查您的网络连接。";break;case"timeout":c.value="请求超时",i.value="服务器响应超时，请稍后重试。";break;case"server":c.value="服务器错误",i.value="服务器内部错误，我们正在处理这个问题。";break;case"permission":c.value="权限不足",i.value="您没有权限访问此资源。";break;case"validation":c.value="数据验证错误",i.value="提交的数据格式不正确，请检查后重试。";break;default:c.value="系统出现错误",i.value=r||"抱歉，系统遇到了一个意外错误。"}h&&(p.value=h),y?g.value=y:g.value=Math.random().toString(36).substr(2,9).toUpperCase()},w=()=>{a.push("/")},M=()=>{window.history.length>1?a.go(-1):a.push("/")},n=()=>{window.location.reload()};return A(()=>{b()}),(f,r)=>{const h=ne("scroll-animation");return s(),l("div",ve,[u("div",pe,[u("div",fe,[X((s(),l("div",ye,r[0]||(r[0]=[u("i",{class:"icon-alert-triangle"},null,-1)]))),[[h,"scaleIn"]]),X((s(),l("div",he,[u("h1",me,$(c.value),1),u("p",ge,$(i.value),1),p.value?(s(),l("p",be,$(p.value),1)):E("",!0)])),[[h,{animation:"slideUp",delay:.2}]]),X((s(),l("div",we,[S(x(L),{variant:"primary",size:"lg",onClick:w,"interaction-type":"magnetic",intensity:.8},{default:Y(()=>r[1]||(r[1]=[z(" 返回首页 ",-1)])),_:1,__:[1]}),S(x(L),{variant:"outline",size:"lg",onClick:M,"interaction-type":"ripple"},{default:Y(()=>r[2]||(r[2]=[z(" 返回上页 ",-1)])),_:1,__:[2]}),S(x(L),{variant:"ghost",size:"lg",onClick:n,"interaction-type":"glow",color:"#ff6b35"},{default:Y(()=>r[3]||(r[3]=[z(" 刷新页面 ",-1)])),_:1,__:[3]})])),[[h,{animation:"fadeIn",delay:.4}]]),X((s(),l("div",ke,r[4]||(r[4]=[ee('<p data-v-59e6d4a6>如果问题持续存在，请联系我们：</p><div class="contact-info" data-v-59e6d4a6><a href="tel:************" class="contact-link" data-v-59e6d4a6><i class="icon-phone" data-v-59e6d4a6></i> ************ </a><a href="mailto:<EMAIL>" class="contact-link" data-v-59e6d4a6><i class="icon-mail" data-v-59e6d4a6></i> <EMAIL> </a></div>',2)]))),[[h,{animation:"fadeIn",delay:.6}]]),g.value?(s(),l("div",_e,[u("small",null,"错误ID: "+$(g.value),1)])):E("",!0)])])])}}}),Be=V(Ce,[["__scopeId","data-v-59e6d4a6"]]);export{Be as default};
