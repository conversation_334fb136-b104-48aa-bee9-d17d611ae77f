<template>
  <section class="hero-section" ref="heroRef">
    <!-- 背景视频/图片 -->
    <div class="hero-background">
      <div class="background-overlay"></div>
      <img
        src="/images/hero-bg.svg"
        alt="包装解决方案背景"
        class="background-image"
        @error="handleImageError"
      />
    </div>

    <!-- 主要内容 -->
    <div class="hero-content">
      <div class="container">
        <div class="hero-text" ref="heroTextRef">
          <h1 class="hero-title">
            <span class="title-line" ref="titleLine1">专业包装</span>
            <span class="title-line" ref="titleLine2">一站式解决方案</span>
          </h1>
          
          <p class="hero-subtitle" ref="subtitleRef">
            从创意设计到生产制造，为您提供全方位的包装服务
          </p>
          
          <div class="hero-stats" ref="statsRef">
            <div class="stat-item" v-for="stat in statistics" :key="stat.label">
              <div class="stat-number" :ref="el => setStatRef(el, stat.key)">
                {{ animatedStats[stat.key] }}{{ stat.suffix }}
              </div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
          
          <div class="hero-actions" ref="actionsRef">
            <button class="btn btn--primary btn--lg hero-cta" @click="navigateToServices">
              了解我们的服务
              <i class="icon-arrow-right"></i>
            </button>
            <button class="btn btn--secondary btn--lg" @click="navigateToContact">
              立即咨询
            </button>
          </div>
        </div>
        
        <!-- 特色服务快速入口 -->
        <div class="hero-features" ref="featuresRef">
          <div 
            class="feature-card" 
            v-for="(feature, index) in features" 
            :key="feature.id"
            :ref="el => setFeatureRef(el, index)"
            @click="navigateToService(feature.category)"
          >
            <div class="feature-icon">
              <i :class="feature.icon"></i>
            </div>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 滚动指示器 -->
    <div class="scroll-indicator" ref="scrollIndicatorRef" @click="scrollToNext">
      <div class="scroll-text">向下滚动</div>
      <div class="scroll-arrow">
        <i class="icon-arrow-down"></i>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { useNavigation } from '@/composables'
import type { Statistics } from '@/types'

// 注册 GSAP 插件
gsap.registerPlugin(ScrollTrigger)

const { navigateToServices, navigateToContact, navigateToServices: navigateToService } = useNavigation()

// 模板引用
const heroRef = ref<HTMLElement>()
const heroTextRef = ref<HTMLElement>()
const titleLine1 = ref<HTMLElement>()
const titleLine2 = ref<HTMLElement>()
const subtitleRef = ref<HTMLElement>()
const statsRef = ref<HTMLElement>()
const actionsRef = ref<HTMLElement>()
const featuresRef = ref<HTMLElement>()
const scrollIndicatorRef = ref<HTMLElement>()

// 动态引用数组
const statRefs = ref<Record<string, HTMLElement>>({})
const featureRefs = ref<HTMLElement[]>([])

// 数据
const statistics = ref([
  { key: 'projects', label: '完成项目', value: 500, suffix: '+' },
  { key: 'clients', label: '满意客户', value: 200, suffix: '+' },
  { key: 'years', label: '行业经验', value: 10, suffix: '年' },
  { key: 'team', label: '专业团队', value: 50, suffix: '人' }
])

const features = ref([
  {
    id: 'design',
    title: '创意设计',
    description: '专业的包装设计团队，为您打造独特的品牌形象',
    icon: 'icon-design',
    category: '包装设计'
  },
  {
    id: 'production',
    title: '生产制造',
    description: '先进的生产设备，严格的质量控制，确保产品品质',
    icon: 'icon-production',
    category: '包装生产'
  },
  {
    id: 'consulting',
    title: '专业咨询',
    description: '资深行业专家，为您提供专业的包装解决方案',
    icon: 'icon-consulting',
    category: '包装咨询'
  },
  {
    id: 'logistics',
    title: '物流配送',
    description: '完善的物流体系，确保产品安全快速到达',
    icon: 'icon-logistics',
    category: '包装物流'
  }
])

// 动画数据
const animatedStats = reactive<Record<string, number>>({
  projects: 0,
  clients: 0,
  years: 0,
  team: 0
})

// 方法
const setStatRef = (el: HTMLElement | null, key: string) => {
  if (el) {
    statRefs.value[key] = el
  }
}

const setFeatureRef = (el: HTMLElement | null, index: number) => {
  if (el) {
    featureRefs.value[index] = el
  }
}

const handleImageError = () => {
  // 如果图片加载失败，使用渐变背景
  const bgElement = heroRef.value?.querySelector('.hero-background') as HTMLElement
  if (bgElement) {
    bgElement.style.background = 'linear-gradient(135deg, #1a365d 0%, #2d3748 100%)'
  }
}

const scrollToNext = () => {
  const nextSection = heroRef.value?.nextElementSibling as HTMLElement
  if (nextSection) {
    nextSection.scrollIntoView({ behavior: 'smooth' })
  }
}

const animateStats = () => {
  statistics.value.forEach(stat => {
    gsap.to(animatedStats, {
      [stat.key]: stat.value,
      duration: 2,
      ease: 'power2.out',
      scrollTrigger: {
        trigger: statsRef.value,
        start: 'top 80%',
        once: true
      }
    })
  })
}

const initAnimations = async () => {
  await nextTick()
  
  // 创建主时间线
  const tl = gsap.timeline()
  
  // 标题动画
  tl.from([titleLine1.value, titleLine2.value], {
    y: 100,
    opacity: 0,
    duration: 1,
    stagger: 0.2,
    ease: 'power3.out'
  })
  
  // 副标题动画
  tl.from(subtitleRef.value, {
    y: 50,
    opacity: 0,
    duration: 0.8,
    ease: 'power2.out'
  }, '-=0.5')
  
  // 按钮动画
  tl.from(actionsRef.value?.children || [], {
    y: 30,
    opacity: 0,
    duration: 0.6,
    stagger: 0.1,
    ease: 'power2.out'
  }, '-=0.3')
  
  // 特色服务卡片动画
  tl.from(featureRefs.value, {
    y: 60,
    opacity: 0,
    duration: 0.8,
    stagger: 0.15,
    ease: 'power2.out'
  }, '-=0.4')
  
  // 滚动指示器动画
  tl.from(scrollIndicatorRef.value, {
    opacity: 0,
    duration: 0.5,
    ease: 'power2.out'
  }, '-=0.2')
  
  // 滚动指示器持续动画
  gsap.to(scrollIndicatorRef.value?.querySelector('.scroll-arrow'), {
    y: 10,
    duration: 1.5,
    repeat: -1,
    yoyo: true,
    ease: 'power2.inOut'
  })
  
  // 视差效果
  gsap.to('.background-image', {
    yPercent: -50,
    ease: 'none',
    scrollTrigger: {
      trigger: heroRef.value,
      start: 'top bottom',
      end: 'bottom top',
      scrub: true
    }
  })
  
  // 统计数字动画
  animateStats()
}

// 生命周期
onMounted(() => {
  initAnimations()
})

onUnmounted(() => {
  ScrollTrigger.getAll().forEach(trigger => trigger.kill())
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.hero-section {
  position: relative;
  min-height: calc(100vh - 4rem); // 减去头部高度
  display: flex;
  align-items: center;
  overflow: hidden;

  @media (min-width: 1024px) {
    min-height: calc(100vh - 5rem); // 桌面端减去更大的头部高度
  }
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  
  .background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(26, 54, 93, 0.8) 0%,
      rgba(45, 55, 72, 0.6) 100%
    );
    z-index: 1;
  }
  
  .background-image {
    width: 100%;
    height: 120%;
    object-fit: cover;
    object-position: center;
  }
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
  padding: 2rem 0;
}

.hero-text {
  text-align: center;
  color: white;
  margin-bottom: 4rem;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  
  .title-line {
    display: block;
    background: linear-gradient(135deg, #ffffff 0%, #ff6b35 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
  
  @media (max-width: 640px) {
    font-size: 2rem;
  }
}

.hero-subtitle {
  font-size: 1.25rem;
  opacity: 0.9;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  
  @media (max-width: 768px) {
    font-size: 1.125rem;
  }
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin-bottom: 3rem;
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.stat-item {
  text-align: center;
  
  .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ff6b35;
    margin-bottom: 0.5rem;
    
    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }
  
  .stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  
  .btn {
    min-width: 180px;
    
    @media (max-width: 640px) {
      min-width: 150px;
      width: 100%;
      max-width: 280px;
    }
  }
}

.hero-cta {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }
  
  &:hover::before {
    left: 100%;
  }
}

.hero-features {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 640px) {
    grid-template-columns: 1fr;
  }
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 2rem 1.5rem;
  text-align: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 107, 53, 0.5);
  }
}

.feature-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8a65 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  
  i {
    font-size: 1.5rem;
    color: white;
  }
}

.feature-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.feature-description {
  font-size: 0.875rem;
  opacity: 0.8;
  line-height: 1.5;
  margin: 0;
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.3s ease;
  
  &:hover {
    opacity: 1;
  }
  
  .scroll-text {
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  .scroll-arrow {
    font-size: 1.5rem;
  }
}

// 图标
.icon-arrow-right::before { content: '→'; }
.icon-arrow-down::before { content: '↓'; }
.icon-design::before { content: '🎨'; }
.icon-production::before { content: '🏭'; }
.icon-consulting::before { content: '💡'; }
.icon-logistics::before { content: '🚚'; }
</style>
