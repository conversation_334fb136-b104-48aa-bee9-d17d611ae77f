import{d as v,r as a,c as u}from"./index-Dz-_Smvi.js";const x=v("products",()=>{const t=a([]),n=a(null),c=a(!1),s=a(null),o=a({keyword:"",category:"",tags:[],page:1,pageSize:12}),i=a(0),d=u(()=>{const e=new Set(t.value.map(r=>r.category));return Array.from(e)}),p=u(()=>t.value.filter(e=>e.availability)),y=u(()=>Math.ceil(i.value/o.value.pageSize)),l=async e=>{c.value=!0,s.value=null,e&&(o.value={...o.value,...e});try{t.value=[{id:"1",name:"环保包装盒",description:"可回收环保材料制作的包装盒",category:"包装盒",specifications:{material:"可回收纸板",size:"20x15x10cm",weight:"50g"},images:["/images/product1.jpg"],price_range:"¥5-15",availability:!0,tags:["环保","可回收","轻量"]},{id:"2",name:"高端礼品盒",description:"精美的高端礼品包装盒",category:"礼品盒",specifications:{material:"高档纸板+烫金",size:"25x20x8cm",weight:"120g"},images:["/images/product2.jpg"],price_range:"¥20-50",availability:!0,tags:["高端","礼品","烫金"]}],i.value=2}catch(r){s.value="获取产品数据失败",console.error("Error fetching products:",r)}finally{c.value=!1}};return{products:t,currentProduct:n,loading:c,error:s,searchParams:o,totalProducts:i,productCategories:d,availableProducts:p,totalPages:y,fetchProducts:l,getProductById:async e=>{const r=t.value.find(g=>g.id===e);return r?(n.value=r,r):(c.value=!0,c.value=!1,null)},searchProducts:async e=>{await l({keyword:e,page:1})},filterByCategory:async e=>{await l({category:e,page:1})},clearFilters:async()=>{o.value={keyword:"",category:"",tags:[],page:1,pageSize:12},await l()},clearError:()=>{s.value=null}}});export{x as u};
