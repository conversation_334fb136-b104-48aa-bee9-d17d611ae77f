import{a as z,r as p,c as S,q as L,i as o,h as e,j as u,f as N,t as i,F as _,k as m,w as E,C as X,m as a,p as C,n as G,_ as F,g as T,S as Y,b as Z,o as ee,e as M,s as Q,v as P,A as te,l as U,D as R}from"./index-Dz-_Smvi.js";import{u as se,A as ae,f as oe,a as le}from"./AppLayout-DAG3tKFc.js";import{B as ie}from"./Breadcrumb-DX9d_gU2.js";import{M as ne,P as ce}from"./ProductCard-BWOk5Fwa.js";/* empty css                                                                            */import"./contact-sLg3zrn9.js";const re={class:"image-gallery"},ue={class:"gallery-main"},de={class:"main-image-container"},ve=["src","alt"],ge={key:0,class:"image-info"},pe={class:"image-title"},he={key:0,class:"image-description"},_e={key:0,class:"gallery-thumbnails"},me=["onClick"],be=["src","alt"],ke={class:"lightbox-content"},ye=["src","alt"],fe={class:"lightbox-counter"},we={key:2,class:"lightbox-info"},Ce={key:0,class:"lightbox-title"},$e={key:1,class:"lightbox-description"},xe=z({__name:"ImageGallery",props:{images:{},initialIndex:{default:0},showThumbnails:{type:Boolean,default:!0},showArrows:{type:Boolean,default:!0},showInfo:{type:Boolean,default:!0},aspectRatio:{default:"16/9"},thumbnailSize:{default:"80px"}},emits:["change","click"],setup(D,{emit:I}){X(s=>({"257b93a2":s.aspectRatio,"9af468ae":s.thumbnailSize}));const g=D,$=I,r=p(g.initialIndex),h=p(!1),d=p(),c=S(()=>g.images[r.value]||{src:"",alt:""}),t=s=>{s>=0&&s<g.images.length&&s!==r.value&&(r.value=s,$("change",r.value,c.value),A())},x=()=>{const s=r.value<g.images.length-1?r.value+1:0;t(s)},q=()=>{const s=r.value>0?r.value-1:g.images.length-1;t(s)},V=()=>{h.value=!0,$("click",r.value,c.value)},A=async()=>{if(!g.showThumbnails||!d.value)return;await G();const s=d.value.querySelector(".thumbnail-item.active");s&&s.scrollIntoView({behavior:"smooth",block:"nearest",inline:"center"})},y=s=>{if(h.value)switch(s.key){case"ArrowLeft":s.preventDefault(),q();break;case"ArrowRight":s.preventDefault(),x();break}};return L(()=>g.initialIndex,s=>{s>=0&&s<g.images.length&&(r.value=s)}),L(h,s=>{s?document.addEventListener("keydown",y):document.removeEventListener("keydown",y)}),(s,v)=>(a(),o("div",re,[e("div",ue,[e("div",de,[e("img",{src:c.value.src,alt:c.value.alt||`图片 ${r.value+1}`,class:"main-image",onClick:V},null,8,ve),s.showInfo&&c.value.title?(a(),o("div",ge,[e("h3",pe,i(c.value.title),1),c.value.description?(a(),o("p",he,i(c.value.description),1)):u("",!0)])):u("",!0),s.showArrows&&s.images.length>1?(a(),o("button",{key:1,class:"gallery-arrow gallery-arrow--prev",onClick:q,"aria-label":"上一张图片"},v[1]||(v[1]=[e("i",{class:"icon-arrow-left"},null,-1)]))):u("",!0),s.showArrows&&s.images.length>1?(a(),o("button",{key:2,class:"gallery-arrow gallery-arrow--next",onClick:x,"aria-label":"下一张图片"},v[2]||(v[2]=[e("i",{class:"icon-arrow-right"},null,-1)]))):u("",!0)])]),s.showThumbnails&&s.images.length>1?(a(),o("div",_e,[e("div",{class:"thumbnails-container",ref_key:"thumbnailsRef",ref:d},[(a(!0),o(_,null,m(s.images,(b,f)=>(a(),o("button",{key:f,class:C(["thumbnail-item",{active:f===r.value}]),onClick:B=>t(f)},[e("img",{src:b.thumbnail||b.src,alt:b.alt||`缩略图 ${f+1}`,class:"thumbnail-image"},null,8,be)],10,me))),128))],512)])):u("",!0),N(ne,{modelValue:h.value,"onUpdate:modelValue":v[0]||(v[0]=b=>h.value=b),closable:!0,"close-on-overlay":!0,"show-header":!1,"show-footer":!1,fullscreen:"","no-padding":""},{default:E(()=>[e("div",ke,[e("img",{src:c.value.src,alt:c.value.alt||`图片 ${r.value+1}`,class:"lightbox-image"},null,8,ye),s.images.length>1?(a(),o("button",{key:0,class:"lightbox-arrow lightbox-arrow--prev",onClick:q,"aria-label":"上一张图片"},v[3]||(v[3]=[e("i",{class:"icon-arrow-left"},null,-1)]))):u("",!0),s.images.length>1?(a(),o("button",{key:1,class:"lightbox-arrow lightbox-arrow--next",onClick:x,"aria-label":"下一张图片"},v[4]||(v[4]=[e("i",{class:"icon-arrow-right"},null,-1)]))):u("",!0),e("div",fe,i(r.value+1)+" / "+i(s.images.length),1),c.value.title||c.value.description?(a(),o("div",we,[c.value.title?(a(),o("h3",Ce,i(c.value.title),1)):u("",!0),c.value.description?(a(),o("p",$e,i(c.value.description),1)):u("",!0)])):u("",!0)])]),_:1},8,["modelValue"])]))}}),qe=F(xe,[["__scopeId","data-v-a312f33a"]]),Te={class:"container"},Pe={class:"hero-layout"},Ie={class:"product-gallery"},Ve={class:"product-info"},Ae={class:"product-header"},Re={class:"product-meta"},Se={class:"product-category"},Ne={class:"product-badges"},De={key:0,class:"badge badge--new"},Be={key:1,class:"badge badge--hot"},Le={key:2,class:"badge badge--featured"},Me={class:"product-name"},Qe={class:"product-description"},Ue={key:0,class:"product-rating"},ze={class:"rating-stars"},Ee={class:"rating-text"},Ge={class:"product-pricing"},Fe={class:"price-main"},We={key:0,class:"price-original"},je={class:"price-current"},He={key:1,class:"price-unit"},Ke={key:0,class:"price-discount"},Je={class:"product-stock"},Oe={key:0,class:"stock-text"},Xe={key:1,class:"stock-text"},Ye={key:2,class:"stock-text"},Ze={class:"quantity-selector"},et={class:"quantity-controls"},tt=["disabled"],st=["max"],at=["disabled"],ot={class:"product-actions"},lt=["disabled"],it={class:"container"},nt={class:"details-tabs"},ct={class:"tab-nav"},rt=["onClick"],ut={class:"tab-content"},dt={class:"tab-panel"},vt={class:"product-specifications"},gt={class:"spec-table"},pt={class:"spec-label"},ht={class:"spec-value"},_t={key:0,class:"product-features"},mt={class:"features-list"},bt={class:"tab-panel"},kt={class:"reviews-summary"},yt={class:"rating-overview"},ft={class:"rating-score"},wt={class:"rating-stars"},Ct={class:"rating-count"},$t={class:"reviews-list"},xt={class:"review-header"},qt={class:"reviewer-info"},Tt={class:"reviewer-name"},Pt={class:"review-date"},It={class:"review-rating"},Vt={class:"review-content"},At={class:"tab-panel"},Rt={class:"related-products"},St={class:"related-grid"},Nt=z({__name:"ProductDetailPage",setup(D){T.registerPlugin(Y);const I=Z(),{navigateToContact:g,navigateToProducts:$}=se(),r=p(),h=p();S(()=>I.params.id);const d=p(1),c=p("details"),t=p({id:"1",name:"高端礼品包装盒",description:"精美的高端礼品包装盒，适用于各种高档商品包装，提升产品价值。采用优质材料制作，工艺精湛，是您产品包装的理想选择。",category:"包装盒",price:25,originalPrice:30,priceUnit:"/个",image:"/images/product1.svg",rating:4.8,reviewCount:156,stock:500,isNew:!0,featured:!0,discount:17,features:["环保材料","精美印刷","可定制尺寸","快速交货","质量保证","专业设计"],specifications:{材质:"高档纸板",尺寸:"20x15x8cm",颜色:"多色可选",工艺:"烫金印刷",厚度:"3mm",重量:"150g"}}),x=S(()=>[{src:t.value.image||"/images/product1.svg",alt:t.value.name},{src:"/images/product1.svg",alt:`${t.value.name} - 侧面`},{src:"/images/product1.svg",alt:`${t.value.name} - 细节`}]),q=p([{key:"details",label:"产品详情"},{key:"reviews",label:"用户评价"},{key:"related",label:"相关产品"}]),V=p([{id:"1",userName:"张先生",rating:5,content:"包装盒质量很好，印刷精美，客户很满意。",date:"2024-01-15"},{id:"2",userName:"李女士",rating:4,content:"材质不错，尺寸合适，就是价格稍微贵了一点。",date:"2024-01-10"}]),A=p([{id:"5",name:"环保纸质包装盒",category:"包装盒",price:12,image:"/images/product5.svg",rating:4.7,stock:800},{id:"2",name:"食品级包装袋",category:"包装袋",price:.5,image:"/images/product2.svg",rating:4.6,stock:1e3}]),y=k=>oe(k),s=()=>{d.value<(t.value.stock||999)&&d.value++},v=()=>{d.value>1&&d.value--},b=()=>{console.log("Add to cart:",{product:t.value,quantity:d.value})},f=()=>{g()},B=()=>{console.log("Add to wishlist:",t.value)},W=()=>{console.log("Compare product:",t.value)},j=()=>{console.log("Share product:",t.value)},H=k=>{$(k.id)},K=k=>{g()},J=k=>{$(k.id)},O=async()=>{await G(),T.from(r.value?.querySelector(".product-gallery"),{x:-50,opacity:0,duration:1,ease:"power2.out"}),T.from(r.value?.querySelector(".product-info")?.children||[],{y:50,opacity:0,duration:.8,stagger:.1,ease:"power2.out",delay:.3}),T.from(h.value,{y:60,opacity:0,duration:.8,ease:"power2.out",scrollTrigger:{trigger:h.value,start:"top 80%",once:!0}})};return ee(()=>{O()}),(k,n)=>(a(),M(ae,null,{default:E(()=>[N(ie),e("section",{class:"product-detail-hero",ref_key:"heroRef",ref:r},[e("div",Te,[e("div",Pe,[e("div",Ie,[N(Q(qe),{images:x.value,"show-thumbnails":!0,"auto-play":!1},null,8,["images"])]),e("div",Ve,[e("div",Ae,[e("div",Re,[e("span",Se,i(t.value?.category),1),e("div",Ne,[t.value?.isNew?(a(),o("span",De,"新品")):u("",!0),t.value?.isHot?(a(),o("span",Be,"热销")):u("",!0),t.value?.featured?(a(),o("span",Le,"推荐")):u("",!0)])]),e("h1",Me,i(t.value?.name),1),e("p",Qe,i(t.value?.description),1),t.value?.rating?(a(),o("div",Ue,[e("div",ze,[(a(),o(_,null,m(5,l=>e("i",{key:l,class:C(["star",{filled:l<=Math.floor(t.value.rating)}])},null,2)),64))]),e("span",Ee,"("+i(t.value?.rating)+") "+i(t.value?.reviewCount||0)+"条评价",1)])):u("",!0)]),e("div",Ge,[e("div",Fe,[t.value?.originalPrice&&t.value.originalPrice>t.value.price?(a(),o("span",We," ¥"+i(y(t.value.originalPrice)),1)):u("",!0),e("span",je,"¥"+i(y(t.value?.price||0)),1),t.value?.priceUnit?(a(),o("span",He,i(t.value.priceUnit),1)):u("",!0)]),t.value?.discount?(a(),o("div",Ke," 节省 ¥"+i(y((t.value.originalPrice||0)-t.value.price)),1)):u("",!0)]),e("div",Je,[e("div",{class:C(["stock-indicator",{"in-stock":(t.value?.stock||0)>10,"low-stock":(t.value?.stock||0)>0&&(t.value?.stock||0)<=10,"out-of-stock":(t.value?.stock||0)===0}])},[n[1]||(n[1]=e("i",{class:"stock-icon"},null,-1)),(t.value?.stock||0)>10?(a(),o("span",Oe,"现货充足")):(t.value?.stock||0)>0?(a(),o("span",Xe,"库存紧张 ("+i(t.value?.stock)+"件)",1)):(a(),o("span",Ye,"暂时缺货"))],2)]),e("div",Ze,[n[2]||(n[2]=e("label",{class:"quantity-label"},"数量：",-1)),e("div",et,[e("button",{class:"quantity-btn",onClick:v,disabled:d.value<=1}," - ",8,tt),P(e("input",{"onUpdate:modelValue":n[0]||(n[0]=l=>d.value=l),type:"number",class:"quantity-input",min:"1",max:t.value?.stock||999},null,8,st),[[te,d.value,void 0,{number:!0}]]),e("button",{class:"quantity-btn",onClick:s,disabled:d.value>=(t.value?.stock||999)}," + ",8,at)])]),e("div",ot,[e("button",{class:"btn btn--secondary btn--lg",onClick:b,disabled:(t.value?.stock||0)===0},n[3]||(n[3]=[e("i",{class:"icon-cart"},null,-1),U(" 加入购物车 ",-1)]),8,lt),e("button",{class:"btn btn--primary btn--lg",onClick:f},n[4]||(n[4]=[e("i",{class:"icon-quote"},null,-1),U(" 获取报价 ",-1)]))]),e("div",{class:"quick-actions"},[e("button",{class:"quick-action-btn",onClick:B},n[5]||(n[5]=[e("i",{class:"icon-heart"},null,-1),e("span",null,"收藏",-1)])),e("button",{class:"quick-action-btn",onClick:W},n[6]||(n[6]=[e("i",{class:"icon-compare"},null,-1),e("span",null,"对比",-1)])),e("button",{class:"quick-action-btn",onClick:j},n[7]||(n[7]=[e("i",{class:"icon-share"},null,-1),e("span",null,"分享",-1)]))])])])])],512),e("section",{class:"product-details",ref_key:"detailsRef",ref:h},[e("div",it,[e("div",nt,[e("div",ct,[(a(!0),o(_,null,m(q.value,l=>(a(),o("button",{key:l.key,class:C(["tab-btn",{active:c.value===l.key}]),onClick:w=>c.value=l.key},i(l.label),11,rt))),128))]),e("div",ut,[P(e("div",dt,[e("div",vt,[n[8]||(n[8]=e("h3",{class:"spec-title"},"产品规格",-1)),e("div",gt,[(a(!0),o(_,null,m(t.value?.specifications,(l,w)=>(a(),o("div",{key:w,class:"spec-row"},[e("div",pt,i(w),1),e("div",ht,i(l),1)]))),128))])]),t.value?.features?(a(),o("div",_t,[n[10]||(n[10]=e("h3",{class:"features-title"},"产品特点",-1)),e("ul",mt,[(a(!0),o(_,null,m(t.value.features,l=>(a(),o("li",{key:l,class:"feature-item"},[n[9]||(n[9]=e("i",{class:"icon-check"},null,-1)),e("span",null,i(l),1)]))),128))])])):u("",!0)],512),[[R,c.value==="details"]]),P(e("div",bt,[e("div",kt,[e("div",yt,[e("div",ft,i(t.value?.rating||0),1),e("div",wt,[(a(),o(_,null,m(5,l=>e("i",{key:l,class:C(["star",{filled:l<=Math.floor(t.value?.rating||0)}])},null,2)),64))]),e("div",Ct,"基于 "+i(t.value?.reviewCount||0)+" 条评价",1)])]),e("div",$t,[(a(!0),o(_,null,m(V.value,l=>(a(),o("div",{key:l.id,class:"review-item"},[e("div",xt,[e("div",qt,[e("div",Tt,i(l.userName),1),e("div",Pt,i(Q(le)(l.date)),1)]),e("div",It,[(a(),o(_,null,m(5,w=>e("i",{key:w,class:C(["star",{filled:w<=l.rating}])},null,2)),64))])]),e("div",Vt,i(l.content),1)]))),128))])],512),[[R,c.value==="reviews"]]),P(e("div",At,[e("div",Rt,[n[11]||(n[11]=e("h3",{class:"related-title"},"相关产品推荐",-1)),e("div",St,[(a(!0),o(_,null,m(A.value,l=>(a(),M(ce,{key:l.id,product:l,variant:"compact",onClick:H,onQuote:K,onViewDetails:J},null,8,["product"]))),128))])])],512),[[R,c.value==="related"]])])])])],512)]),_:1}))}}),zt=F(Nt,[["__scopeId","data-v-6aa96901"]]);export{zt as default};
