<template>
  <footer class="app-footer">
    <div class="container">
      <!-- 主要内容区域 -->
      <div class="footer-main">
        <div class="footer-grid">
          <!-- 公司信息 -->
          <div class="footer-section company-section">
            <div class="company-info">
              <div class="logo-container">
                <div class="logo">
                  <div class="logo-icon">
                    <img src="/logo.svg" alt="包装解决方案" class="logo-image" />
                  </div>
                  <div class="logo-content">
                    <span class="logo-text">包装解决方案</span>
                    <span class="logo-tagline">专业 · 创新 · 可持续</span>
                  </div>
                </div>
              </div>
              <p class="company-description">
                {{ companyInfo.description }}
              </p>
              <div class="company-highlights">
                <div class="highlight-item">
                  <i class="icon-award"></i>
                  <span>行业领先</span>
                </div>
                <div class="highlight-item">
                  <i class="icon-eco"></i>
                  <span>环保包装</span>
                </div>
                <div class="highlight-item">
                  <i class="icon-innovation"></i>
                  <span>技术创新</span>
                </div>
              </div>
              <div class="social-media">
                <div class="social-title">关注我们</div>
                <div class="social-links">
                  <a
                    v-for="(link, platform) in companyInfo.socialMedia"
                    :key="platform"
                    :href="getSocialMediaUrl(platform, link)"
                    class="social-link"
                    :aria-label="`关注我们的${getSocialMediaName(platform)}`"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <i :class="getSocialMediaIcon(platform)"></i>
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- 快速链接 -->
          <div class="footer-section">
            <h3 class="section-title">快速链接</h3>
            <ul class="link-list">
              <li v-for="item in mainMenuItems" :key="item.id">
                <router-link :to="item.path" class="footer-link">
                  {{ item.label }}
                </router-link>
              </li>
            </ul>
          </div>

          <!-- 服务分类 -->
          <div class="footer-section">
            <h3 class="section-title">我们的服务</h3>
            <ul class="link-list">
              <li v-for="category in serviceCategories" :key="category">
                <router-link :to="`/services/${encodeURIComponent(category)}`" class="footer-link">
                  {{ category }}
                </router-link>
              </li>
            </ul>
          </div>

          <!-- 联系信息 -->
          <div class="footer-section">
            <h3 class="section-title">联系我们</h3>
            <div class="contact-info">
              <div class="contact-item">
                <i class="icon-location"></i>
                <span>{{ companyInfo.address }}</span>
              </div>
              <div class="contact-item">
                <i class="icon-phone"></i>
                <a :href="`tel:${companyInfo.phone}`" class="contact-link">
                  {{ companyInfo.phone }}
                </a>
              </div>
              <div class="contact-item">
                <i class="icon-email"></i>
                <a :href="`mailto:${companyInfo.email}`" class="contact-link">
                  {{ companyInfo.email }}
                </a>
              </div>
              <div class="contact-item">
                <i class="icon-clock"></i>
                <div class="business-hours">
                  <div>{{ companyInfo.businessHours.weekdays }}</div>
                  <div>{{ companyInfo.businessHours.weekends }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部版权信息 -->
      <div class="footer-bottom">
        <div class="copyright">
          <p>&copy; {{ currentYear }} {{ companyInfo.name }}. 保留所有权利。</p>
        </div>
        <div class="legal-links">
          <a href="/privacy" class="legal-link">隐私政策</a>
          <a href="/terms" class="legal-link">服务条款</a>
          <a href="/sitemap" class="legal-link">网站地图</a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useContactStore, useServicesStore } from '@/stores'
import { useNavigation } from '@/composables'

const contactStore = useContactStore()
const servicesStore = useServicesStore()
const { mainMenuItems } = useNavigation()

// 公司信息
const companyInfo = computed(() => contactStore.companyInfo)

// 服务分类
const serviceCategories = computed(() => servicesStore.serviceCategories)

// 当前年份
const currentYear = computed(() => new Date().getFullYear())

// 社交媒体相关方法
const getSocialMediaUrl = (platform: string, link: string): string => {
  const baseUrls: Record<string, string> = {
    wechat: '#', // 微信通常显示二维码
    weibo: 'https://weibo.com/',
    linkedin: 'https://linkedin.com/company/',
    facebook: 'https://facebook.com/',
    twitter: 'https://twitter.com/',
    instagram: 'https://instagram.com/'
  }
  
  return baseUrls[platform] ? `${baseUrls[platform]}${link}` : '#'
}

const getSocialMediaName = (platform: string): string => {
  const names: Record<string, string> = {
    wechat: '微信',
    weibo: '微博',
    linkedin: 'LinkedIn',
    facebook: 'Facebook',
    twitter: 'Twitter',
    instagram: 'Instagram'
  }
  
  return names[platform] || platform
}

const getSocialMediaIcon = (platform: string): string => {
  const icons: Record<string, string> = {
    wechat: 'icon-wechat',
    weibo: 'icon-weibo',
    linkedin: 'icon-linkedin',
    facebook: 'icon-facebook',
    twitter: 'icon-twitter',
    instagram: 'icon-instagram'
  }
  
  return icons[platform] || 'icon-link'
}
</script>

<style lang="scss" scoped>
.app-footer {
  background: linear-gradient(135deg, #1a365d 0%, #2d3748 100%);
  color: white;
  margin-top: auto;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 20% 20%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(255, 107, 53, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }
}

.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
  max-width: 1280px;

  @media (min-width: 768px) {
    padding: 0 1.5rem;
  }

  @media (min-width: 1024px) {
    padding: 0 2rem;
  }
}

.footer-main {
  padding: 4rem 0 2rem;
  position: relative;
  z-index: 1;
}

.footer-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;

  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 1024px) {
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
  }
}

.footer-section {
  .section-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ff6b35;
  }
}

.company-section {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -1rem;
    left: -1rem;
    right: -1rem;
    bottom: -1rem;
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.05) 0%, rgba(26, 54, 93, 0.05) 100%);
    border-radius: 1rem;
    z-index: -1;
  }
}

.company-info {
  .logo-container {
    margin-bottom: 1.5rem;
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
  }

  .logo-icon {
    position: relative;
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #ff6b35 0%, #ff8a65 100%);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  }

  .logo-image {
    width: 1.75rem;
    height: 1.75rem;
    filter: brightness(0) invert(1);
  }

  .logo-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    line-height: 1.2;
  }

  .logo-tagline {
    font-size: 0.75rem;
    color: #ff6b35;
    font-weight: 500;
    letter-spacing: 0.5px;
  }

  .company-description {
    color: #cbd5e0;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
  }
}

.company-highlights {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;

  .highlight-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #cbd5e0;
    font-size: 0.875rem;

    i {
      width: 1.5rem;
      height: 1.5rem;
      background: rgba(255, 107, 53, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ff6b35;
      font-size: 0.75rem;
    }
  }
}

.social-media {
  .social-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #ff6b35;
    margin-bottom: 0.75rem;
  }

  .social-links {
    display: flex;
    gap: 0.75rem;
  }
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.25rem;
  height: 2.25rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    background: linear-gradient(135deg, #ff6b35 0%, #ff8a65 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4);

    &::before {
      left: 100%;
    }
  }

  i {
    font-size: 1rem;
    z-index: 1;
  }
}

.link-list {
  list-style: none;
  margin: 0;
  padding: 0;

  li {
    margin-bottom: 0.5rem;
  }
}

.footer-link {
  color: #cbd5e0;
  text-decoration: none;
  transition: color 0.3s ease-in-out;

  &:hover {
    color: #ff6b35;
  }
}

.contact-info {
  .contact-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 1rem;
    color: #cbd5e0;

    i {
      margin-top: 0.125rem;
      color: #ff6b35;
      font-size: 1rem;
    }
  }

  .contact-link {
    color: #cbd5e0;
    text-decoration: none;
    transition: color 0.3s ease-in-out;

    &:hover {
      color: #ff6b35;
    }
  }

  .business-hours {
    div {
      margin-bottom: 0.25rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.footer-bottom {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 2rem 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  gap: 1rem;
  position: relative;
  z-index: 1;
  background: rgba(0, 0, 0, 0.1);

  @media (min-width: 768px) {
    flex-direction: row;
    gap: 0;
  }
}

.copyright {
  p {
    color: #a0aec0;
    font-size: 0.875rem;
    margin: 0;
  }
}

.legal-links {
  display: flex;
  gap: 1.5rem;
}

.legal-link {
  color: #a0aec0;
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s ease-in-out;

  &:hover {
    color: #ff6b35;
  }
}

// 图标样式（使用字体图标或可以替换为实际的图标库）
.icon-location::before { content: '📍'; }
.icon-phone::before { content: '📞'; }
.icon-email::before { content: '✉️'; }
.icon-clock::before { content: '🕒'; }
.icon-wechat::before { content: '💬'; }
.icon-weibo::before { content: '🌐'; }
.icon-linkedin::before { content: '💼'; }
.icon-facebook::before { content: '📘'; }
.icon-twitter::before { content: '🐦'; }
.icon-instagram::before { content: '📷'; }
.icon-link::before { content: '🔗'; }

// 新增图标
.icon-award::before { content: '🏆'; }
.icon-eco::before { content: '🌱'; }
.icon-innovation::before { content: '💡'; }
</style>
