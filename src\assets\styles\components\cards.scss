// 卡片组件样式系统

@use '../variables' as *;
@use '../mixins' as *;

// ===== 基础卡片样式 =====
.card {
  @include card-base;
  @include card-padding;
  
  // 尺寸变体
  &--sm {
    @include card-padding('sm');
  }
  
  &--md {
    @include card-padding('md');
  }
  
  &--lg {
    @include card-padding('lg');
  }
  
  // 交互变体
  &--hover {
    @include card-hover;
  }
  
  &--clickable {
    cursor: pointer;
    @include card-hover;
    
    &:active {
      transform: translateY(-2px);
    }
  }
  
  // 边框变体
  &--bordered {
    border: 2px solid $gray-200;
  }
  
  &--borderless {
    border: none;
  }
  
  // 阴影变体
  &--shadow-none {
    box-shadow: none;
  }
  
  &--shadow-sm {
    box-shadow: $shadow-sm;
  }
  
  &--shadow-lg {
    box-shadow: $shadow-lg;
  }
  
  &--shadow-xl {
    box-shadow: $shadow-xl;
  }
  
  // 圆角变体
  &--rounded-none {
    border-radius: 0;
  }
  
  &--rounded-sm {
    border-radius: $border-radius-sm;
  }
  
  &--rounded-lg {
    border-radius: $border-radius-2xl;
  }
  
  &--rounded-full {
    border-radius: $border-radius-full;
  }
}

// ===== 卡片头部 =====
.card-header {
  padding: $spacing-4 $spacing-6;
  border-bottom: 1px solid $gray-200;
  background: $gray-50;
  border-radius: $border-radius-xl $border-radius-xl 0 0;
  
  .card-title {
    margin: 0;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $primary-color;
  }
  
  .card-subtitle {
    margin: $spacing-1 0 0 0;
    font-size: $font-size-sm;
    color: $gray-600;
  }
  
  .card-actions {
    margin-left: auto;
  }
  
  &--flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

// ===== 卡片主体 =====
.card-body {
  padding: $spacing-6;
  
  .card-text {
    color: $gray-700;
    line-height: $line-height-relaxed;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .card-title {
    margin: 0 0 $spacing-3 0;
    font-size: $font-size-xl;
    font-weight: $font-weight-semibold;
    color: $primary-color;
  }
  
  .card-subtitle {
    margin: 0 0 $spacing-4 0;
    font-size: $font-size-base;
    color: $gray-600;
  }
}

// ===== 卡片底部 =====
.card-footer {
  padding: $spacing-4 $spacing-6;
  border-top: 1px solid $gray-200;
  background: $gray-50;
  border-radius: 0 0 $border-radius-xl $border-radius-xl;
  
  &--flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  &--center {
    text-align: center;
  }
  
  &--right {
    text-align: right;
  }
}

// ===== 卡片图片 =====
.card-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  
  img {
    @include image-cover;
    transition: transform 0.3s ease;
  }
  
  &--top {
    border-radius: $border-radius-xl $border-radius-xl 0 0;
  }
  
  &--bottom {
    border-radius: 0 0 $border-radius-xl $border-radius-xl;
  }
  
  &--full {
    border-radius: $border-radius-xl;
  }
  
  // 高度变体
  &--sm {
    height: 150px;
  }
  
  &--md {
    height: 200px;
  }
  
  &--lg {
    height: 250px;
  }
  
  &--xl {
    height: 300px;
  }
}

// ===== 特殊卡片样式 =====

// 产品卡片
.card--product {
  position: relative;
  overflow: hidden;
  
  .card-image {
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  }
  
  .card-badge {
    position: absolute;
    top: $spacing-3;
    left: $spacing-3;
    @include badge($error-color, $white);
    z-index: 2;
  }
  
  .card-actions {
    position: absolute;
    top: $spacing-3;
    right: $spacing-3;
    display: flex;
    flex-direction: column;
    gap: $spacing-2;
    opacity: 0;
    transform: translateX(10px);
    transition: all 0.3s ease;
    z-index: 2;
  }
  
  &:hover {
    .card-image::after {
      opacity: 1;
    }
    
    .card-actions {
      opacity: 1;
      transform: translateX(0);
    }
    
    .card-image img {
      transform: scale(1.05);
    }
  }
}

// 服务卡片
.card--service {
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  
  .card-icon {
    width: 4rem;
    height: 4rem;
    margin: 0 auto $spacing-4;
    background: $primary-gradient;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: $white;
    font-size: $font-size-2xl;
  }
  
  &:hover {
    border-color: $primary-color;
    transform: translateY(-8px);
    box-shadow: $shadow-xl;
    
    .card-icon {
      transform: scale(1.1);
    }
  }
}

// 团队成员卡片
.card--team {
  text-align: center;
  
  .card-avatar {
    width: 5rem;
    height: 5rem;
    margin: 0 auto $spacing-4;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid $white;
    box-shadow: $shadow-md;
    
    img {
      @include image-cover;
    }
  }
  
  .card-name {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $primary-color;
    margin-bottom: $spacing-1;
  }
  
  .card-position {
    font-size: $font-size-sm;
    color: $gray-600;
    margin-bottom: $spacing-3;
  }
  
  .card-social {
    display: flex;
    justify-content: center;
    gap: $spacing-2;
    
    a {
      width: 2rem;
      height: 2rem;
      background: $gray-100;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: $gray-600;
      text-decoration: none;
      transition: all 0.3s ease;
      
      &:hover {
        background: $primary-color;
        color: $white;
        transform: translateY(-2px);
      }
    }
  }
}

// 统计卡片
.card--stat {
  text-align: center;
  background: $primary-gradient;
  color: $white;
  
  .card-number {
    font-size: $font-size-4xl;
    font-weight: $font-weight-bold;
    margin-bottom: $spacing-2;
  }
  
  .card-label {
    font-size: $font-size-base;
    opacity: 0.9;
  }
  
  .card-icon {
    position: absolute;
    top: $spacing-4;
    right: $spacing-4;
    font-size: $font-size-2xl;
    opacity: 0.3;
  }
}

// 玻璃态卡片
.card--glass {
  @include glass-effect(0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  &:hover {
    @include glass-effect(0.15);
    transform: translateY(-4px);
  }
}

// 渐变卡片
.card--gradient {
  &.card--primary {
    background: $primary-gradient;
    color: $white;
    border: none;
  }
  
  &.card--secondary {
    background: $gradient-warm;
    color: $white;
    border: none;
  }
  
  &.card--success {
    background: $gradient-success;
    color: $white;
    border: none;
  }
}

// ===== 卡片网格布局 =====
.card-grid {
  display: grid;
  gap: $spacing-6;
  
  &--1 {
    grid-template-columns: 1fr;
  }
  
  &--2 {
    grid-template-columns: repeat(2, 1fr);
    
    @include mobile {
      grid-template-columns: 1fr;
    }
  }
  
  &--3 {
    grid-template-columns: repeat(3, 1fr);
    
    @include tablet {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include mobile {
      grid-template-columns: 1fr;
    }
  }
  
  &--4 {
    grid-template-columns: repeat(4, 1fr);
    
    @include desktop {
      grid-template-columns: repeat(3, 1fr);
    }
    
    @include tablet {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include mobile {
      grid-template-columns: 1fr;
    }
  }
  
  &--auto {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

// ===== 响应式调整 =====
@include mobile {
  .card {
    margin-bottom: $spacing-4;
  }
  
  .card-header,
  .card-body,
  .card-footer {
    padding: $spacing-4;
  }
  
  .card--service .card-icon {
    width: 3rem;
    height: 3rem;
    font-size: $font-size-xl;
  }
  
  .card--team .card-avatar {
    width: 4rem;
    height: 4rem;
  }
  
  .card--stat .card-number {
    font-size: $font-size-3xl;
  }
}
