import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

import App from './App.vue'
import router from './router'
// import '@/assets/styles/main.scss'

// 导入指令
import { vScrollAnimation, vLazyLoad } from './directives'

// 导入主题初始化
import { initializeTheme } from './composables'

// 导入错误处理器
import { installErrorHandler } from './utils/errorHandler'

const app = createApp(App)

// 注册指令
app.directive('scroll-animation', vScrollAnimation)
app.directive('lazy-load', vLazyLoad)

// 使用插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 安装错误处理器
installErrorHandler(app, router)

// 初始化主题
initializeTheme()

app.mount('#app')
