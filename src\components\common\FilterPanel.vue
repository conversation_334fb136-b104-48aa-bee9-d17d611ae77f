<template>
  <div class="filter-panel" :class="{ 'filter-panel--collapsed': collapsed }">
    <!-- 筛选头部 -->
    <div class="filter-header">
      <h3 class="filter-title">筛选条件</h3>
      <div class="filter-actions">
        <button
          v-if="hasActiveFilters"
          class="clear-filters-button"
          @click="clearAllFilters"
        >
          清除全部
        </button>
        <button
          class="collapse-button"
          @click="toggleCollapse"
          :aria-label="collapsed ? '展开筛选' : '收起筛选'"
        >
          <i :class="collapsed ? 'icon-expand' : 'icon-collapse'"></i>
        </button>
      </div>
    </div>

    <!-- 筛选内容 -->
    <Transition name="filter-content">
      <div v-if="!collapsed" class="filter-content">
        <!-- 筛选组 -->
        <div
          v-for="group in filterGroups"
          :key="group.key"
          class="filter-group"
        >
          <h4 class="filter-group-title">{{ group.title }}</h4>
          
          <!-- 单选筛选 -->
          <div v-if="group.type === 'radio'" class="filter-options">
            <label
              v-for="option in group.options"
              :key="option.value"
              class="filter-option filter-option--radio"
            >
              <input
                type="radio"
                :name="group.key"
                :value="option.value"
                :checked="filters[group.key] === option.value"
                @change="handleFilterChange(group.key, option.value)"
              />
              <span class="option-label">{{ option.label }}</span>
              <span v-if="option.count !== undefined" class="option-count">
                ({{ option.count }})
              </span>
            </label>
          </div>
          
          <!-- 多选筛选 -->
          <div v-else-if="group.type === 'checkbox'" class="filter-options">
            <label
              v-for="option in group.options"
              :key="option.value"
              class="filter-option filter-option--checkbox"
            >
              <input
                type="checkbox"
                :value="option.value"
                :checked="isOptionSelected(group.key, option.value)"
                @change="handleCheckboxChange(group.key, option.value, $event)"
              />
              <span class="option-label">{{ option.label }}</span>
              <span v-if="option.count !== undefined" class="option-count">
                ({{ option.count }})
              </span>
            </label>
          </div>
          
          <!-- 范围筛选 -->
          <div v-else-if="group.type === 'range'" class="filter-range">
            <div class="range-inputs">
              <input
                type="number"
                :placeholder="group.minPlaceholder || '最小值'"
                :value="getRangeValue(group.key, 'min')"
                @input="handleRangeChange(group.key, 'min', $event)"
                class="range-input"
              />
              <span class="range-separator">-</span>
              <input
                type="number"
                :placeholder="group.maxPlaceholder || '最大值'"
                :value="getRangeValue(group.key, 'max')"
                @input="handleRangeChange(group.key, 'max', $event)"
                class="range-input"
              />
            </div>
          </div>
          
          <!-- 下拉选择筛选 -->
          <div v-else-if="group.type === 'select'" class="filter-select">
            <select
              :value="filters[group.key] || ''"
              @change="handleFilterChange(group.key, ($event.target as HTMLSelectElement).value)"
              class="select-input"
            >
              <option value="">{{ group.placeholder || '请选择' }}</option>
              <option
                v-for="option in group.options"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
                <span v-if="option.count !== undefined">({{ option.count }})</span>
              </option>
            </select>
          </div>
        </div>
      </div>
    </Transition>

    <!-- 已选筛选标签 -->
    <div v-if="activeFilterTags.length > 0" class="active-filters">
      <div class="active-filters-title">已选筛选：</div>
      <div class="filter-tags">
        <span
          v-for="tag in activeFilterTags"
          :key="tag.key"
          class="filter-tag"
        >
          {{ tag.label }}
          <button
            class="remove-tag-button"
            @click="removeFilter(tag.groupKey, tag.value)"
            :aria-label="`移除 ${tag.label} 筛选`"
          >
            <i class="icon-close"></i>
          </button>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { FilterOption } from '@/types'

interface FilterGroup {
  key: string
  title: string
  type: 'radio' | 'checkbox' | 'range' | 'select'
  options?: FilterOption[]
  placeholder?: string
  minPlaceholder?: string
  maxPlaceholder?: string
}

interface FilterTag {
  key: string
  groupKey: string
  label: string
  value: any
}

interface Props {
  filterGroups: FilterGroup[]
  modelValue: Record<string, any>
  collapsed?: boolean
}

interface Emits {
  (e: 'update:modelValue', filters: Record<string, any>): void
  (e: 'change', filters: Record<string, any>): void
  (e: 'clear'): void
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const collapsed = ref(props.collapsed)

// 计算属性
const filters = computed({
  get: () => props.modelValue,
  set: (value: Record<string, any>) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
})

const hasActiveFilters = computed(() => {
  return Object.values(filters.value).some(value => {
    if (Array.isArray(value)) {
      return value.length > 0
    }
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(v => v !== null && v !== undefined && v !== '')
    }
    return value !== null && value !== undefined && value !== ''
  })
})

const activeFilterTags = computed(() => {
  const tags: FilterTag[] = []
  
  props.filterGroups.forEach(group => {
    const value = filters.value[group.key]
    
    if (group.type === 'radio' || group.type === 'select') {
      if (value) {
        const option = group.options?.find(opt => opt.value === value)
        if (option) {
          tags.push({
            key: `${group.key}-${value}`,
            groupKey: group.key,
            label: option.label,
            value: value
          })
        }
      }
    } else if (group.type === 'checkbox') {
      if (Array.isArray(value) && value.length > 0) {
        value.forEach(val => {
          const option = group.options?.find(opt => opt.value === val)
          if (option) {
            tags.push({
              key: `${group.key}-${val}`,
              groupKey: group.key,
              label: option.label,
              value: val
            })
          }
        })
      }
    } else if (group.type === 'range') {
      if (value && (value.min !== undefined || value.max !== undefined)) {
        const parts = []
        if (value.min !== undefined && value.min !== '') parts.push(`≥${value.min}`)
        if (value.max !== undefined && value.max !== '') parts.push(`≤${value.max}`)
        if (parts.length > 0) {
          tags.push({
            key: `${group.key}-range`,
            groupKey: group.key,
            label: `${group.title}: ${parts.join(', ')}`,
            value: value
          })
        }
      }
    }
  })
  
  return tags
})

// 方法
const toggleCollapse = () => {
  collapsed.value = !collapsed.value
}

const handleFilterChange = (key: string, value: any) => {
  const newFilters = { ...filters.value }
  if (value === '' || value === null) {
    delete newFilters[key]
  } else {
    newFilters[key] = value
  }
  filters.value = newFilters
}

const handleCheckboxChange = (key: string, value: any, event: Event) => {
  const target = event.target as HTMLInputElement
  const currentValues = filters.value[key] || []
  let newValues: any[]
  
  if (target.checked) {
    newValues = [...currentValues, value]
  } else {
    newValues = currentValues.filter((v: any) => v !== value)
  }
  
  handleFilterChange(key, newValues.length > 0 ? newValues : null)
}

const handleRangeChange = (key: string, type: 'min' | 'max', event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value === '' ? undefined : Number(target.value)
  const currentRange = filters.value[key] || {}
  
  const newRange = {
    ...currentRange,
    [type]: value
  }
  
  // 如果 min 和 max 都为空，则删除整个筛选
  if (newRange.min === undefined && newRange.max === undefined) {
    handleFilterChange(key, null)
  } else {
    handleFilterChange(key, newRange)
  }
}

const isOptionSelected = (groupKey: string, value: any): boolean => {
  const groupValue = filters.value[groupKey]
  if (Array.isArray(groupValue)) {
    return groupValue.includes(value)
  }
  return false
}

const getRangeValue = (groupKey: string, type: 'min' | 'max'): string => {
  const range = filters.value[groupKey]
  if (range && range[type] !== undefined) {
    return range[type].toString()
  }
  return ''
}

const removeFilter = (groupKey: string, value: any) => {
  const group = props.filterGroups.find(g => g.key === groupKey)
  if (!group) return
  
  if (group.type === 'checkbox') {
    const currentValues = filters.value[groupKey] || []
    const newValues = currentValues.filter((v: any) => v !== value)
    handleFilterChange(groupKey, newValues.length > 0 ? newValues : null)
  } else if (group.type === 'range') {
    handleFilterChange(groupKey, null)
  } else {
    handleFilterChange(groupKey, null)
  }
}

const clearAllFilters = () => {
  filters.value = {}
  emit('clear')
}

// 监听器
watch(() => props.collapsed, (newValue) => {
  collapsed.value = newValue
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.filter-panel {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  overflow: hidden;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
}

.filter-title {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.filter-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.clear-filters-button {
  font-size: 0.875rem;
  color: #4299e1;
  background: none;
  border: none;
  cursor: pointer;
  
  &:hover {
    color: #3182ce;
    text-decoration: underline;
  }
}

.collapse-button {
  width: 1.5rem;
  height: 1.5rem;
  background: none;
  border: none;
  color: #718096;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    color: #4a5568;
  }
}

.filter-content {
  padding: 1.5rem;
}

.filter-group {
  margin-bottom: 1.5rem;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.filter-group-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 0.75rem;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  
  input[type="radio"],
  input[type="checkbox"] {
    margin: 0;
  }
  
  .option-label {
    flex: 1;
    font-size: 0.875rem;
    color: #4a5568;
  }
  
  .option-count {
    font-size: 0.75rem;
    color: #a0aec0;
  }
  
  &:hover .option-label {
    color: #2d3748;
  }
}

.filter-range {
  .range-inputs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .range-input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    
    &:focus {
      outline: none;
      border-color: #4299e1;
      box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    }
  }
  
  .range-separator {
    color: #a0aec0;
    font-weight: 500;
  }
}

.filter-select {
  .select-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    background: white;
    
    &:focus {
      outline: none;
      border-color: #4299e1;
      box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    }
  }
}

.active-filters {
  padding: 1rem 1.5rem;
  background: #f7fafc;
  border-top: 1px solid #e2e8f0;
}

.active-filters-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 0.75rem;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: #4299e1;
  color: white;
  border-radius: 1rem;
  font-size: 0.75rem;
}

.remove-tag-button {
  width: 1rem;
  height: 1rem;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}

// 动画
.filter-content-enter-active,
.filter-content-leave-active {
  transition: all 0.3s ease-in-out;
}

.filter-content-enter-from,
.filter-content-leave-to {
  opacity: 0;
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
}

// 图标
.icon-expand::before { content: '▼'; }
.icon-collapse::before { content: '▲'; }
.icon-close::before { content: '✕'; }

// 响应式
@media (max-width: 768px) {
  .filter-header {
    padding: 0.75rem 1rem;
  }
  
  .filter-content {
    padding: 1rem;
  }
  
  .active-filters {
    padding: 0.75rem 1rem;
  }
  
  .filter-tags {
    gap: 0.375rem;
  }
  
  .filter-tag {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
}
</style>
